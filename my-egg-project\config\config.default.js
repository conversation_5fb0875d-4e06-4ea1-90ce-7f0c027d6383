/* eslint valid-jsdoc: "off" */

/**
 * @param {Egg.EggAppInfo} appInfo app info
 */
module.exports = (appInfo) => {
  /**
   * built-in config
   * @type {Egg.EggAppConfig}
   **/
  const config = (exports = {});

  // use for cookie sign key, should change to your own and keep security
  config.keys = appInfo.name + "_1754149263311_298";

  // add your middleware config here
  config.middleware = [];

  // add your user config here
  const userConfig = {
    // myAppName: 'egg',
  };

  // socket.io config
  config.io = {
    init: {
      cors: {
        origin: "*",
        methods: ["GET", "POST"],
      },
    }, // passed to engine.io
    namespace: {
      "/": {
        connectionMiddleware: ["connection"],
        packetMiddleware: [],
      },
    },
  };

  // security config for cross-origin
  config.security = {
    csrf: {
      enable: false,
    },
  };

  // cors config
  config.cors = {
    origin: "*",
    allowMethods: "GET,HEAD,PUT,POST,DELETE,PATCH",
  };

  return {
    ...config,
    ...userConfig,
  };
};

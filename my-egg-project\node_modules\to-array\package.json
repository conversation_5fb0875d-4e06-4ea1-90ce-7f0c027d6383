{"name": "to-array", "version": "0.1.4", "description": "Turn an array like into an array", "keywords": [], "author": "Raynos <<EMAIL>>", "repository": "git://github.com/Raynos/to-array.git", "main": "index", "homepage": "https://github.com/Raynos/to-array", "contributors": [{"name": "<PERSON>"}], "bugs": {"url": "https://github.com/Raynos/to-array/issues", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"tap": "~0.3.1"}, "licenses": [{"type": "MIT", "url": "http://github.com/Raynos/to-array/raw/master/LICENSE"}], "scripts": {"test": "tap --stderr --tap ./test"}, "component": {"scripts": {"to-array/index.js": "index.js"}}}
[{"name": "Process Start", "start": 1754150689949, "end": 1754150690634, "duration": 685, "pid": 23368, "index": 0}, {"name": "Application Start", "start": 1754150690635, "end": 1754150690848, "duration": 213, "pid": 23368, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1754150690645, "end": 1754150690661, "duration": 16, "pid": 23368, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1754150690661, "end": 1754150690681, "duration": 20, "pid": 23368, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1754150690662, "end": 1754150690662, "duration": 0, "pid": 23368, "index": 4}, {"name": "Require(1) node_modules/egg-session/config/config.default.js", "start": 1754150690663, "end": 1754150690663, "duration": 0, "pid": 23368, "index": 5}, {"name": "Require(2) node_modules/egg-security/config/config.default.js", "start": 1754150690664, "end": 1754150690664, "duration": 0, "pid": 23368, "index": 6}, {"name": "Require(3) node_modules/egg-jsonp/config/config.default.js", "start": 1754150690665, "end": 1754150690665, "duration": 0, "pid": 23368, "index": 7}, {"name": "Require(4) node_modules/egg-onerror/config/config.default.js", "start": 1754150690666, "end": 1754150690666, "duration": 0, "pid": 23368, "index": 8}, {"name": "Require(5) node_modules/egg-i18n/config/config.default.js", "start": 1754150690666, "end": 1754150690667, "duration": 1, "pid": 23368, "index": 9}, {"name": "Require(6) node_modules/egg-watcher/config/config.default.js", "start": 1754150690667, "end": 1754150690668, "duration": 1, "pid": 23368, "index": 10}, {"name": "Require(7) node_modules/egg-schedule/config/config.default.js", "start": 1754150690668, "end": 1754150690669, "duration": 1, "pid": 23368, "index": 11}, {"name": "Require(8) node_modules/egg-multipart/config/config.default.js", "start": 1754150690669, "end": 1754150690669, "duration": 0, "pid": 23368, "index": 12}, {"name": "Require(9) node_modules/egg-development/config/config.default.js", "start": 1754150690670, "end": 1754150690670, "duration": 0, "pid": 23368, "index": 13}, {"name": "Require(10) node_modules/egg-logrotator/config/config.default.js", "start": 1754150690671, "end": 1754150690671, "duration": 0, "pid": 23368, "index": 14}, {"name": "Require(11) node_modules/egg-static/config/config.default.js", "start": 1754150690672, "end": 1754150690672, "duration": 0, "pid": 23368, "index": 15}, {"name": "Require(12) node_modules/egg-view/config/config.default.js", "start": 1754150690672, "end": 1754150690673, "duration": 1, "pid": 23368, "index": 16}, {"name": "Require(13) node_modules/egg-socket.io/config/config.default.js", "start": 1754150690673, "end": 1754150690674, "duration": 1, "pid": 23368, "index": 17}, {"name": "Require(14) node_modules/egg/config/config.default.js", "start": 1754150690674, "end": 1754150690674, "duration": 0, "pid": 23368, "index": 18}, {"name": "Require(15) config/config.default.js", "start": 1754150690676, "end": 1754150690676, "duration": 0, "pid": 23368, "index": 19}, {"name": "Require(16) node_modules/egg-security/config/config.local.js", "start": 1754150690676, "end": 1754150690677, "duration": 1, "pid": 23368, "index": 20}, {"name": "Require(17) node_modules/egg-watcher/config/config.local.js", "start": 1754150690678, "end": 1754150690678, "duration": 0, "pid": 23368, "index": 21}, {"name": "Require(18) node_modules/egg-view/config/config.local.js", "start": 1754150690680, "end": 1754150690680, "duration": 0, "pid": 23368, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.local.js", "start": 1754150690681, "end": 1754150690681, "duration": 0, "pid": 23368, "index": 23}, {"name": "Load extend/agent.js", "start": 1754150690682, "end": 1754150690724, "duration": 42, "pid": 23368, "index": 24}, {"name": "Require(20) node_modules/egg-security/app/extend/agent.js", "start": 1754150690683, "end": 1754150690684, "duration": 1, "pid": 23368, "index": 25}, {"name": "Require(21) node_modules/egg-schedule/app/extend/agent.js", "start": 1754150690685, "end": 1754150690716, "duration": 31, "pid": 23368, "index": 26}, {"name": "Require(22) node_modules/egg-logrotator/app/extend/agent.js", "start": 1754150690717, "end": 1754150690718, "duration": 1, "pid": 23368, "index": 27}, {"name": "Load extend/context.js", "start": 1754150690724, "end": 1754150690767, "duration": 43, "pid": 23368, "index": 28}, {"name": "Require(23) node_modules/egg-security/app/extend/context.js", "start": 1754150690725, "end": 1754150690735, "duration": 10, "pid": 23368, "index": 29}, {"name": "Require(24) node_modules/egg-jsonp/app/extend/context.js", "start": 1754150690736, "end": 1754150690737, "duration": 1, "pid": 23368, "index": 30}, {"name": "Require(25) node_modules/egg-i18n/app/extend/context.js", "start": 1754150690738, "end": 1754150690739, "duration": 1, "pid": 23368, "index": 31}, {"name": "Require(26) node_modules/egg-multipart/app/extend/context.js", "start": 1754150690740, "end": 1754150690759, "duration": 19, "pid": 23368, "index": 32}, {"name": "Require(27) node_modules/egg-view/app/extend/context.js", "start": 1754150690760, "end": 1754150690761, "duration": 1, "pid": 23368, "index": 33}, {"name": "Require(28) node_modules/egg/app/extend/context.js", "start": 1754150690762, "end": 1754150690764, "duration": 2, "pid": 23368, "index": 34}, {"name": "Load agent.js", "start": 1754150690767, "end": 1754150690793, "duration": 26, "pid": 23368, "index": 35}, {"name": "Require(29) node_modules/egg-security/agent.js", "start": 1754150690768, "end": 1754150690768, "duration": 0, "pid": 23368, "index": 36}, {"name": "Require(30) node_modules/egg-onerror/agent.js", "start": 1754150690769, "end": 1754150690769, "duration": 0, "pid": 23368, "index": 37}, {"name": "Require(31) node_modules/egg-watcher/agent.js", "start": 1754150690770, "end": 1754150690773, "duration": 3, "pid": 23368, "index": 38}, {"name": "Require(32) node_modules/egg-schedule/agent.js", "start": 1754150690773, "end": 1754150690774, "duration": 1, "pid": 23368, "index": 39}, {"name": "Require(33) node_modules/egg-development/agent.js", "start": 1754150690775, "end": 1754150690791, "duration": 16, "pid": 23368, "index": 40}, {"name": "Require(34) node_modules/egg-logrotator/agent.js", "start": 1754150690791, "end": 1754150690791, "duration": 0, "pid": 23368, "index": 41}, {"name": "Require(35) node_modules/egg/agent.js", "start": 1754150690793, "end": 1754150690793, "duration": 0, "pid": 23368, "index": 42}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1754150690798, "end": 1754150690844, "duration": 46, "pid": 23368, "index": 43}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1754150690799, "end": 1754150690832, "duration": 33, "pid": 23368, "index": 44}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1754150690799, "end": 1754150690848, "duration": 49, "pid": 23368, "index": 45}]
{"name": "engine.io-parser", "description": "Parser for the client for the realtime Engine", "license": "MIT", "version": "2.2.1", "main": "lib/index.js", "homepage": "https://github.com/socketio/engine.io-parser", "devDependencies": {"benchmark": "^2.1.4", "expect.js": "0.3.1", "mocha": "^5.2.0", "socket.io-browsers": "^1.0.2", "zuul": "3.11.1", "zuul-ngrok": "4.0.0"}, "dependencies": {"after": "0.8.2", "arraybuffer.slice": "~0.0.7", "base64-arraybuffer": "0.1.4", "blob": "0.0.5", "has-binary2": "~1.0.2"}, "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "**************:socketio/engine.io-parser.git"}, "files": ["lib/"], "browser": "./lib/browser.js"}
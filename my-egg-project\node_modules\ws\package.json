{"name": "ws", "version": "7.5.10", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "homepage": "https://github.com/websockets/ws", "bugs": "https://github.com/websockets/ws/issues", "repository": "websockets/ws", "author": "<PERSON><PERSON> <PERSON> <<EMAIL>> (http://2x.io)", "license": "MIT", "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "files": ["browser.js", "index.js", "lib/*.js"], "scripts": {"test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js", "lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\""}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "devDependencies": {"benchmark": "^2.1.4", "bufferutil": "^4.0.1", "eslint": "^7.2.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0", "mocha": "^7.0.0", "nyc": "^15.0.0", "prettier": "^2.0.5", "utf-8-validate": "^5.0.2"}}
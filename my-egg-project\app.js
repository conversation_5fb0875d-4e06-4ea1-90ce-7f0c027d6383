"use strict";

class AppBootHook {
  constructor(app) {
    this.app = app;
  }

  configWillLoad() {
    // 应用配置即将加载，这是最后动态修改配置的时机
  }

  async didLoad() {
    // 所有的配置已经加载完毕
    // 可以用来加载应用自定义的文件，启动自定义的服务
  }

  async willReady() {
    // 所有的插件都已启动完毕，但是应用整体还未 ready
    // 可以做一些数据初始化等操作，这些操作成功才会启动应用
  }

  async didReady() {
    // 应用已经启动完毕
    console.log("🚀 Video annotation server is ready!");
    console.log(
      "📡 WebSocket server listening on port:",
      this.app.config.cluster.listen.port || 7001
    );
  }

  async serverDidReady() {
    // http / https server 已启动，开始接受外部请求
    // 此时可以从 app.server 拿到 server 的实例
  }
}

module.exports = AppBootHook;

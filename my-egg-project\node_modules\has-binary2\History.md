<a name="1.0.3"></a>
## [1.0.3](https://github.com/darrachequesne/has-binary/compare/1.0.2...1.0.3) (2018-05-14)


### Bug Fixes

* avoid use of global ([#4](https://github.com/darrachequesne/has-binary/issues/4)) ([91aa21e](https://github.com/darrachequesne/has-binary/commit/91aa21e))



<a name="1.0.2"></a>
## [1.0.2](https://github.com/darrachequesne/has-binary/compare/1.0.1...1.0.2) (2017-04-27)


### Bug Fixes

* Fix Blob detection for iOS 8/9 ([2a7b25c](https://github.com/darrachequesne/has-binary/commit/2a7b25c))



<a name="1.0.1"></a>
## [1.0.1](https://github.com/darrachequesne/has-binary/compare/1.0.0...1.0.1) (2017-04-05)



<a name="1.0.0"></a>
# [1.0.0](https://github.com/darrachequesne/has-binary/compare/0.1.7...1.0.0) (2017-04-05)


### Bug Fixes

* do not call toJSON more than once ([#7](https://github.com/darrachequesne/has-binary/issues/7)) ([27165d2](https://github.com/darrachequesne/has-binary/commit/27165d2))
* Ensure globals are functions before running `instanceof` checks against them. ([#4](https://github.com/darrachequesne/has-binary/issues/4)) ([f9be9b3](https://github.com/darrachequesne/has-binary/commit/f9be9b3))
* fix the case when toJSON() returns a Buffer  ([#6](https://github.com/darrachequesne/has-binary/issues/6)) ([518747d](https://github.com/darrachequesne/has-binary/commit/518747d))


### Performance Improvements

* Performance improvements ([#3](https://github.com/darrachequesne/has-binary/issues/3)) ([3e88e81](https://github.com/darrachequesne/has-binary/commit/3e88e81))



<a name="0.1.7"></a>
## [0.1.7](https://github.com/darrachequesne/has-binary/compare/0.1.6...0.1.7) (2015-11-19)



<a name="0.1.6"></a>
## [0.1.6](https://github.com/darrachequesne/has-binary/compare/0.1.5...0.1.6) (2015-01-24)



<a name="0.1.5"></a>
## 0.1.5 (2014-09-04)




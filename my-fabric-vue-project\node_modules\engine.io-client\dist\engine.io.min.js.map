{"version": 3, "file": "engine.io.min.js", "sources": ["../../engine.io-parser/build/esm/commons.js", "../../engine.io-parser/build/esm/encodePacket.browser.js", "../../engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../../engine.io-parser/build/esm/index.js", "../../engine.io-parser/build/esm/decodePacket.browser.js", "../../socket.io-component-emitter/lib/esm/index.js", "../build/esm/globals.js", "../build/esm/util.js", "../build/esm/transport.js", "../build/esm/contrib/parseqs.js", "../build/esm/transports/polling.js", "../build/esm/contrib/has-cors.js", "../build/esm/transports/polling-xhr.js", "../build/esm/transports/websocket.js", "../build/esm/transports/webtransport.js", "../build/esm/transports/index.js", "../build/esm/contrib/parseuri.js", "../build/esm/socket.js", "../build/esm/browser-entrypoint.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach((key) => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data.arrayBuffer().then(toArray).then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, (encoded) => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n", "// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n", "import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET, } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, (encodedPacket) => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, (encodedPacket) => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        },\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* State.READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* State.READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* State.READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* State.READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* State.READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* State.READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else if (state === 2 /* State.READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* State.READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        },\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload, };\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE, } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType),\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType),\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1),\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type],\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            if (data instanceof Blob) {\n                // from WebSocket + binaryType \"blob\"\n                return data;\n            }\n            else {\n                // from HTTP long-polling or WebTransport\n                return new Blob([data]);\n            }\n        case \"arraybuffer\":\n        default:\n            if (data instanceof ArrayBuffer) {\n                // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n    }\n};\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\nexport const defaultBinaryType = \"arraybuffer\";\nexport function createCookieJar() { }\n", "import { globalThisShim as globalThis } from \"./globals.node.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n/**\n * Generates a random 8-characters string.\n */\nexport function randomString() {\n    return (Date.now().toString(36).substring(3) +\n        Math.random().toString(36).substring(2, 5));\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nexport class TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n        this.supportsBinary = !opts.forceBase64;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n    createUri(schema, query = {}) {\n        return (schema +\n            \"://\" +\n            this._hostname() +\n            this._port() +\n            this.opts.path +\n            this._query(query));\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port &&\n            ((this.opts.secure && Number(this.opts.port !== 443)) ||\n                (!this.opts.secure && Number(this.opts.port) !== 80))) {\n            return \":\" + this.opts.port;\n        }\n        else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = encode(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "import { Transport } from \"../transport.js\";\nimport { randomString } from \"../util.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nexport class Polling extends Transport {\n    constructor() {\n        super(...arguments);\n        this._polling = false;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this._poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this._polling || !this.writable) {\n            let total = 0;\n            if (this._polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    _poll() {\n        this._polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this._polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this._poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "import { Polling } from \"./polling.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globals.node.js\";\nimport { hasCORS } from \"../contrib/has-cors.js\";\nfunction empty() { }\nexport class BaseXHR extends Polling {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(createRequest, uri, opts) {\n        super();\n        this.createRequest = createRequest;\n        installTimerFunctions(this, opts);\n        this._opts = opts;\n        this._method = opts.method || \"GET\";\n        this._uri = uri;\n        this._data = undefined !== opts.data ? opts.data : null;\n        this._create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    _create() {\n        var _a;\n        const opts = pick(this._opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this._opts.xd;\n        const xhr = (this._xhr = this.createRequest(opts));\n        try {\n            xhr.open(this._method, this._uri, true);\n            try {\n                if (this._opts.extraHeaders) {\n                    // @ts-ignore\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this._opts.extraHeaders) {\n                        if (this._opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this._opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this._method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this._opts.withCredentials;\n            }\n            if (this._opts.requestTimeout) {\n                xhr.timeout = this._opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(\n                    // @ts-ignore\n                    xhr.getResponseHeader(\"set-cookie\"));\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this._onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this._onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this._data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this._onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this._index = Request.requestsCount++;\n            Request.requests[this._index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    _onError(err) {\n        this.emitReserved(\"error\", err, this._xhr);\n        this._cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    _cleanup(fromError) {\n        if (\"undefined\" === typeof this._xhr || null === this._xhr) {\n            return;\n        }\n        this._xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this._xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this._index];\n        }\n        this._xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    _onLoad() {\n        const data = this._xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this._cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this._cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\nconst hasXHR2 = (function () {\n    const xhr = newRequest({\n        xdomain: false,\n    });\n    return xhr && xhr.responseType !== null;\n})();\n/**\n * HTTP long-polling based on the built-in `XMLHttpRequest` object.\n *\n * Usage: browser\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */\nexport class XHR extends BaseXHR {\n    constructor(opts) {\n        super(opts);\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd }, this.opts);\n        return new Request(newRequest, this.uri(), opts);\n    }\n}\nfunction newRequest(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { pick, randomString } from \"../util.js\";\nimport { encodePacket } from \"engine.io-parser\";\nimport { globalThisShim as globalThis, nextTick } from \"../globals.node.js\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class BaseWS extends Transport {\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws = this.createSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    this.doWrite(packet, data);\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.onerror = () => { };\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\nconst WebSocketCtor = globalThis.WebSocket || globalThis.MozWebSocket;\n/**\n * WebSocket transport based on the built-in `WebSocket` object.\n *\n * Usage: browser, Node.js (since v21), Deno, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n * @see https://nodejs.org/api/globals.html#websocket\n */\nexport class WS extends BaseWS {\n    createSocket(uri, protocols, opts) {\n        return !isReactNative\n            ? protocols\n                ? new WebSocketCtor(uri, protocols)\n                : new WebSocketCtor(uri)\n            : new WebSocketCtor(uri, protocols, opts);\n    }\n    doWrite(_packet, data) {\n        this.ws.send(data);\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { nextTick } from \"../globals.node.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\n/**\n * WebTransport transport based on the built-in `WebTransport` object.\n *\n * Usage: browser, Node.js (with the `@fails-components/webtransport` package)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebTransport\n * @see https://caniuse.com/webtransport\n */\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        try {\n            // @ts-ignore\n            this._transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this._transport.closed\n            .then(() => {\n            this.onClose();\n        })\n            .catch((err) => {\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this._transport.ready.then(() => {\n            this._transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this._writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            return;\n                        }\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this._writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this._writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this._transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n", "import { XHR } from \"./polling-xhr.node.js\";\nimport { WS } from \"./websocket.node.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: XHR,\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    if (str.length > 8000) {\n        throw \"URI too long\";\n    }\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports as DEFAULT_TRANSPORTS } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { createCookieJar, defaultBinaryType, nextTick, } from \"./globals.node.js\";\nconst withEventListeners = typeof addEventListener === \"function\" &&\n    typeof removeEventListener === \"function\";\nconst OFFLINE_EVENT_LISTENERS = [];\nif (withEventListeners) {\n    // within a ServiceWorker, any event handler for the 'offline' event must be added on the initial evaluation of the\n    // script, so we create one single event listener here which will forward the event to the socket instances\n    addEventListener(\"offline\", () => {\n        OFFLINE_EVENT_LISTENERS.forEach((listener) => listener());\n    }, false);\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes without upgrade mechanism, which means that it will keep the first low-level transport that\n * successfully establishes the connection.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithoutUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithoutUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithUpgrade\n * @see Socket\n */\nexport class SocketWithoutUpgrade extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        this._prevBufferLen = 0;\n        this._pingInterval = -1;\n        this._pingTimeout = -1;\n        this._maxPayload = -1;\n        /**\n         * The expiration timestamp of the {@link _pingTimeoutTimer} object is tracked, in case the timer is throttled and the\n         * callback is not fired on time. This can happen for example when a laptop is suspended or when a phone is locked.\n         */\n        this._pingTimeoutTime = Infinity;\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            const parsedUri = parse(uri);\n            opts.hostname = parsedUri.host;\n            opts.secure =\n                parsedUri.protocol === \"https\" || parsedUri.protocol === \"wss\";\n            opts.port = parsedUri.port;\n            if (parsedUri.query)\n                opts.query = parsedUri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = [];\n        this._transportsByName = {};\n        opts.transports.forEach((t) => {\n            const transportName = t.prototype.name;\n            this.transports.push(transportName);\n            this._transportsByName[transportName] = t;\n        });\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        if (withEventListeners) {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this._beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this._offlineEventListener = () => {\n                    this._onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                OFFLINE_EVENT_LISTENERS.push(this._offlineEventListener);\n            }\n        }\n        if (this.opts.withCredentials) {\n            this._cookieJar = createCookieJar();\n        }\n        this._open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        return new this._transportsByName[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    _open() {\n        if (this.transports.length === 0) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        const transportName = this.opts.rememberUpgrade &&\n            SocketWithoutUpgrade.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1\n            ? \"websocket\"\n            : this.transports[0];\n        this.readyState = \"opening\";\n        const transport = this.createTransport(transportName);\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this._onDrain.bind(this))\n            .on(\"packet\", this._onPacket.bind(this))\n            .on(\"error\", this._onError.bind(this))\n            .on(\"close\", (reason) => this._onClose(\"transport close\", reason));\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        SocketWithoutUpgrade.priorWebsocketSuccess =\n            \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    _onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this._sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    this._resetPingTimeout();\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this._onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this._pingInterval = data.pingInterval;\n        this._pingTimeout = data.pingTimeout;\n        this._maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this._resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    _resetPingTimeout() {\n        this.clearTimeoutFn(this._pingTimeoutTimer);\n        const delay = this._pingInterval + this._pingTimeout;\n        this._pingTimeoutTime = Date.now() + delay;\n        this._pingTimeoutTimer = this.setTimeoutFn(() => {\n            this._onClose(\"ping timeout\");\n        }, delay);\n        if (this.opts.autoUnref) {\n            this._pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    _onDrain() {\n        this.writeBuffer.splice(0, this._prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this._prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this._getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this._prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    _getWritablePackets() {\n        const shouldCheckPayloadSize = this._maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this._maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Checks whether the heartbeat timer has expired but the socket has not yet been notified.\n     *\n     * Note: this method is private for now because it does not really fit the WebSocket API, but if we put it in the\n     * `write()` method then the message would not be buffered by the Socket.IO client.\n     *\n     * @return {boolean}\n     * @private\n     */\n    /* private */ _hasPingExpired() {\n        if (!this._pingTimeoutTime)\n            return true;\n        const hasExpired = Date.now() > this._pingTimeoutTime;\n        if (hasExpired) {\n            this._pingTimeoutTime = 0;\n            nextTick(() => {\n                this._onClose(\"ping timeout\");\n            }, this.setTimeoutFn);\n        }\n        return hasExpired;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a message. Alias of {@link Socket#write}.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    send(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    _sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this._onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    _onError(err) {\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        if (this.opts.tryAllTransports &&\n            this.transports.length > 1 &&\n            this.readyState === \"opening\") {\n            this.transports.shift();\n            return this._open();\n        }\n        this.emitReserved(\"error\", err);\n        this._onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    _onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this._pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (withEventListeners) {\n                if (this._beforeunloadEventListener) {\n                    removeEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n                }\n                if (this._offlineEventListener) {\n                    const i = OFFLINE_EVENT_LISTENERS.indexOf(this._offlineEventListener);\n                    if (i !== -1) {\n                        OFFLINE_EVENT_LISTENERS.splice(i, 1);\n                    }\n                }\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this._prevBufferLen = 0;\n        }\n    }\n}\nSocketWithoutUpgrade.protocol = protocol;\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see Socket\n */\nexport class SocketWithUpgrade extends SocketWithoutUpgrade {\n    constructor() {\n        super(...arguments);\n        this._upgrades = [];\n    }\n    onOpen() {\n        super.onOpen();\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            for (let i = 0; i < this._upgrades.length; i++) {\n                this._probe(this._upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    _probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    SocketWithoutUpgrade.priorWebsocketSuccess =\n                        \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this._upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    onHandshake(data) {\n        this._upgrades = this._filterUpgrades(data.upgrades);\n        super.onHandshake(data);\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    _filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        for (let i = 0; i < upgrades.length; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * @example\n * import { Socket } from \"engine.io-client\";\n *\n * const socket = new Socket();\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see SocketWithUpgrade\n */\nexport class Socket extends SocketWithUpgrade {\n    constructor(uri, opts = {}) {\n        const o = typeof uri === \"object\" ? uri : opts;\n        if (!o.transports ||\n            (o.transports && typeof o.transports[0] === \"string\")) {\n            o.transports = (o.transports || [\"polling\", \"websocket\", \"webtransport\"])\n                .map((transportName) => DEFAULT_TRANSPORTS[transportName])\n                .filter((t) => !!t);\n        }\n        super(uri, o);\n    }\n}\n", "import { Socket } from \"./socket.js\";\nexport default (uri, opts) => new Socket(uri, opts);\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "TEXT_ENCODER", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "encodePacket", "_ref", "supportsBinary", "callback", "encodeBlobAsBase64", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "toArray", "Uint8Array", "byteOffset", "byteLength", "chars", "lookup", "i", "charCodeAt", "TEXT_DECODER", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "length", "decoded", "base64", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "len", "p", "arraybuffer", "bytes", "decode", "SEPARATOR", "String", "fromCharCode", "createPacketEncoderStream", "TransformStream", "transform", "packet", "controller", "arrayBuffer", "then", "encoded", "TextEncoder", "encode", "encodePacketToBinary", "header", "payloadLength", "DataView", "setUint8", "view", "setUint16", "setBigUint64", "BigInt", "enqueue", "totalLength", "chunks", "reduce", "acc", "chunk", "concatChunks", "size", "shift", "j", "slice", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "this", "_callbacks", "push", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "cb", "callbacks", "splice", "emit", "args", "Array", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "nextTick", "Promise", "resolve", "setTimeoutFn", "globalThisShim", "self", "window", "Function", "pick", "_len", "attr", "_key", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "globalThis", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "bind", "clearTimeoutFn", "randomString", "Date", "now", "Math", "random", "TransportError", "_Error", "reason", "description", "context", "_this", "_inherits<PERSON><PERSON>e", "_wrapNativeSuper", "Error", "Transport", "_Emitter", "_this2", "writable", "query", "socket", "forceBase64", "_proto", "onError", "open", "readyState", "doOpen", "close", "doClose", "onClose", "send", "packets", "write", "onOpen", "onData", "onPacket", "details", "pause", "onPause", "createUri", "schema", "undefined", "_hostname", "_port", "path", "_query", "hostname", "indexOf", "port", "secure", "Number", "<PERSON><PERSON><PERSON><PERSON>", "str", "encodeURIComponent", "Polling", "_Transport", "_polling", "_poll", "total", "doPoll", "_this3", "encodedPayload", "encodedPackets", "decodedPacket", "decodePayload", "_this4", "_this5", "count", "join", "encodePayload", "doWrite", "uri", "timestampRequests", "timestampParam", "sid", "b64", "_createClass", "get", "value", "XMLHttpRequest", "err", "hasCORS", "empty", "BaseXHR", "_Polling", "location", "isSSL", "protocol", "xd", "req", "request", "method", "xhrStatus", "pollXhr", "Request", "createRequest", "_opts", "_method", "_uri", "_data", "_create", "_proto2", "_a", "xdomain", "xhr", "_xhr", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "e", "cookieJar", "addCookies", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "parseCookies", "getResponseHeader", "status", "_onLoad", "_onError", "document", "_index", "requestsCount", "requests", "_cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "hasXHR2", "newRequest", "responseType", "XHR", "_BaseXHR", "_this6", "_extends", "concat", "isReactNative", "navigator", "product", "toLowerCase", "BaseWS", "protocols", "headers", "ws", "createSocket", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "_loop", "lastPacket", "WebSocketCtor", "WebSocket", "MozWebSocket", "WS", "_BaseWS", "_packet", "WT", "_transport", "WebTransport", "transportOptions", "name", "closed", "ready", "createBidirectionalStream", "stream", "decoderStream", "maxPayload", "TextDecoder", "state", "<PERSON><PERSON><PERSON><PERSON>", "isBinary", "headerArray", "getUint16", "n", "getUint32", "pow", "createPacketDecoderStream", "MAX_SAFE_INTEGER", "reader", "readable", "pipeThrough", "<PERSON><PERSON><PERSON><PERSON>", "encoderStream", "pipeTo", "_writer", "getWriter", "read", "done", "transports", "websocket", "webtransport", "polling", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "regx", "names", "query<PERSON><PERSON>", "$0", "$1", "$2", "withEventListeners", "OFFLINE_EVENT_LISTENERS", "listener", "SocketWithoutUpgrade", "writeBuffer", "_prevBufferLen", "_pingInterval", "_pingTimeout", "_maxPayload", "_pingTimeoutTime", "Infinity", "_typeof", "parsed<PERSON><PERSON>", "_transportsByName", "t", "transportName", "agent", "upgrade", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "closeOnBeforeunload", "qs", "qry", "pairs", "l", "pair", "decodeURIComponent", "_beforeunloadEventListener", "transport", "_offlineEventListener", "_onClose", "_cookieJar", "createCookieJar", "_open", "createTransport", "EIO", "id", "priorWebsocketSuccess", "setTransport", "_onDrain", "_onPacket", "flush", "onHandshake", "JSON", "_sendPacket", "_resetPingTimeout", "code", "pingInterval", "pingTimeout", "_pingTimeoutTimer", "delay", "upgrading", "_getWritablePackets", "payloadSize", "c", "utf8Length", "ceil", "_hasPingExpired", "hasExpired", "msg", "options", "compress", "cleanupAndClose", "waitForUpgrade", "tryAllTransports", "SocketWithUpgrade", "_SocketWithoutUpgrade", "_this7", "_upgrades", "_probe", "_this8", "failed", "onTransportOpen", "cleanup", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "_filterUpgrades", "upgrades", "filteredUpgrades", "Socket", "_SocketWithUpgrade", "o", "map", "DEFAULT_TRANSPORTS", "filter"], "mappings": ";;;;;46EAAA,IAAMA,EAAeC,OAAOC,OAAO,MACnCF,EAAmB,KAAI,IACvBA,EAAoB,MAAI,IACxBA,EAAmB,KAAI,IACvBA,EAAmB,KAAI,IACvBA,EAAsB,QAAI,IAC1BA,EAAsB,QAAI,IAC1BA,EAAmB,KAAI,IACvB,IAAMG,EAAuBF,OAAOC,OAAO,MAC3CD,OAAOG,KAAKJ,GAAcK,SAAQ,SAACC,GAC/BH,EAAqBH,EAAaM,IAAQA,CAC9C,IACA,ICuCIC,EDvCEC,EAAe,CAAEC,KAAM,QAASC,KAAM,gBCXtCC,EAAiC,mBAATC,MACT,oBAATA,MACqC,6BAAzCX,OAAOY,UAAUC,SAASC,KAAKH,MACjCI,EAA+C,mBAAhBC,YAE/BC,EAAS,SAACC,GACZ,MAAqC,mBAAvBF,YAAYC,OACpBD,YAAYC,OAAOC,GACnBA,GAAOA,EAAIC,kBAAkBH,WACvC,EACMI,EAAe,SAAHC,EAAoBC,EAAgBC,GAAa,IAA3Cf,EAAIa,EAAJb,KAAMC,EAAIY,EAAJZ,KAC1B,OAAIC,GAAkBD,aAAgBE,KAC9BW,EACOC,EAASd,GAGTe,EAAmBf,EAAMc,GAG/BR,IACJN,aAAgBO,aAAeC,EAAOR,IACnCa,EACOC,EAASd,GAGTe,EAAmB,IAAIb,KAAK,CAACF,IAAQc,GAI7CA,EAASxB,EAAaS,IAASC,GAAQ,IAClD,EACMe,EAAqB,SAACf,EAAMc,GAC9B,IAAME,EAAa,IAAIC,WAKvB,OAJAD,EAAWE,OAAS,WAChB,IAAMC,EAAUH,EAAWI,OAAOC,MAAM,KAAK,GAC7CP,EAAS,KAAOK,GAAW,MAExBH,EAAWM,cAActB,EACpC,EACA,SAASuB,EAAQvB,GACb,OAAIA,aAAgBwB,WACTxB,EAEFA,aAAgBO,YACd,IAAIiB,WAAWxB,GAGf,IAAIwB,WAAWxB,EAAKU,OAAQV,EAAKyB,WAAYzB,EAAK0B,WAEjE,CC9CA,IAHA,IAAMC,EAAQ,mEAERC,EAA+B,oBAAfJ,WAA6B,GAAK,IAAIA,WAAW,KAC9DK,EAAI,EAAGA,EAAIF,GAAcE,IAC9BD,EAAOD,EAAMG,WAAWD,IAAMA,EAkB3B,ICyCHE,EC9DEzB,EAA+C,mBAAhBC,YACxByB,EAAe,SAACC,EAAeC,GACxC,GAA6B,iBAAlBD,EACP,MAAO,CACHlC,KAAM,UACNC,KAAMmC,EAAUF,EAAeC,IAGvC,IAAMnC,EAAOkC,EAAcG,OAAO,GAClC,MAAa,MAATrC,EACO,CACHA,KAAM,UACNC,KAAMqC,EAAmBJ,EAAcK,UAAU,GAAIJ,IAG1CzC,EAAqBM,GAIjCkC,EAAcM,OAAS,EACxB,CACExC,KAAMN,EAAqBM,GAC3BC,KAAMiC,EAAcK,UAAU,IAEhC,CACEvC,KAAMN,EAAqBM,IARxBD,CAUf,EACMuC,EAAqB,SAACrC,EAAMkC,GAC9B,GAAI5B,EAAuB,CACvB,IAAMkC,EFTQ,SAACC,GACnB,IAA8DZ,EAAUa,EAAUC,EAAUC,EAAUC,EAAlGC,EAA+B,IAAhBL,EAAOF,OAAeQ,EAAMN,EAAOF,OAAWS,EAAI,EACnC,MAA9BP,EAAOA,EAAOF,OAAS,KACvBO,IACkC,MAA9BL,EAAOA,EAAOF,OAAS,IACvBO,KAGR,IAAMG,EAAc,IAAI1C,YAAYuC,GAAeI,EAAQ,IAAI1B,WAAWyB,GAC1E,IAAKpB,EAAI,EAAGA,EAAIkB,EAAKlB,GAAK,EACtBa,EAAWd,EAAOa,EAAOX,WAAWD,IACpCc,EAAWf,EAAOa,EAAOX,WAAWD,EAAI,IACxCe,EAAWhB,EAAOa,EAAOX,WAAWD,EAAI,IACxCgB,EAAWjB,EAAOa,EAAOX,WAAWD,EAAI,IACxCqB,EAAMF,KAAQN,GAAY,EAAMC,GAAY,EAC5CO,EAAMF,MAAoB,GAAXL,IAAkB,EAAMC,GAAY,EACnDM,EAAMF,MAAoB,EAAXJ,IAAiB,EAAiB,GAAXC,EAE1C,OAAOI,CACX,CEVwBE,CAAOnD,GACvB,OAAOmC,EAAUK,EAASN,EAC9B,CAEI,MAAO,CAAEO,QAAQ,EAAMzC,KAAAA,EAE/B,EACMmC,EAAY,SAACnC,EAAMkC,GACrB,MACS,SADDA,EAEIlC,aAAgBE,KAETF,EAIA,IAAIE,KAAK,CAACF,IAIjBA,aAAgBO,YAETP,EAIAA,EAAKU,MAG5B,ED1DM0C,EAAYC,OAAOC,aAAa,IA4B/B,SAASC,IACZ,OAAO,IAAIC,gBAAgB,CACvBC,UAASA,SAACC,EAAQC,IFmBnB,SAA8BD,EAAQ5C,GACrCb,GAAkByD,EAAO1D,gBAAgBE,KAClCwD,EAAO1D,KAAK4D,cAAcC,KAAKtC,GAASsC,KAAK/C,GAE/CR,IACJoD,EAAO1D,gBAAgBO,aAAeC,EAAOkD,EAAO1D,OAC9Cc,EAASS,EAAQmC,EAAO1D,OAEnCW,EAAa+C,GAAQ,GAAO,SAACI,GACpBjE,IACDA,EAAe,IAAIkE,aAEvBjD,EAASjB,EAAamE,OAAOF,GACjC,GACJ,CEhCYG,CAAqBP,GAAQ,SAACzB,GAC1B,IACIiC,EADEC,EAAgBlC,EAAcM,OAGpC,GAAI4B,EAAgB,IAChBD,EAAS,IAAI1C,WAAW,GACxB,IAAI4C,SAASF,EAAOxD,QAAQ2D,SAAS,EAAGF,QAEvC,GAAIA,EAAgB,MAAO,CAC5BD,EAAS,IAAI1C,WAAW,GACxB,IAAM8C,EAAO,IAAIF,SAASF,EAAOxD,QACjC4D,EAAKD,SAAS,EAAG,KACjBC,EAAKC,UAAU,EAAGJ,EACtB,KACK,CACDD,EAAS,IAAI1C,WAAW,GACxB,IAAM8C,EAAO,IAAIF,SAASF,EAAOxD,QACjC4D,EAAKD,SAAS,EAAG,KACjBC,EAAKE,aAAa,EAAGC,OAAON,GAChC,CAEIT,EAAO1D,MAA+B,iBAAhB0D,EAAO1D,OAC7BkE,EAAO,IAAM,KAEjBP,EAAWe,QAAQR,GACnBP,EAAWe,QAAQzC,EACvB,GACJ,GAER,CAEA,SAAS0C,EAAYC,GACjB,OAAOA,EAAOC,QAAO,SAACC,EAAKC,GAAK,OAAKD,EAAMC,EAAMxC,MAAM,GAAE,EAC7D,CACA,SAASyC,EAAaJ,EAAQK,GAC1B,GAAIL,EAAO,GAAGrC,SAAW0C,EACrB,OAAOL,EAAOM,QAIlB,IAFA,IAAMxE,EAAS,IAAIc,WAAWyD,GAC1BE,EAAI,EACCtD,EAAI,EAAGA,EAAIoD,EAAMpD,IACtBnB,EAAOmB,GAAK+C,EAAO,GAAGO,KAClBA,IAAMP,EAAO,GAAGrC,SAChBqC,EAAOM,QACPC,EAAI,GAMZ,OAHIP,EAAOrC,QAAU4C,EAAIP,EAAO,GAAGrC,SAC/BqC,EAAO,GAAKA,EAAO,GAAGQ,MAAMD,IAEzBzE,CACX,CE/EO,SAAS2E,EAAQ5E,GACtB,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAIb,KAAOyF,EAAQlF,UACtBM,EAAIb,GAAOyF,EAAQlF,UAAUP,GAE/B,OAAOa,CACT,CAhBkB6E,CAAM7E,EACxB,CA0BA4E,EAAQlF,UAAUoF,GAClBF,EAAQlF,UAAUqF,iBAAmB,SAASC,EAAOC,GAInD,OAHAC,KAAKC,EAAaD,KAAKC,GAAc,CAAA,GACpCD,KAAKC,EAAW,IAAMH,GAASE,KAAKC,EAAW,IAAMH,IAAU,IAC7DI,KAAKH,GACDC,IACT,EAYAN,EAAQlF,UAAU2F,KAAO,SAASL,EAAOC,GACvC,SAASH,IACPI,KAAKI,IAAIN,EAAOF,GAChBG,EAAGM,MAAML,KAAMM,UACjB,CAIA,OAFAV,EAAGG,GAAKA,EACRC,KAAKJ,GAAGE,EAAOF,GACRI,IACT,EAYAN,EAAQlF,UAAU4F,IAClBV,EAAQlF,UAAU+F,eAClBb,EAAQlF,UAAUgG,mBAClBd,EAAQlF,UAAUiG,oBAAsB,SAASX,EAAOC,GAItD,GAHAC,KAAKC,EAAaD,KAAKC,GAAc,CAAA,EAGjC,GAAKK,UAAU1D,OAEjB,OADAoD,KAAKC,EAAa,GACXD,KAIT,IAUIU,EAVAC,EAAYX,KAAKC,EAAW,IAAMH,GACtC,IAAKa,EAAW,OAAOX,KAGvB,GAAI,GAAKM,UAAU1D,OAEjB,cADOoD,KAAKC,EAAW,IAAMH,GACtBE,KAKT,IAAK,IAAI9D,EAAI,EAAGA,EAAIyE,EAAU/D,OAAQV,IAEpC,IADAwE,EAAKC,EAAUzE,MACJ6D,GAAMW,EAAGX,KAAOA,EAAI,CAC7BY,EAAUC,OAAO1E,EAAG,GACpB,KACF,CASF,OAJyB,IAArByE,EAAU/D,eACLoD,KAAKC,EAAW,IAAMH,GAGxBE,IACT,EAUAN,EAAQlF,UAAUqG,KAAO,SAASf,GAChCE,KAAKC,EAAaD,KAAKC,GAAc,CAAA,EAKrC,IAHA,IAAIa,EAAO,IAAIC,MAAMT,UAAU1D,OAAS,GACpC+D,EAAYX,KAAKC,EAAW,IAAMH,GAE7B5D,EAAI,EAAGA,EAAIoE,UAAU1D,OAAQV,IACpC4E,EAAK5E,EAAI,GAAKoE,UAAUpE,GAG1B,GAAIyE,EAEG,CAAIzE,EAAI,EAAb,IAAK,IAAWkB,GADhBuD,EAAYA,EAAUlB,MAAM,IACI7C,OAAQV,EAAIkB,IAAOlB,EACjDyE,EAAUzE,GAAGmE,MAAML,KAAMc,EADKlE,CAKlC,OAAOoD,IACT,EAGAN,EAAQlF,UAAUwG,aAAetB,EAAQlF,UAAUqG,KAUnDnB,EAAQlF,UAAUyG,UAAY,SAASnB,GAErC,OADAE,KAAKC,EAAaD,KAAKC,GAAc,CAAA,EAC9BD,KAAKC,EAAW,IAAMH,IAAU,EACzC,EAUAJ,EAAQlF,UAAU0G,aAAe,SAASpB,GACxC,QAAUE,KAAKiB,UAAUnB,GAAOlD,MAClC,ECxKO,IAAMuE,EACqC,mBAAZC,SAAqD,mBAApBA,QAAQC,QAEhE,SAACX,GAAE,OAAKU,QAAQC,UAAUnD,KAAKwC,EAAG,EAGlC,SAACA,EAAIY,GAAY,OAAKA,EAAaZ,EAAI,EAAE,EAG3Ca,EACW,oBAATC,KACAA,KAEgB,oBAAXC,OACLA,OAGAC,SAAS,cAATA,GChBR,SAASC,EAAK7G,GAAc,IAAA8G,IAAAA,EAAAtB,UAAA1D,OAANiF,MAAId,MAAAa,EAAAA,EAAAA,OAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAJD,EAAIC,EAAAxB,GAAAA,UAAAwB,GAC7B,OAAOD,EAAK3C,QAAO,SAACC,EAAK4C,GAIrB,OAHIjH,EAAIkH,eAAeD,KACnB5C,EAAI4C,GAAKjH,EAAIiH,IAEV5C,CACV,GAAE,CAAE,EACT,CAEA,IAAM8C,EAAqBC,EAAWC,WAChCC,EAAuBF,EAAWG,aACjC,SAASC,EAAsBxH,EAAKyH,GACnCA,EAAKC,iBACL1H,EAAIwG,aAAeW,EAAmBQ,KAAKP,GAC3CpH,EAAI4H,eAAiBN,EAAqBK,KAAKP,KAG/CpH,EAAIwG,aAAeY,EAAWC,WAAWM,KAAKP,GAC9CpH,EAAI4H,eAAiBR,EAAWG,aAAaI,KAAKP,GAE1D,CAkCO,SAASS,IACZ,OAAQC,KAAKC,MAAMpI,SAAS,IAAIkC,UAAU,GACtCmG,KAAKC,SAAStI,SAAS,IAAIkC,UAAU,EAAG,EAChD,CCtDaqG,IAAAA,WAAcC,GACvB,SAAAD,EAAYE,EAAQC,EAAaC,GAAS,IAAAC,EAIT,OAH7BA,EAAAJ,EAAAvI,KAAAsF,KAAMkD,IAAOlD,MACRmD,YAAcA,EACnBE,EAAKD,QAAUA,EACfC,EAAKjJ,KAAO,iBAAiBiJ,CACjC,CAAC,OAAAC,EAAAN,EAAAC,GAAAD,CAAA,EAAAO,EAN+BC,QAQvBC,WAASC,GAOlB,SAAAD,EAAYlB,GAAM,IAAAoB,EAO0B,OANxCA,EAAAD,EAAAhJ,YAAOsF,MACF4D,UAAW,EAChBtB,EAAqBqB,EAAOpB,GAC5BoB,EAAKpB,KAAOA,EACZoB,EAAKE,MAAQtB,EAAKsB,MAClBF,EAAKG,OAASvB,EAAKuB,OACnBH,EAAKzI,gBAAkBqH,EAAKwB,YAAYJ,CAC5C,CACAL,EAAAG,EAAAC,GAAA,IAAAM,EAAAP,EAAAjJ,UAgHC,OAhHDwJ,EASAC,QAAA,SAAQf,EAAQC,EAAaC,GAEzB,OADAM,EAAAlJ,UAAMwG,aAAYtG,KAACsF,KAAA,QAAS,IAAIgD,EAAeE,EAAQC,EAAaC,IAC7DpD,IACX,EACAgE,EAGAE,KAAA,WAGI,OAFAlE,KAAKmE,WAAa,UAClBnE,KAAKoE,SACEpE,IACX,EACAgE,EAGAK,MAAA,WAKI,MAJwB,YAApBrE,KAAKmE,YAAgD,SAApBnE,KAAKmE,aACtCnE,KAAKsE,UACLtE,KAAKuE,WAEFvE,IACX,EACAgE,EAKAQ,KAAA,SAAKC,GACuB,SAApBzE,KAAKmE,YACLnE,KAAK0E,MAAMD,EAKnB,EACAT,EAKAW,OAAA,WACI3E,KAAKmE,WAAa,OAClBnE,KAAK4D,UAAW,EAChBF,EAAAlJ,UAAMwG,aAAYtG,UAAC,OACvB,EACAsJ,EAMAY,OAAA,SAAOvK,GACH,IAAM0D,EAAS1B,EAAahC,EAAM2F,KAAK8D,OAAOvH,YAC9CyD,KAAK6E,SAAS9G,EAClB,EACAiG,EAKAa,SAAA,SAAS9G,GACL2F,EAAAlJ,UAAMwG,aAAYtG,KAAAsF,KAAC,SAAUjC,EACjC,EACAiG,EAKAO,QAAA,SAAQO,GACJ9E,KAAKmE,WAAa,SAClBT,EAAAlJ,UAAMwG,aAAYtG,KAAAsF,KAAC,QAAS8E,EAChC,EACAd,EAKAe,MAAA,SAAMC,GAAS,EAAGhB,EAClBiB,UAAA,SAAUC,GAAoB,IAAZrB,EAAKvD,UAAA1D,OAAA,QAAAuI,IAAA7E,UAAA,GAAAA,UAAA,GAAG,CAAA,EACtB,OAAQ4E,EACJ,MACAlF,KAAKoF,IACLpF,KAAKqF,IACLrF,KAAKuC,KAAK+C,KACVtF,KAAKuF,EAAO1B,IACnBG,EACDoB,EAAA,WACI,IAAMI,EAAWxF,KAAKuC,KAAKiD,SAC3B,OAAkC,IAA3BA,EAASC,QAAQ,KAAcD,EAAW,IAAMA,EAAW,KACrExB,EACDqB,EAAA,WACI,OAAIrF,KAAKuC,KAAKmD,OACR1F,KAAKuC,KAAKoD,QAAUC,OAA0B,MAAnB5F,KAAKuC,KAAKmD,QACjC1F,KAAKuC,KAAKoD,QAAqC,KAA3BC,OAAO5F,KAAKuC,KAAKmD,OACpC,IAAM1F,KAAKuC,KAAKmD,KAGhB,IAEd1B,EACDuB,EAAA,SAAO1B,GACH,IAAMgC,EClIP,SAAgB/K,GACnB,IAAIgL,EAAM,GACV,IAAK,IAAI5J,KAAKpB,EACNA,EAAIkH,eAAe9F,KACf4J,EAAIlJ,SACJkJ,GAAO,KACXA,GAAOC,mBAAmB7J,GAAK,IAAM6J,mBAAmBjL,EAAIoB,KAGpE,OAAO4J,CACX,CDwH6BzH,CAAOwF,GAC5B,OAAOgC,EAAajJ,OAAS,IAAMiJ,EAAe,IACrDpC,CAAA,EAhI0B/D,GETlBsG,WAAOC,GAChB,SAAAD,IAAc,IAAA3C,EAEY,OADtBA,EAAA4C,EAAA5F,MAAAL,KAASM,YAAUN,MACdkG,GAAW,EAAM7C,CAC1B,CAACC,EAAA0C,EAAAC,GAAA,IAAAjC,EAAAgC,EAAAxL,UAwIA,OApIDwJ,EAMAI,OAAA,WACIpE,KAAKmG,GACT,EACAnC,EAMAe,MAAA,SAAMC,GAAS,IAAArB,EAAA3D,KACXA,KAAKmE,WAAa,UAClB,IAAMY,EAAQ,WACVpB,EAAKQ,WAAa,SAClBa,KAEJ,GAAIhF,KAAKkG,IAAalG,KAAK4D,SAAU,CACjC,IAAIwC,EAAQ,EACRpG,KAAKkG,IACLE,IACApG,KAAKG,KAAK,gBAAgB,aACpBiG,GAASrB,GACf,KAEC/E,KAAK4D,WACNwC,IACApG,KAAKG,KAAK,SAAS,aACbiG,GAASrB,GACf,IAER,MAEIA,GAER,EACAf,EAKAmC,EAAA,WACInG,KAAKkG,GAAW,EAChBlG,KAAKqG,SACLrG,KAAKgB,aAAa,OACtB,EACAgD,EAKAY,OAAA,SAAOvK,GAAM,IAAAiM,EAAAtG,MP/CK,SAACuG,EAAgBhK,GAGnC,IAFA,IAAMiK,EAAiBD,EAAe7K,MAAM+B,GACtCgH,EAAU,GACPvI,EAAI,EAAGA,EAAIsK,EAAe5J,OAAQV,IAAK,CAC5C,IAAMuK,EAAgBpK,EAAamK,EAAetK,GAAIK,GAEtD,GADAkI,EAAQvE,KAAKuG,GACc,UAAvBA,EAAcrM,KACd,KAER,CACA,OAAOqK,CACX,EOmDQiC,CAAcrM,EAAM2F,KAAK8D,OAAOvH,YAAYvC,SAd3B,SAAC+D,GAMd,GAJI,YAAcuI,EAAKnC,YAA8B,SAAhBpG,EAAO3D,MACxCkM,EAAK3B,SAGL,UAAY5G,EAAO3D,KAEnB,OADAkM,EAAK/B,QAAQ,CAAEpB,YAAa,oCACrB,EAGXmD,EAAKzB,SAAS9G,MAKd,WAAaiC,KAAKmE,aAElBnE,KAAKkG,GAAW,EAChBlG,KAAKgB,aAAa,gBACd,SAAWhB,KAAKmE,YAChBnE,KAAKmG,IAKjB,EACAnC,EAKAM,QAAA,WAAU,IAAAqC,EAAA3G,KACAqE,EAAQ,WACVsC,EAAKjC,MAAM,CAAC,CAAEtK,KAAM,YAEpB,SAAW4F,KAAKmE,WAChBE,IAKArE,KAAKG,KAAK,OAAQkE,EAE1B,EACAL,EAMAU,MAAA,SAAMD,GAAS,IAAAmC,EAAA5G,KACXA,KAAK4D,UAAW,EPnHF,SAACa,EAAStJ,GAE5B,IAAMyB,EAAS6H,EAAQ7H,OACjB4J,EAAiB,IAAIzF,MAAMnE,GAC7BiK,EAAQ,EACZpC,EAAQzK,SAAQ,SAAC+D,EAAQ7B,GAErBlB,EAAa+C,GAAQ,GAAO,SAACzB,GACzBkK,EAAetK,GAAKI,IACduK,IAAUjK,GACZzB,EAASqL,EAAeM,KAAKrJ,GAErC,GACJ,GACJ,COsGQsJ,CAActC,GAAS,SAACpK,GACpBuM,EAAKI,QAAQ3M,GAAM,WACfuM,EAAKhD,UAAW,EAChBgD,EAAK5F,aAAa,QACtB,GACJ,GACJ,EACAgD,EAKAiD,IAAA,WACI,IAAM/B,EAASlF,KAAKuC,KAAKoD,OAAS,QAAU,OACtC9B,EAAQ7D,KAAK6D,OAAS,GAQ5B,OANI,IAAU7D,KAAKuC,KAAK2E,oBACpBrD,EAAM7D,KAAKuC,KAAK4E,gBAAkBxE,KAEjC3C,KAAK9E,gBAAmB2I,EAAMuD,MAC/BvD,EAAMwD,IAAM,GAETrH,KAAKiF,UAAUC,EAAQrB,IACjCyD,EAAAtB,EAAA,CAAA,CAAA/L,IAAA,OAAAsN,IAvID,WACI,MAAO,SACX,IAAC,EAPwB9D,GCFzB+D,GAAQ,EACZ,IACIA,EAAkC,oBAAnBC,gBACX,oBAAqB,IAAIA,cACjC,CACA,MAAOC,GAEH,CAEG,IAAMC,EAAUH,ECLvB,SAASI,IAAU,CACNC,IAAAA,WAAOC,GAOhB,SAAAD,EAAYtF,GAAM,IAAAc,EAEd,GADAA,EAAAyE,EAAApN,KAAAsF,KAAMuC,IAAKvC,KACa,oBAAb+H,SAA0B,CACjC,IAAMC,EAAQ,WAAaD,SAASE,SAChCvC,EAAOqC,SAASrC,KAEfA,IACDA,EAAOsC,EAAQ,MAAQ,MAE3B3E,EAAK6E,GACoB,oBAAbH,UACJxF,EAAKiD,WAAauC,SAASvC,UAC3BE,IAASnD,EAAKmD,IAC1B,CAAC,OAAArC,CACL,CACAC,EAAAuE,EAAAC,GAAA,IAAA9D,EAAA6D,EAAArN,UA6BC,OA7BDwJ,EAOAgD,QAAA,SAAQ3M,EAAM0F,GAAI,IAAA4D,EAAA3D,KACRmI,EAAMnI,KAAKoI,QAAQ,CACrBC,OAAQ,OACRhO,KAAMA,IAEV8N,EAAIvI,GAAG,UAAWG,GAClBoI,EAAIvI,GAAG,SAAS,SAAC0I,EAAWlF,GACxBO,EAAKM,QAAQ,iBAAkBqE,EAAWlF,EAC9C,GACJ,EACAY,EAKAqC,OAAA,WAAS,IAAAC,EAAAtG,KACCmI,EAAMnI,KAAKoI,UACjBD,EAAIvI,GAAG,OAAQI,KAAK4E,OAAOnC,KAAKzC,OAChCmI,EAAIvI,GAAG,SAAS,SAAC0I,EAAWlF,GACxBkD,EAAKrC,QAAQ,iBAAkBqE,EAAWlF,EAC9C,IACApD,KAAKuI,QAAUJ,GAClBN,CAAA,EAnDwB7B,GAqDhBwC,WAAO9E,GAOhB,SAAA8E,EAAYC,EAAexB,EAAK1E,GAAM,IAAAoE,EAQnB,OAPfA,EAAAjD,EAAAhJ,YAAOsF,MACFyI,cAAgBA,EACrBnG,EAAqBqE,EAAOpE,GAC5BoE,EAAK+B,EAAQnG,EACboE,EAAKgC,EAAUpG,EAAK8F,QAAU,MAC9B1B,EAAKiC,EAAO3B,EACZN,EAAKkC,OAAQ1D,IAAc5C,EAAKlI,KAAOkI,EAAKlI,KAAO,KACnDsM,EAAKmC,IAAUnC,CACnB,CACArD,EAAAkF,EAAA9E,GAAA,IAAAqF,EAAAP,EAAAhO,UAgIC,OAhIDuO,EAKAD,EAAA,WAAU,IACFE,EADEpC,EAAA5G,KAEAuC,EAAOZ,EAAK3B,KAAK0I,EAAO,QAAS,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,aAClHnG,EAAK0G,UAAYjJ,KAAK0I,EAAMR,GAC5B,IAAMgB,EAAOlJ,KAAKmJ,EAAOnJ,KAAKyI,cAAclG,GAC5C,IACI2G,EAAIhF,KAAKlE,KAAK2I,EAAS3I,KAAK4I,GAAM,GAClC,IACI,GAAI5I,KAAK0I,EAAMU,aAGX,IAAK,IAAIlN,KADTgN,EAAIG,uBAAyBH,EAAIG,uBAAsB,GACzCrJ,KAAK0I,EAAMU,aACjBpJ,KAAK0I,EAAMU,aAAapH,eAAe9F,IACvCgN,EAAII,iBAAiBpN,EAAG8D,KAAK0I,EAAMU,aAAalN,GAIhE,CACA,MAAOqN,GAAK,CACZ,GAAI,SAAWvJ,KAAK2I,EAChB,IACIO,EAAII,iBAAiB,eAAgB,2BACzC,CACA,MAAOC,GAAK,CAEhB,IACIL,EAAII,iBAAiB,SAAU,MACnC,CACA,MAAOC,GAAK,CACoB,QAA/BP,EAAKhJ,KAAK0I,EAAMc,iBAA8B,IAAPR,GAAyBA,EAAGS,WAAWP,GAE3E,oBAAqBA,IACrBA,EAAIQ,gBAAkB1J,KAAK0I,EAAMgB,iBAEjC1J,KAAK0I,EAAMiB,iBACXT,EAAIU,QAAU5J,KAAK0I,EAAMiB,gBAE7BT,EAAIW,mBAAqB,WACrB,IAAIb,EACmB,IAAnBE,EAAI/E,aAC4B,QAA/B6E,EAAKpC,EAAK8B,EAAMc,iBAA8B,IAAPR,GAAyBA,EAAGc,aAEpEZ,EAAIa,kBAAkB,gBAEtB,IAAMb,EAAI/E,aAEV,MAAQ+E,EAAIc,QAAU,OAASd,EAAIc,OACnCpD,EAAKqD,IAKLrD,EAAKtF,cAAa,WACdsF,EAAKsD,EAA+B,iBAAfhB,EAAIc,OAAsBd,EAAIc,OAAS,EAC/D,GAAE,KAGXd,EAAI1E,KAAKxE,KAAK6I,EACjB,CACD,MAAOU,GAOH,YAHAvJ,KAAKsB,cAAa,WACdsF,EAAKsD,EAASX,EACjB,GAAE,EAEP,CACwB,oBAAbY,WACPnK,KAAKoK,EAAS5B,EAAQ6B,gBACtB7B,EAAQ8B,SAAStK,KAAKoK,GAAUpK,KAExC,EACA+I,EAKAmB,EAAA,SAASxC,GACL1H,KAAKgB,aAAa,QAAS0G,EAAK1H,KAAKmJ,GACrCnJ,KAAKuK,GAAS,EAClB,EACAxB,EAKAwB,EAAA,SAASC,GACL,QAAI,IAAuBxK,KAAKmJ,GAAQ,OAASnJ,KAAKmJ,EAAtD,CAIA,GADAnJ,KAAKmJ,EAAKU,mBAAqBjC,EAC3B4C,EACA,IACIxK,KAAKmJ,EAAKsB,OACd,CACA,MAAOlB,GAAK,CAEQ,oBAAbY,iBACA3B,EAAQ8B,SAAStK,KAAKoK,GAEjCpK,KAAKmJ,EAAO,IAXZ,CAYJ,EACAJ,EAKAkB,EAAA,WACI,IAAM5P,EAAO2F,KAAKmJ,EAAKuB,aACV,OAATrQ,IACA2F,KAAKgB,aAAa,OAAQ3G,GAC1B2F,KAAKgB,aAAa,WAClBhB,KAAKuK,IAEb,EACAxB,EAKA0B,MAAA,WACIzK,KAAKuK,KACR/B,CAAA,EAjJwB9I,GA0J7B,GAPA8I,EAAQ6B,cAAgB,EACxB7B,EAAQ8B,SAAW,CAAA,EAMK,oBAAbH,SAEP,GAA2B,mBAAhBQ,YAEPA,YAAY,WAAYC,QAEvB,GAAgC,mBAArB/K,iBAAiC,CAE7CA,iBADyB,eAAgBqC,EAAa,WAAa,SAChC0I,GAAe,EACtD,CAEJ,SAASA,IACL,IAAK,IAAI1O,KAAKsM,EAAQ8B,SACd9B,EAAQ8B,SAAStI,eAAe9F,IAChCsM,EAAQ8B,SAASpO,GAAGuO,OAGhC,CACA,IACUvB,EADJ2B,GACI3B,EAAM4B,EAAW,CACnB7B,SAAS,MAEsB,OAArBC,EAAI6B,aASTC,WAAGC,GACZ,SAAAD,EAAYzI,GAAM,IAAA2I,EACdA,EAAAD,EAAAvQ,KAAAsF,KAAMuC,IAAKvC,KACX,IAAM+D,EAAcxB,GAAQA,EAAKwB,YACa,OAA9CmH,EAAKhQ,eAAiB2P,IAAY9G,EAAYmH,CAClD,CAIC,OAJA5H,EAAA0H,EAAAC,GAAAD,EAAAxQ,UACD4N,QAAA,WAAmB,IAAX7F,EAAIjC,UAAA1D,OAAA,QAAAuI,IAAA7E,UAAA,GAAAA,UAAA,GAAG,CAAA,EAEX,OADA6K,EAAc5I,EAAM,CAAE2F,GAAIlI,KAAKkI,IAAMlI,KAAKuC,MACnC,IAAIiG,EAAQsC,EAAY9K,KAAKiH,MAAO1E,IAC9CyI,CAAA,EAToBnD,GAWzB,SAASiD,EAAWvI,GAChB,IAAM0G,EAAU1G,EAAK0G,QAErB,IACI,GAAI,oBAAuBxB,kBAAoBwB,GAAWtB,GACtD,OAAO,IAAIF,cAEnB,CACA,MAAO8B,GAAK,CACZ,IAAKN,EACD,IACI,OAAO,IAAI/G,EAAW,CAAC,UAAUkJ,OAAO,UAAUtE,KAAK,OAAM,oBACjE,CACA,MAAOyC,GAAK,CAEpB,CCzQA,IAAM8B,EAAqC,oBAAdC,WACI,iBAAtBA,UAAUC,SACmB,gBAApCD,UAAUC,QAAQC,cACTC,YAAMxF,GAAA,SAAAwF,IAAA,OAAAxF,EAAA5F,MAAAL,KAAAM,YAAAN,IAAA,CAAAsD,EAAAmI,EAAAxF,GAAA,IAAAjC,EAAAyH,EAAAjR,UA6Fd,OA7FcwJ,EAIfI,OAAA,WACI,IAAM6C,EAAMjH,KAAKiH,MACXyE,EAAY1L,KAAKuC,KAAKmJ,UAEtBnJ,EAAO8I,EACP,CAAA,EACA1J,EAAK3B,KAAKuC,KAAM,QAAS,oBAAqB,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,eAAgB,kBAAmB,SAAU,aAAc,SAAU,uBAChMvC,KAAKuC,KAAK6G,eACV7G,EAAKoJ,QAAU3L,KAAKuC,KAAK6G,cAE7B,IACIpJ,KAAK4L,GAAK5L,KAAK6L,aAAa5E,EAAKyE,EAAWnJ,EAC/C,CACD,MAAOmF,GACH,OAAO1H,KAAKgB,aAAa,QAAS0G,EACtC,CACA1H,KAAK4L,GAAGrP,WAAayD,KAAK8D,OAAOvH,WACjCyD,KAAK8L,mBACT,EACA9H,EAKA8H,kBAAA,WAAoB,IAAAzI,EAAArD,KAChBA,KAAK4L,GAAGG,OAAS,WACT1I,EAAKd,KAAKyJ,WACV3I,EAAKuI,GAAGK,EAAQC,QAEpB7I,EAAKsB,UAET3E,KAAK4L,GAAGO,QAAU,SAACC,GAAU,OAAK/I,EAAKkB,QAAQ,CAC3CpB,YAAa,8BACbC,QAASgJ,GACX,EACFpM,KAAK4L,GAAGS,UAAY,SAACC,GAAE,OAAKjJ,EAAKuB,OAAO0H,EAAGjS,KAAK,EAChD2F,KAAK4L,GAAGW,QAAU,SAAChD,GAAC,OAAKlG,EAAKY,QAAQ,kBAAmBsF,EAAE,GAC9DvF,EACDU,MAAA,SAAMD,GAAS,IAAAd,EAAA3D,KACXA,KAAK4D,UAAW,EAGhB,IADA,IAAA4I,EAAAA,WAEI,IAAMzO,EAAS0G,EAAQvI,GACjBuQ,EAAavQ,IAAMuI,EAAQ7H,OAAS,EAC1C5B,EAAa+C,EAAQ4F,EAAKzI,gBAAgB,SAACb,GAIvC,IACIsJ,EAAKqD,QAAQjJ,EAAQ1D,EACzB,CACA,MAAOkP,GACP,CACIkD,GAGAtL,GAAS,WACLwC,EAAKC,UAAW,EAChBD,EAAK3C,aAAa,QACtB,GAAG2C,EAAKrC,aAEhB,KApBKpF,EAAI,EAAGA,EAAIuI,EAAQ7H,OAAQV,IAAGsQ,KAsB1CxI,EACDM,QAAA,gBAC2B,IAAZtE,KAAK4L,KACZ5L,KAAK4L,GAAGW,QAAU,aAClBvM,KAAK4L,GAAGvH,QACRrE,KAAK4L,GAAK,KAElB,EACA5H,EAKAiD,IAAA,WACI,IAAM/B,EAASlF,KAAKuC,KAAKoD,OAAS,MAAQ,KACpC9B,EAAQ7D,KAAK6D,OAAS,GAS5B,OAPI7D,KAAKuC,KAAK2E,oBACVrD,EAAM7D,KAAKuC,KAAK4E,gBAAkBxE,KAGjC3C,KAAK9E,iBACN2I,EAAMwD,IAAM,GAETrH,KAAKiF,UAAUC,EAAQrB,IACjCyD,EAAAmE,EAAA,CAAA,CAAAxR,IAAA,OAAAsN,IA5FD,WACI,MAAO,WACX,IAAC,EAHuB9D,GA+FtBiJ,GAAgBxK,EAAWyK,WAAazK,EAAW0K,aAU5CC,YAAEC,GAAA,SAAAD,IAAA,OAAAC,EAAAzM,MAAAL,KAAAM,YAAAN,IAAA,CAAAsD,EAAAuJ,EAAAC,GAAA,IAAA/D,EAAA8D,EAAArS,UAUV,OAVUuO,EACX8C,aAAA,SAAa5E,EAAKyE,EAAWnJ,GACzB,OAAQ8I,EAIF,IAAIqB,GAAczF,EAAKyE,EAAWnJ,GAHlCmJ,EACI,IAAIgB,GAAczF,EAAKyE,GACvB,IAAIgB,GAAczF,IAE/B8B,EACD/B,QAAA,SAAQ+F,EAAS1S,GACb2F,KAAK4L,GAAGpH,KAAKnK,IAChBwS,CAAA,EAVmBpB,ICtGXuB,YAAE/G,GAAA,SAAA+G,IAAA,OAAA/G,EAAA5F,MAAAL,KAAAM,YAAAN,IAAA,CAAAsD,EAAA0J,EAAA/G,GAAA,IAAAjC,EAAAgJ,EAAAxS,UAmEV,OAnEUwJ,EAIXI,OAAA,WAAS,IAAAf,EAAArD,KACL,IAEIA,KAAKiN,EAAa,IAAIC,aAAalN,KAAKiF,UAAU,SAAUjF,KAAKuC,KAAK4K,iBAAiBnN,KAAKoN,MAC/F,CACD,MAAO1F,GACH,OAAO1H,KAAKgB,aAAa,QAAS0G,EACtC,CACA1H,KAAKiN,EAAWI,OACXnP,MAAK,WACNmF,EAAKkB,SACT,IAAE,OACS,SAACmD,GACRrE,EAAKY,QAAQ,qBAAsByD,EACvC,IAEA1H,KAAKiN,EAAWK,MAAMpP,MAAK,WACvBmF,EAAK4J,EAAWM,4BAA4BrP,MAAK,SAACsP,GAC9C,IAAMC,EXqDf,SAAmCC,EAAYnR,GAC7CH,IACDA,EAAe,IAAIuR,aAEvB,IAAM1O,EAAS,GACX2O,EAAQ,EACRC,GAAkB,EAClBC,GAAW,EACf,OAAO,IAAIjQ,gBAAgB,CACvBC,UAASA,SAACsB,EAAOpB,GAEb,IADAiB,EAAOiB,KAAKd,KACC,CACT,GAAc,IAAVwO,EAAqC,CACrC,GAAI5O,EAAYC,GAAU,EACtB,MAEJ,IAAMV,EAASc,EAAaJ,EAAQ,GACpC6O,IAAkC,KAAtBvP,EAAO,IACnBsP,EAA6B,IAAZtP,EAAO,GAEpBqP,EADAC,EAAiB,IACT,EAEgB,MAAnBA,EACG,EAGA,CAEhB,MACK,GAAc,IAAVD,EAAiD,CACtD,GAAI5O,EAAYC,GAAU,EACtB,MAEJ,IAAM8O,EAAc1O,EAAaJ,EAAQ,GACzC4O,EAAiB,IAAIpP,SAASsP,EAAYhT,OAAQgT,EAAYjS,WAAYiS,EAAYnR,QAAQoR,UAAU,GACxGJ,EAAQ,CACZ,MACK,GAAc,IAAVA,EAAiD,CACtD,GAAI5O,EAAYC,GAAU,EACtB,MAEJ,IAAM8O,EAAc1O,EAAaJ,EAAQ,GACnCN,EAAO,IAAIF,SAASsP,EAAYhT,OAAQgT,EAAYjS,WAAYiS,EAAYnR,QAC5EqR,EAAItP,EAAKuP,UAAU,GACzB,GAAID,EAAInL,KAAKqL,IAAI,EAAG,IAAW,EAAG,CAE9BnQ,EAAWe,QAAQ5E,GACnB,KACJ,CACA0T,EAAiBI,EAAInL,KAAKqL,IAAI,EAAG,IAAMxP,EAAKuP,UAAU,GACtDN,EAAQ,CACZ,KACK,CACD,GAAI5O,EAAYC,GAAU4O,EACtB,MAEJ,IAAMxT,EAAOgF,EAAaJ,EAAQ4O,GAClC7P,EAAWe,QAAQ1C,EAAayR,EAAWzT,EAAO+B,EAAaoB,OAAOnD,GAAOkC,IAC7EqR,EAAQ,CACZ,CACA,GAAuB,IAAnBC,GAAwBA,EAAiBH,EAAY,CACrD1P,EAAWe,QAAQ5E,GACnB,KACJ,CACJ,CACJ,GAER,CWxHsCiU,CAA0BxI,OAAOyI,iBAAkBhL,EAAKS,OAAOvH,YAC/E+R,EAASd,EAAOe,SAASC,YAAYf,GAAegB,YACpDC,EAAgB9Q,IACtB8Q,EAAcH,SAASI,OAAOnB,EAAO5J,UACrCP,EAAKuL,EAAUF,EAAc9K,SAASiL,aACzB,SAAPC,IACFR,EACKQ,OACA5Q,MAAK,SAAAjD,GAAqB,IAAlB8T,EAAI9T,EAAJ8T,KAAMvH,EAAKvM,EAALuM,MACXuH,IAGJ1L,EAAKwB,SAAS2C,GACdsH,IACH,WACU,SAACpH,GACX,IAELoH,GACA,IAAM/Q,EAAS,CAAE3D,KAAM,QACnBiJ,EAAKQ,MAAMuD,MACXrJ,EAAO1D,KAAI,WAAA+Q,OAAc/H,EAAKQ,MAAMuD,IAAO,OAE/C/D,EAAKuL,EAAQlK,MAAM3G,GAAQG,MAAK,WAAA,OAAMmF,EAAKsB,WAC/C,GACJ,KACHX,EACDU,MAAA,SAAMD,GAAS,IAAAd,EAAA3D,KACXA,KAAK4D,UAAW,EAChB,IADsB,IAAA4I,EAAAA,WAElB,IAAMzO,EAAS0G,EAAQvI,GACjBuQ,EAAavQ,IAAMuI,EAAQ7H,OAAS,EAC1C+G,EAAKiL,EAAQlK,MAAM3G,GAAQG,MAAK,WACxBuO,GACAtL,GAAS,WACLwC,EAAKC,UAAW,EAChBD,EAAK3C,aAAa,QACtB,GAAG2C,EAAKrC,aAEhB,KAVKpF,EAAI,EAAGA,EAAIuI,EAAQ7H,OAAQV,IAAGsQ,KAY1CxI,EACDM,QAAA,WACI,IAAI0E,EACuB,QAA1BA,EAAKhJ,KAAKiN,SAA+B,IAAPjE,GAAyBA,EAAG3E,SAClEiD,EAAA0F,EAAA,CAAA,CAAA/S,IAAA,OAAAsN,IAlED,WACI,MAAO,cACX,IAAC,EAHmB9D,GCRXuL,GAAa,CACtBC,UAAWpC,GACXqC,aAAclC,GACdmC,QAASnE,GCaPoE,GAAK,sPACLC,GAAQ,CACV,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAElI,SAASC,GAAMxJ,GAClB,GAAIA,EAAIlJ,OAAS,IACb,KAAM,eAEV,IAAM2S,EAAMzJ,EAAK0J,EAAI1J,EAAIL,QAAQ,KAAM8D,EAAIzD,EAAIL,QAAQ,MAC7C,GAAN+J,IAAiB,GAANjG,IACXzD,EAAMA,EAAInJ,UAAU,EAAG6S,GAAK1J,EAAInJ,UAAU6S,EAAGjG,GAAGkG,QAAQ,KAAM,KAAO3J,EAAInJ,UAAU4M,EAAGzD,EAAIlJ,SAG9F,IADA,IAwBmBiH,EACbxJ,EAzBFqV,EAAIN,GAAGO,KAAK7J,GAAO,IAAKmB,EAAM,CAAE,EAAE/K,EAAI,GACnCA,KACH+K,EAAIoI,GAAMnT,IAAMwT,EAAExT,IAAM,GAU5B,OARU,GAANsT,IAAiB,GAANjG,IACXtC,EAAI2I,OAASL,EACbtI,EAAI4I,KAAO5I,EAAI4I,KAAKlT,UAAU,EAAGsK,EAAI4I,KAAKjT,OAAS,GAAG6S,QAAQ,KAAM,KACpExI,EAAI6I,UAAY7I,EAAI6I,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9ExI,EAAI8I,SAAU,GAElB9I,EAAI+I,UAIR,SAAmBlV,EAAKwK,GACpB,IAAM2K,EAAO,WAAYC,EAAQ5K,EAAKmK,QAAQQ,EAAM,KAAKvU,MAAM,KACvC,KAApB4J,EAAK7F,MAAM,EAAG,IAA6B,IAAhB6F,EAAK1I,QAChCsT,EAAMtP,OAAO,EAAG,GAEE,KAAlB0E,EAAK7F,OAAO,IACZyQ,EAAMtP,OAAOsP,EAAMtT,OAAS,EAAG,GAEnC,OAAOsT,CACX,CAboBF,CAAU/I,EAAKA,EAAU,MACzCA,EAAIkJ,UAaetM,EAbUoD,EAAW,MAclC5M,EAAO,CAAA,EACbwJ,EAAM4L,QAAQ,6BAA6B,SAAUW,EAAIC,EAAIC,GACrDD,IACAhW,EAAKgW,GAAMC,EAEnB,IACOjW,GAnBA4M,CACX,CCrCA,IAAMsJ,GAAiD,mBAArB1Q,kBACC,mBAAxBY,oBACL+P,GAA0B,GAC5BD,IAGA1Q,iBAAiB,WAAW,WACxB2Q,GAAwBxW,SAAQ,SAACyW,GAAQ,OAAKA,MACjD,IAAE,GAyBMC,IAAAA,YAAoBhN,GAO7B,SAAAgN,EAAYzJ,EAAK1E,GAAM,IAAAc,EAiBnB,IAhBAA,EAAAK,EAAAhJ,YAAOsF,MACFzD,WX7BoB,cW8BzB8G,EAAKsN,YAAc,GACnBtN,EAAKuN,EAAiB,EACtBvN,EAAKwN,GAAiB,EACtBxN,EAAKyN,GAAgB,EACrBzN,EAAK0N,GAAe,EAKpB1N,EAAK2N,EAAmBC,IACpBhK,GAAO,WAAQiK,EAAYjK,KAC3B1E,EAAO0E,EACPA,EAAM,MAENA,EAAK,CACL,IAAMkK,EAAY7B,GAAMrI,GACxB1E,EAAKiD,SAAW2L,EAAUtB,KAC1BtN,EAAKoD,OACsB,UAAvBwL,EAAUlJ,UAA+C,QAAvBkJ,EAAUlJ,SAChD1F,EAAKmD,KAAOyL,EAAUzL,KAClByL,EAAUtN,QACVtB,EAAKsB,MAAQsN,EAAUtN,MAC/B,MACStB,EAAKsN,OACVtN,EAAKiD,SAAW8J,GAAM/M,EAAKsN,MAAMA,MA2ExB,OAzEbvN,EAAqBe,EAAOd,GAC5Bc,EAAKsC,OACD,MAAQpD,EAAKoD,OACPpD,EAAKoD,OACe,oBAAboC,UAA4B,WAAaA,SAASE,SAC/D1F,EAAKiD,WAAajD,EAAKmD,OAEvBnD,EAAKmD,KAAOrC,EAAKsC,OAAS,MAAQ,MAEtCtC,EAAKmC,SACDjD,EAAKiD,WACoB,oBAAbuC,SAA2BA,SAASvC,SAAW,aAC/DnC,EAAKqC,KACDnD,EAAKmD,OACoB,oBAAbqC,UAA4BA,SAASrC,KACvCqC,SAASrC,KACTrC,EAAKsC,OACD,MACA,MAClBtC,EAAK2L,WAAa,GAClB3L,EAAK+N,EAAoB,GACzB7O,EAAKyM,WAAWhV,SAAQ,SAACqX,GACrB,IAAMC,EAAgBD,EAAE7W,UAAU4S,KAClC/J,EAAK2L,WAAW9O,KAAKoR,GACrBjO,EAAK+N,EAAkBE,GAAiBD,CAC5C,IACAhO,EAAKd,KAAO4I,EAAc,CACtB7F,KAAM,aACNiM,OAAO,EACP7H,iBAAiB,EACjB8H,SAAS,EACTrK,eAAgB,IAChBsK,iBAAiB,EACjBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAmB,CACfC,UAAW,MAEf1E,iBAAkB,CAAE,EACpB2E,qBAAqB,GACtBvP,GACHc,EAAKd,KAAK+C,KACNjC,EAAKd,KAAK+C,KAAKmK,QAAQ,MAAO,KACzBpM,EAAKd,KAAKmP,iBAAmB,IAAM,IACb,iBAApBrO,EAAKd,KAAKsB,QACjBR,EAAKd,KAAKsB,MRhGf,SAAgBkO,GAGnB,IAFA,IAAIC,EAAM,CAAA,EACNC,EAAQF,EAAGrW,MAAM,KACZQ,EAAI,EAAGgW,EAAID,EAAMrV,OAAQV,EAAIgW,EAAGhW,IAAK,CAC1C,IAAIiW,EAAOF,EAAM/V,GAAGR,MAAM,KAC1BsW,EAAII,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,GAC/D,CACA,OAAOH,CACX,CQwF8BxU,CAAO6F,EAAKd,KAAKsB,QAEnC0M,KACIlN,EAAKd,KAAKuP,sBAIVzO,EAAKgP,EAA6B,WAC1BhP,EAAKiP,YAELjP,EAAKiP,UAAU9R,qBACf6C,EAAKiP,UAAUjO,UAGvBxE,iBAAiB,eAAgBwD,EAAKgP,GAA4B,IAEhD,cAAlBhP,EAAKmC,WACLnC,EAAKkP,EAAwB,WACzBlP,EAAKmP,EAAS,kBAAmB,CAC7BrP,YAAa,6BAGrBqN,GAAwBtQ,KAAKmD,EAAKkP,KAGtClP,EAAKd,KAAKmH,kBACVrG,EAAKoP,OAAaC,GAEtBrP,EAAKsP,IAAQtP,CACjB,CACAC,EAAAoN,EAAAhN,GAAA,IAAAM,EAAA0M,EAAAlW,UAiYC,OAjYDwJ,EAOA4O,gBAAA,SAAgBxF,GACZ,IAAMvJ,EAAQsH,EAAc,CAAA,EAAInL,KAAKuC,KAAKsB,OAE1CA,EAAMgP,IdPU,EcShBhP,EAAMyO,UAAYlF,EAEdpN,KAAK8S,KACLjP,EAAMuD,IAAMpH,KAAK8S,IACrB,IAAMvQ,EAAO4I,EAAc,GAAInL,KAAKuC,KAAM,CACtCsB,MAAAA,EACAC,OAAQ9D,KACRwF,SAAUxF,KAAKwF,SACfG,OAAQ3F,KAAK2F,OACbD,KAAM1F,KAAK0F,MACZ1F,KAAKuC,KAAK4K,iBAAiBC,IAC9B,OAAO,IAAIpN,KAAKoR,EAAkBhE,GAAM7K,EAC5C,EACAyB,EAKA2O,EAAA,WAAQ,IAAAhP,EAAA3D,KACJ,GAA+B,IAA3BA,KAAKgP,WAAWpS,OAApB,CAOA,IAAM0U,EAAgBtR,KAAKuC,KAAKkP,iBAC5Bf,EAAqBqC,wBACqB,IAA1C/S,KAAKgP,WAAWvJ,QAAQ,aACtB,YACAzF,KAAKgP,WAAW,GACtBhP,KAAKmE,WAAa,UAClB,IAAMmO,EAAYtS,KAAK4S,gBAAgBtB,GACvCgB,EAAUpO,OACVlE,KAAKgT,aAAaV,EATlB,MAJItS,KAAKsB,cAAa,WACdqC,EAAK3C,aAAa,QAAS,0BAC9B,GAAE,EAYX,EACAgD,EAKAgP,aAAA,SAAaV,GAAW,IAAAhM,EAAAtG,KAChBA,KAAKsS,WACLtS,KAAKsS,UAAU9R,qBAGnBR,KAAKsS,UAAYA,EAEjBA,EACK1S,GAAG,QAASI,KAAKiT,EAASxQ,KAAKzC,OAC/BJ,GAAG,SAAUI,KAAKkT,EAAUzQ,KAAKzC,OACjCJ,GAAG,QAASI,KAAKkK,EAASzH,KAAKzC,OAC/BJ,GAAG,SAAS,SAACsD,GAAM,OAAKoD,EAAKkM,EAAS,kBAAmBtP,KAClE,EACAc,EAKAW,OAAA,WACI3E,KAAKmE,WAAa,OAClBuM,EAAqBqC,sBACjB,cAAgB/S,KAAKsS,UAAUlF,KACnCpN,KAAKgB,aAAa,QAClBhB,KAAKmT,OACT,EACAnP,EAKAkP,EAAA,SAAUnV,GACN,GAAI,YAAciC,KAAKmE,YACnB,SAAWnE,KAAKmE,YAChB,YAAcnE,KAAKmE,WAInB,OAHAnE,KAAKgB,aAAa,SAAUjD,GAE5BiC,KAAKgB,aAAa,aACVjD,EAAO3D,MACX,IAAK,OACD4F,KAAKoT,YAAYC,KAAK/D,MAAMvR,EAAO1D,OACnC,MACJ,IAAK,OACD2F,KAAKsT,EAAY,QACjBtT,KAAKgB,aAAa,QAClBhB,KAAKgB,aAAa,QAClBhB,KAAKuT,IACL,MACJ,IAAK,QACD,IAAM7L,EAAM,IAAIlE,MAAM,gBAEtBkE,EAAI8L,KAAOzV,EAAO1D,KAClB2F,KAAKkK,EAASxC,GACd,MACJ,IAAK,UACD1H,KAAKgB,aAAa,OAAQjD,EAAO1D,MACjC2F,KAAKgB,aAAa,UAAWjD,EAAO1D,MAMpD,EACA2J,EAMAoP,YAAA,SAAY/Y,GACR2F,KAAKgB,aAAa,YAAa3G,GAC/B2F,KAAK8S,GAAKzY,EAAK+M,IACfpH,KAAKsS,UAAUzO,MAAMuD,IAAM/M,EAAK+M,IAChCpH,KAAK6Q,EAAgBxW,EAAKoZ,aAC1BzT,KAAK8Q,EAAezW,EAAKqZ,YACzB1T,KAAK+Q,EAAc1W,EAAKqT,WACxB1N,KAAK2E,SAED,WAAa3E,KAAKmE,YAEtBnE,KAAKuT,GACT,EACAvP,EAKAuP,EAAA,WAAoB,IAAA5M,EAAA3G,KAChBA,KAAK0C,eAAe1C,KAAK2T,GACzB,IAAMC,EAAQ5T,KAAK6Q,EAAgB7Q,KAAK8Q,EACxC9Q,KAAKgR,EAAmBpO,KAAKC,MAAQ+Q,EACrC5T,KAAK2T,EAAoB3T,KAAKsB,cAAa,WACvCqF,EAAK6L,EAAS,eACjB,GAAEoB,GACC5T,KAAKuC,KAAKyJ,WACVhM,KAAK2T,EAAkBzH,OAE/B,EACAlI,EAKAiP,EAAA,WACIjT,KAAK2Q,YAAY/P,OAAO,EAAGZ,KAAK4Q,GAIhC5Q,KAAK4Q,EAAiB,EAClB,IAAM5Q,KAAK2Q,YAAY/T,OACvBoD,KAAKgB,aAAa,SAGlBhB,KAAKmT,OAEb,EACAnP,EAKAmP,MAAA,WACI,GAAI,WAAanT,KAAKmE,YAClBnE,KAAKsS,UAAU1O,WACd5D,KAAK6T,WACN7T,KAAK2Q,YAAY/T,OAAQ,CACzB,IAAM6H,EAAUzE,KAAK8T,IACrB9T,KAAKsS,UAAU9N,KAAKC,GAGpBzE,KAAK4Q,EAAiBnM,EAAQ7H,OAC9BoD,KAAKgB,aAAa,QACtB,CACJ,EACAgD,EAMA8P,EAAA,WAII,KAH+B9T,KAAK+Q,GACR,YAAxB/Q,KAAKsS,UAAUlF,MACfpN,KAAK2Q,YAAY/T,OAAS,GAE1B,OAAOoD,KAAK2Q,YAGhB,IADA,IVrUmB7V,EUqUfiZ,EAAc,EACT7X,EAAI,EAAGA,EAAI8D,KAAK2Q,YAAY/T,OAAQV,IAAK,CAC9C,IAAM7B,EAAO2F,KAAK2Q,YAAYzU,GAAG7B,KAIjC,GAHIA,IACA0Z,GVxUO,iBADIjZ,EUyUeT,GVlU1C,SAAoByL,GAEhB,IADA,IAAIkO,EAAI,EAAGpX,EAAS,EACXV,EAAI,EAAGgW,EAAIpM,EAAIlJ,OAAQV,EAAIgW,EAAGhW,KACnC8X,EAAIlO,EAAI3J,WAAWD,IACX,IACJU,GAAU,EAELoX,EAAI,KACTpX,GAAU,EAELoX,EAAI,OAAUA,GAAK,MACxBpX,GAAU,GAGVV,IACAU,GAAU,GAGlB,OAAOA,CACX,CAxBeqX,CAAWnZ,GAGfgI,KAAKoR,KAPQ,MAOFpZ,EAAIiB,YAAcjB,EAAIwE,QUsU5BpD,EAAI,GAAK6X,EAAc/T,KAAK+Q,EAC5B,OAAO/Q,KAAK2Q,YAAYlR,MAAM,EAAGvD,GAErC6X,GAAe,CACnB,CACA,OAAO/T,KAAK2Q,WAChB,EAUA3M,EAAcmQ,EAAA,WAAkB,IAAAvN,EAAA5G,KAC5B,IAAKA,KAAKgR,EACN,OAAO,EACX,IAAMoD,EAAaxR,KAAKC,MAAQ7C,KAAKgR,EAOrC,OANIoD,IACApU,KAAKgR,EAAmB,EACxB7P,GAAS,WACLyF,EAAK4L,EAAS,eAClB,GAAGxS,KAAKsB,eAEL8S,CACX,EACApQ,EAQAU,MAAA,SAAM2P,EAAKC,EAASvU,GAEhB,OADAC,KAAKsT,EAAY,UAAWe,EAAKC,EAASvU,GACnCC,IACX,EACAgE,EAQAQ,KAAA,SAAK6P,EAAKC,EAASvU,GAEf,OADAC,KAAKsT,EAAY,UAAWe,EAAKC,EAASvU,GACnCC,IACX,EACAgE,EASAsP,EAAA,SAAYlZ,EAAMC,EAAMia,EAASvU,GAS7B,GARI,mBAAsB1F,IACtB0F,EAAK1F,EACLA,OAAO8K,GAEP,mBAAsBmP,IACtBvU,EAAKuU,EACLA,EAAU,MAEV,YAActU,KAAKmE,YAAc,WAAanE,KAAKmE,WAAvD,EAGAmQ,EAAUA,GAAW,IACbC,UAAW,IAAUD,EAAQC,SACrC,IAAMxW,EAAS,CACX3D,KAAMA,EACNC,KAAMA,EACNia,QAASA,GAEbtU,KAAKgB,aAAa,eAAgBjD,GAClCiC,KAAK2Q,YAAYzQ,KAAKnC,GAClBgC,GACAC,KAAKG,KAAK,QAASJ,GACvBC,KAAKmT,OAZL,CAaJ,EACAnP,EAGAK,MAAA,WAAQ,IAAA6G,EAAAlL,KACEqE,EAAQ,WACV6G,EAAKsH,EAAS,gBACdtH,EAAKoH,UAAUjO,SAEbmQ,EAAkB,SAAlBA,IACFtJ,EAAK9K,IAAI,UAAWoU,GACpBtJ,EAAK9K,IAAI,eAAgBoU,GACzBnQ,KAEEoQ,EAAiB,WAEnBvJ,EAAK/K,KAAK,UAAWqU,GACrBtJ,EAAK/K,KAAK,eAAgBqU,IAqB9B,MAnBI,YAAcxU,KAAKmE,YAAc,SAAWnE,KAAKmE,aACjDnE,KAAKmE,WAAa,UACdnE,KAAK2Q,YAAY/T,OACjBoD,KAAKG,KAAK,SAAS,WACX+K,EAAK2I,UACLY,IAGApQ,GAER,IAEKrE,KAAK6T,UACVY,IAGApQ,KAGDrE,IACX,EACAgE,EAKAkG,EAAA,SAASxC,GAEL,GADAgJ,EAAqBqC,uBAAwB,EACzC/S,KAAKuC,KAAKmS,kBACV1U,KAAKgP,WAAWpS,OAAS,GACL,YAApBoD,KAAKmE,WAEL,OADAnE,KAAKgP,WAAWzP,QACTS,KAAK2S,IAEhB3S,KAAKgB,aAAa,QAAS0G,GAC3B1H,KAAKwS,EAAS,kBAAmB9K,EACrC,EACA1D,EAKAwO,EAAA,SAAStP,EAAQC,GACb,GAAI,YAAcnD,KAAKmE,YACnB,SAAWnE,KAAKmE,YAChB,YAAcnE,KAAKmE,WAAY,CAS/B,GAPAnE,KAAK0C,eAAe1C,KAAK2T,GAEzB3T,KAAKsS,UAAU9R,mBAAmB,SAElCR,KAAKsS,UAAUjO,QAEfrE,KAAKsS,UAAU9R,qBACX+P,KACIvQ,KAAKqS,GACL5R,oBAAoB,eAAgBT,KAAKqS,GAA4B,GAErErS,KAAKuS,GAAuB,CAC5B,IAAMrW,EAAIsU,GAAwB/K,QAAQzF,KAAKuS,IACpC,IAAPrW,GACAsU,GAAwB5P,OAAO1E,EAAG,EAE1C,CAGJ8D,KAAKmE,WAAa,SAElBnE,KAAK8S,GAAK,KAEV9S,KAAKgB,aAAa,QAASkC,EAAQC,GAGnCnD,KAAK2Q,YAAc,GACnB3Q,KAAK4Q,EAAiB,CAC1B,GACHF,CAAA,EAhfqChR,GAkf1CgR,GAAqBzI,SdhYG,EcwZX0M,IAAAA,YAAiBC,GAC1B,SAAAD,IAAc,IAAAE,EAEU,OADpBA,EAAAD,EAAAvU,MAAAL,KAASM,YAAUN,MACd8U,EAAY,GAAGD,CACxB,CAACvR,EAAAqR,EAAAC,GAAA,IAAA7L,EAAA4L,EAAAna,UAgIA,OAhIAuO,EACDpE,OAAA,WAEI,GADAiQ,EAAApa,UAAMmK,OAAMjK,KAAAsF,MACR,SAAWA,KAAKmE,YAAcnE,KAAKuC,KAAKiP,QACxC,IAAK,IAAItV,EAAI,EAAGA,EAAI8D,KAAK8U,EAAUlY,OAAQV,IACvC8D,KAAK+U,GAAO/U,KAAK8U,EAAU5Y,GAGvC,EACA6M,EAMAgM,GAAA,SAAO3H,GAAM,IAAA4H,EAAAhV,KACLsS,EAAYtS,KAAK4S,gBAAgBxF,GACjC6H,GAAS,EACbvE,GAAqBqC,uBAAwB,EAC7C,IAAMmC,EAAkB,WAChBD,IAEJ3C,EAAU9N,KAAK,CAAC,CAAEpK,KAAM,OAAQC,KAAM,WACtCiY,EAAUnS,KAAK,UAAU,SAACkU,GACtB,IAAIY,EAEJ,GAAI,SAAWZ,EAAIja,MAAQ,UAAYia,EAAIha,KAAM,CAG7C,GAFA2a,EAAKnB,WAAY,EACjBmB,EAAKhU,aAAa,YAAasR,IAC1BA,EACD,OACJ5B,GAAqBqC,sBACjB,cAAgBT,EAAUlF,KAC9B4H,EAAK1C,UAAUvN,OAAM,WACbkQ,GAEA,WAAaD,EAAK7Q,aAEtBgR,IACAH,EAAKhC,aAAaV,GAClBA,EAAU9N,KAAK,CAAC,CAAEpK,KAAM,aACxB4a,EAAKhU,aAAa,UAAWsR,GAC7BA,EAAY,KACZ0C,EAAKnB,WAAY,EACjBmB,EAAK7B,QACT,GACJ,KACK,CACD,IAAMzL,EAAM,IAAIlE,MAAM,eAEtBkE,EAAI4K,UAAYA,EAAUlF,KAC1B4H,EAAKhU,aAAa,eAAgB0G,EACtC,CACJ,MAEJ,SAAS0N,IACDH,IAGJA,GAAS,EACTE,IACA7C,EAAUjO,QACViO,EAAY,KAChB,CAEA,IAAM/F,EAAU,SAAC7E,GACb,IAAM2N,EAAQ,IAAI7R,MAAM,gBAAkBkE,GAE1C2N,EAAM/C,UAAYA,EAAUlF,KAC5BgI,IACAJ,EAAKhU,aAAa,eAAgBqU,IAEtC,SAASC,IACL/I,EAAQ,mBACZ,CAEA,SAASJ,IACLI,EAAQ,gBACZ,CAEA,SAASgJ,EAAUC,GACXlD,GAAakD,EAAGpI,OAASkF,EAAUlF,MACnCgI,GAER,CAEA,IAAMD,EAAU,WACZ7C,EAAU/R,eAAe,OAAQ2U,GACjC5C,EAAU/R,eAAe,QAASgM,GAClC+F,EAAU/R,eAAe,QAAS+U,GAClCN,EAAK5U,IAAI,QAAS+L,GAClB6I,EAAK5U,IAAI,YAAamV,IAE1BjD,EAAUnS,KAAK,OAAQ+U,GACvB5C,EAAUnS,KAAK,QAASoM,GACxB+F,EAAUnS,KAAK,QAASmV,GACxBtV,KAAKG,KAAK,QAASgM,GACnBnM,KAAKG,KAAK,YAAaoV,IACyB,IAA5CvV,KAAK8U,EAAUrP,QAAQ,iBACd,iBAAT2H,EAEApN,KAAKsB,cAAa,WACT2T,GACD3C,EAAUpO,MAEjB,GAAE,KAGHoO,EAAUpO,QAEjB6E,EACDqK,YAAA,SAAY/Y,GACR2F,KAAK8U,EAAY9U,KAAKyV,GAAgBpb,EAAKqb,UAC3Cd,EAAApa,UAAM4Y,YAAW1Y,UAACL,EACtB,EACA0O,EAMA0M,GAAA,SAAgBC,GAEZ,IADA,IAAMC,EAAmB,GAChBzZ,EAAI,EAAGA,EAAIwZ,EAAS9Y,OAAQV,KAC5B8D,KAAKgP,WAAWvJ,QAAQiQ,EAASxZ,KAClCyZ,EAAiBzV,KAAKwV,EAASxZ,IAEvC,OAAOyZ,GACVhB,CAAA,EApIkCjE,IAyJ1BkF,YAAMC,GACf,SAAAD,EAAY3O,GAAgB,IAAX1E,EAAIjC,UAAA1D,OAAA,QAAAuI,IAAA7E,UAAA,GAAAA,UAAA,GAAG,CAAA,EACdwV,EAAmB,WAAf5E,EAAOjK,GAAmBA,EAAM1E,EAMzC,QALIuT,EAAE9G,YACF8G,EAAE9G,YAAyC,iBAApB8G,EAAE9G,WAAW,MACrC8G,EAAE9G,YAAc8G,EAAE9G,YAAc,CAAC,UAAW,YAAa,iBACpD+G,KAAI,SAACzE,GAAa,OAAK0E,GAAmB1E,EAAc,IACxD2E,QAAO,SAAC5E,GAAC,QAAOA,MAEzBwE,EAAAnb,UAAMuM,EAAK6O,IAAE9V,IACjB,CAAC,OAAAsD,EAAAsS,EAAAC,GAAAD,CAAA,EAVuBjB,WC1sBb,SAAC1N,EAAK1E,GAAI,OAAK,IAAIqT,GAAO3O,EAAK1E,EAAK"}
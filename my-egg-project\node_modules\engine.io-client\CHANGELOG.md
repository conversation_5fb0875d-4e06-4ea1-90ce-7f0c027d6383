## [3.5.4](https://github.com/socketio/engine.io-client/compare/3.5.3...3.5.4) (2024-06-18)

This release contains a bump of the `ws` dependency, which includes an important [security fix](https://github.com/websockets/ws/commit/e55e5106f10fcbaac37cfa89759e4cc0d073a52c).

Advisory: https://github.com/advisories/GHSA-3h5v-q93c-6h6q



## [3.5.3](https://github.com/socketio/engine.io-client/compare/3.5.2...3.5.3) (2022-09-07)


### Bug Fixes

* fix usage with vite ([280de36](https://github.com/socketio/engine.io-client/commit/280de368092b17648b59b7467fa49f2425edcd45))



## [3.5.2](https://github.com/socketio/engine.io-client/compare/3.5.1...3.5.2) (2021-05-05)

This release only contains a bump of `xmlhttprequest-ssl`, in order to fix the following vulnerability: https://www.npmjs.com/advisories/1665.

Please note that `engine.io-client` was not directly impacted by this vulnerability, since we are always using `async: true`.


## [3.5.1](https://github.com/socketio/engine.io-client/compare/3.5.0...3.5.1) (2021-03-02)


### Bug Fixes

* replace default nulls in SSL options with undefineds ([d0c551c](https://github.com/socketio/engine.io-client/commit/d0c551cca1e37301e8b28843c8f6e7ad5cf561d3))


# [3.5.0](https://github.com/socketio/engine.io-client/compare/3.4.4...3.5.0) (2020-12-30)


### Bug Fixes

* check the type of the initial packet ([8750356](https://github.com/socketio/engine.io-client/commit/8750356dba5409ba0e1d3a27da6d214118702b3e))



## [3.4.4](https://github.com/socketio/engine.io-client/compare/3.4.3...3.4.4) (2020-09-30)



## [3.4.3](https://github.com/socketio/engine.io-client/compare/3.4.2...3.4.3) (2020-06-04)


### Bug Fixes

* **react-native:** restrict the list of options for the WebSocket object ([e5bc106](https://github.com/socketio/engine.io-client/commit/e5bc1063cc90a7b6262146c7b5338ffff1ff9e5b))



## [3.4.2](https://github.com/socketio/engine.io-client/compare/3.4.1...3.4.2) (2020-05-13)



## [3.4.1](https://github.com/socketio/engine.io-client/compare/3.4.0...3.4.1) (2020-04-17)


### Bug Fixes

* use globalThis polyfill instead of self/global ([357f01d](https://github.com/socketio/engine.io-client/commit/357f01d90448d8565b650377bc7cabb351d991bd))




{"name": "has-binary2", "version": "1.0.3", "description": "A function that takes anything in javascript and returns true if its argument contains binary data.", "dependencies": {"isarray": "2.0.1"}, "devDependencies": {"better-assert": "^1.0.2", "mocha": "^3.2.0", "semistandard": "^9.2.1"}, "scripts": {"checkstyle": "semistandard", "test": "npm run checkstyle && mocha --bail"}, "files": ["index.js"], "author": "<PERSON>", "license": "MIT"}
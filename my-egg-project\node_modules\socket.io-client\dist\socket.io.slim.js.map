{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///socket.io.slim.js", "webpack:///webpack/bootstrap 7863bcd206341bc7952a", "webpack:///./lib/index.js", "webpack:///./lib/url.js", "webpack:///./~/parseuri/index.js", "webpack:///./support/noop.js", "webpack:///./~/socket.io-parser/index.js", "webpack:///./~/component-emitter/index.js", "webpack:///./~/socket.io-parser/binary.js", "webpack:///./~/isarray/index.js", "webpack:///./~/socket.io-parser/is-buffer.js", "webpack:///./lib/manager.js", "webpack:///./~/engine.io-client/lib/index.js", "webpack:///./~/engine.io-client/lib/socket.js", "webpack:///./~/engine.io-client/lib/transports/index.js", "webpack:///./~/engine.io-client/lib/xmlhttprequest.js", "webpack:///./~/has-cors/index.js", "webpack:///./~/engine.io-client/lib/globalThis.browser.js", "webpack:///./~/engine.io-client/lib/transports/polling-xhr.js", "webpack:///./~/engine.io-client/lib/transports/polling.js", "webpack:///./~/engine.io-client/lib/transport.js", "webpack:///./~/engine.io-parser/lib/browser.js", "webpack:///./~/engine.io-parser/lib/keys.js", "webpack:///./~/has-binary2/index.js", "webpack:///./~/arraybuffer.slice/index.js", "webpack:///./~/after/index.js", "webpack:///./~/engine.io-parser/lib/utf8.js", "webpack:///./~/engine.io-parser/~/base64-arraybuffer/lib/base64-arraybuffer.js", "webpack:///./~/blob/index.js", "webpack:///./~/parseqs/index.js", "webpack:///./~/component-inherit/index.js", "webpack:///./~/yeast/index.js", "webpack:///./~/engine.io-client/lib/transports/polling-jsonp.js", "webpack:///./~/engine.io-client/lib/transports/websocket.js", "webpack:///./~/indexof/index.js", "webpack:///./lib/socket.js", "webpack:///./~/to-array/index.js", "webpack:///./lib/on.js", "webpack:///./~/component-bind/index.js", "webpack:///./~/backo2/index.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "modules", "__webpack_require__", "moduleId", "installedModules", "id", "loaded", "call", "m", "c", "p", "lookup", "uri", "opts", "_typeof", "undefined", "io", "parsed", "url", "source", "path", "sameNamespace", "cache", "nsps", "newConnection", "forceNew", "multiplex", "Manager", "query", "socket", "Symbol", "iterator", "obj", "constructor", "prototype", "parser", "managers", "protocol", "connect", "Socket", "loc", "location", "host", "char<PERSON>t", "test", "parseuri", "port", "ipv6", "indexOf", "href", "pathNames", "regx", "names", "replace", "split", "substr", "length", "splice", "query<PERSON><PERSON>", "data", "$0", "$1", "$2", "re", "parts", "str", "src", "b", "e", "substring", "exec", "i", "authority", "ipv6uri", "Encoder", "encodeAsString", "type", "BINARY_EVENT", "BINARY_ACK", "attachments", "nsp", "payload", "tryStringify", "ERROR_PACKET", "JSON", "stringify", "encodeAsBinary", "callback", "writeEncoding", "bloblessData", "deconstruction", "binary", "deconstructPacket", "pack", "packet", "buffers", "unshift", "removeBlobs", "Decoder", "reconstructor", "decodeString", "Number", "types", "error", "buf", "Error", "next", "try<PERSON><PERSON><PERSON>", "isPayloadValid", "ERROR", "isArray", "parse", "BinaryReconstructor", "reconPack", "msg", "Emitter", "isBuf", "CONNECT", "DISCONNECT", "EVENT", "ACK", "encode", "encoding", "add", "emit", "base64", "takeBinaryData", "destroy", "finishedReconstruction", "binData", "push", "reconstructPacket", "mixin", "key", "on", "addEventListener", "event", "fn", "_callbacks", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "callbacks", "cb", "args", "Array", "slice", "len", "listeners", "hasListeners", "_deconstructPacket", "placeholder", "_placeholder", "num", "newData", "Date", "_reconstructPacket", "toString", "Object", "withNativeBlob", "Blob", "withNativeFile", "File", "packetData", "_removeBlobs", "cur<PERSON><PERSON>", "containingObject", "pendingBlobs", "fileReader", "FileReader", "onload", "result", "readAsA<PERSON>y<PERSON><PERSON>er", "arr", "with<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "subs", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "Backoff", "min", "max", "jitter", "timeout", "readyState", "connecting", "lastPing", "packetBuffer", "_parser", "encoder", "decoder", "autoConnect", "open", "eio", "bind", "has", "hasOwnProperty", "emitAll", "updateSocketIds", "generateId", "engine", "v", "_reconnection", "_reconnectionAttempts", "_reconnectionDelay", "setMin", "_randomizationFactor", "setJitter", "_reconnectionDelayMax", "setMax", "_timeout", "maybeReconnectOnOpen", "reconnecting", "attempts", "reconnect", "self", "skipReconnect", "openSub", "onopen", "errorSub", "cleanup", "err", "timer", "setTimeout", "close", "clearTimeout", "onping", "onpong", "ondata", "ondecoded", "onerror", "onConnecting", "index", "encodedPackets", "write", "options", "processPacketQueue", "shift", "subsLength", "sub", "disconnect", "reset", "onclose", "reason", "delay", "duration", "onreconnect", "attempt", "hostname", "secure", "agent", "parseqs", "decode", "upgrade", "forceJSONP", "jsonp", "forceBase64", "enablesXDR", "withCredentials", "timestampParam", "timestampRequests", "transports", "transportOptions", "writeBuffer", "prevBufferLen", "policyPort", "rememberUpgrade", "binaryType", "onlyBinaryUpgrades", "perMessageDeflate", "threshold", "pfx", "passphrase", "cert", "ca", "ciphers", "rejectUnauthorized", "forceNode", "isReactNative", "navigator", "product", "toLowerCase", "extraHeaders", "keys", "localAddress", "upgrades", "pingInterval", "pingTimeout", "pingIntervalTimer", "pingTimeoutTimer", "clone", "o", "priorWebsocketSuccess", "Transport", "createTransport", "name", "EIO", "transport", "sid", "requestTimeout", "protocols", "setTransport", "onDrain", "onPacket", "onError", "onClose", "probe", "onTransportOpen", "upgradeLosesBinary", "supportsBinary", "failed", "send", "upgrading", "pause", "flush", "freezeTransport", "onTransportClose", "onupgrade", "to", "onOpen", "l", "onHandshake", "setPing", "code", "filterUpgrades", "onHeartbeat", "ping", "sendPacket", "writable", "compress", "cleanupAndClose", "waitForUpgrade", "desc", "filteredUpgrades", "j", "polling", "xhr", "xd", "xs", "isSSL", "xdomain", "xscheme", "XMLHttpRequest", "XHR", "JSONP", "websocket", "hasCORS", "globalThis", "XDomainRequest", "concat", "join", "window", "Function", "empty", "Polling", "Request", "method", "async", "isBinary", "create", "unload<PERSON><PERSON><PERSON>", "requests", "abort", "inherit", "request", "doWrite", "req", "sendXhr", "doPoll", "onData", "pollXhr", "setDisableHeaderCheck", "setRequestHeader", "hasXDR", "onLoad", "responseText", "onreadystatechange", "contentType", "getResponseHeader", "responseType", "status", "document", "requestsCount", "onSuccess", "fromError", "response", "attachEvent", "terminationEvent", "hasXHR2", "yeast", "doOpen", "poll", "onPause", "total", "decodePayload", "doClose", "packets", "callbackfn", "encodePayload", "schema", "b64", "description", "decodePacket", "encodeBase64Object", "message", "encode<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encodeBase64Packet", "contentArray", "Uint8Array", "result<PERSON><PERSON><PERSON>", "byteLength", "encodeBlobAsArrayBuffer", "fr", "encodePacket", "encodeBlob", "dontSendBlobs", "blob", "tryDecode", "utf8", "strict", "map", "ary", "each", "done", "after", "eachWithIndex", "el", "base64encoder", "hasBinary", "sliceBuffer", "isAndroid", "userAgent", "isPhantomJS", "pong", "noop", "packetslist", "utf8encode", "encoded", "String", "readAsDataURL", "b64data", "fromCharCode", "typed", "basic", "btoa", "utf8decode", "decodeBase64Packet", "asArray", "rest", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encodeOne", "doneCallback", "encodePayloadAsBlob", "encodePayloadAsArrayBuffer", "results", "decodePayloadAsBinary", "n", "chr", "ret", "totalLength", "reduce", "acc", "resultArray", "bufferIndex", "for<PERSON>ach", "isString", "ab", "view", "charCodeAt", "lenStr", "parseInt", "binaryIdentifier", "size", "lengthAry", "bufferTail", "tailArray", "msg<PERSON><PERSON>th", "toJSON", "arraybuffer", "start", "end", "bytes", "abv", "ii", "count", "err_cb", "proxy", "bail", "ucs2decode", "string", "value", "extra", "output", "counter", "ucs2encode", "array", "stringFromCharCode", "checkScalarValue", "codePoint", "toUpperCase", "createByte", "encodeCodePoint", "symbol", "codePoints", "byteString", "readContinuationByte", "byteIndex", "byteCount", "continuationByte", "byteArray", "decodeSymbol", "byte1", "byte2", "byte3", "byte4", "tmp", "version", "chars", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "mapArrayBufferViews", "chunk", "copy", "set", "byteOffset", "BlobBuilderConstructor", "bb", "BlobBuilder", "part", "append", "getBlob", "BlobConstructor", "WebKitBlobBuilder", "MSBlobBuilder", "MozBlobBuilder", "blobSupported", "a", "blobSupportsArrayBufferView", "blobBuilderSupported", "encodeURIComponent", "qs", "qry", "pairs", "pair", "decodeURIComponent", "alphabet", "Math", "floor", "decoded", "now", "prev", "seed", "JSONPPolling", "___eio", "script", "rNewline", "rEscapedNewline", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "form", "iframe", "createElement", "insertAt", "getElementsByTagName", "insertBefore", "head", "body", "append<PERSON><PERSON><PERSON>", "isUAgecko", "complete", "initIframe", "html", "iframeId", "area", "className", "style", "position", "top", "left", "target", "setAttribute", "action", "submit", "WS", "usingBrowserWebSocket", "BrowserWebSocket", "WebSocketImpl", "NodeWebSocket", "WebSocket", "MozWebSocket", "check", "headers", "ws", "supports", "addEventListeners", "onmessage", "ev", "json", "ids", "acks", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "connected", "disconnected", "flags", "toArray", "hasBin", "events", "connect_error", "connect_timeout", "reconnect_attempt", "reconnect_failed", "reconnect_error", "subEvents", "pop", "onpacket", "rootNamespaceError", "onconnect", "onevent", "onack", "ondisconnect", "ack", "sent", "emitBuffered", "list", "ms", "factor", "pow", "rand", "random", "deviation"], "mappings": "CAAA,SAAAA,EAAAC,GACA,gBAAAC,UAAA,gBAAAC,QACAA,OAAAD,QAAAD,IACA,kBAAAG,gBAAAC,IACAD,UAAAH,GACA,gBAAAC,SACAA,QAAA,GAAAD,IAEAD,EAAA,GAAAC,KACCK,KAAA,WACD,MCAgB,UAAUC,GCN1B,QAAAC,GAAAC,GAGA,GAAAC,EAAAD,GACA,MAAAC,GAAAD,GAAAP,OAGA,IAAAC,GAAAO,EAAAD,IACAP,WACAS,GAAAF,EACAG,QAAA,EAUA,OANAL,GAAAE,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAS,QAAA,EAGAT,EAAAD,QAvBA,GAAAQ,KAqCA,OATAF,GAAAM,EAAAP,EAGAC,EAAAO,EAAAL,EAGAF,EAAAQ,EAAA,GAGAR,EAAA,KDgBM,SAAUL,EAAQD,EAASM,GAEhC,YErBD,SAASS,GAAQC,EAAKC,GACD,YAAf,mBAAOD,GAAP,YAAAE,EAAOF,MACTC,EAAOD,EACPA,EAAMG,QAGRF,EAAOA,KAEP,IAQIG,GARAC,EAASC,EAAIN,GACbO,EAASF,EAAOE,OAChBd,EAAKY,EAAOZ,GACZe,EAAOH,EAAOG,KACdC,EAAgBC,EAAMjB,IAAOe,IAAQE,GAAMjB,GAAIkB,KAC/CC,EAAgBX,EAAKY,UAAYZ,EAAK,0BACtB,IAAUA,EAAKa,WAAaL,CAiBhD,OAbIG,GAEFR,EAAKW,EAAQR,EAAQN,IAEhBS,EAAMjB,KAETiB,EAAMjB,GAAMsB,EAAQR,EAAQN,IAE9BG,EAAKM,EAAMjB,IAETY,EAAOW,QAAUf,EAAKe,QACxBf,EAAKe,MAAQX,EAAOW,OAEfZ,EAAGa,OAAOZ,EAAOG,KAAMP,GFR/B,GAAIC,GAA4B,kBAAXgB,SAAoD,gBAApBA,QAAOC,SAAwB,SAAUC,GAAO,aAAcA,IAAS,SAAUA,GAAO,MAAOA,IAAyB,kBAAXF,SAAyBE,EAAIC,cAAgBH,QAAUE,IAAQF,OAAOI,UAAY,eAAkBF,IErDnQd,EAAMhB,EAAQ,GACdiC,EAASjC,EAAQ,GACjByB,EAAUzB,EAAQ,EACVA,GAAQ,GAAS,mBAM7BL,GAAOD,QAAUA,EAAUe,CAM3B,IAAIW,GAAQ1B,EAAQwC,WAuDpBxC,GAAQyC,SAAWF,EAAOE,SAS1BzC,EAAQ0C,QAAU3B,EAQlBf,EAAQ+B,QAAUzB,EAAQ,GAC1BN,EAAQ2C,OAASrC,EAAQ,KF8DnB,SAAUL,EAAQD,EAASM,GAEhC,YGtID,SAASgB,GAAKN,EAAK4B,GACjB,GAAIR,GAAMpB,CAGV4B,GAAMA,GAA4B,mBAAbC,WAA4BA,SAC7C,MAAQ7B,IAAKA,EAAM4B,EAAIH,SAAW,KAAOG,EAAIE,MAG7C,gBAAoB9B,KAClB,MAAQA,EAAI+B,OAAO,KAEnB/B,EADE,MAAQA,EAAI+B,OAAO,GACfH,EAAIH,SAAWzB,EAEf4B,EAAIE,KAAO9B,GAIhB,sBAAsBgC,KAAKhC,KAG5BA,EADE,mBAAuB4B,GACnBA,EAAIH,SAAW,KAAOzB,EAEtB,WAAaA,GAMvBoB,EAAMa,EAASjC,IAIZoB,EAAIc,OACH,cAAcF,KAAKZ,EAAIK,UACzBL,EAAIc,KAAO,KACF,eAAeF,KAAKZ,EAAIK,YACjCL,EAAIc,KAAO,QAIfd,EAAIZ,KAAOY,EAAIZ,MAAQ,GAEvB,IAAI2B,GAAOf,EAAIU,KAAKM,QAAQ,QAAS,EACjCN,EAAOK,EAAO,IAAMf,EAAIU,KAAO,IAAMV,EAAIU,IAO7C,OAJAV,GAAI3B,GAAK2B,EAAIK,SAAW,MAAQK,EAAO,IAAMV,EAAIc,KAEjDd,EAAIiB,KAAOjB,EAAIK,SAAW,MAAQK,GAAQF,GAAOA,EAAIM,OAASd,EAAIc,KAAO,GAAM,IAAMd,EAAIc,MAElFd,EApET,GAAIa,GAAW3C,EAAQ,EACXA,GAAQ,GAAS,uBAM7BL,GAAOD,QAAUsB,GHgOX,SAAUrB,EAAQD,GIjMxB,QAAAsD,GAAAlB,EAAAZ,GACA,GAAA+B,GAAA,WACAC,EAAAhC,EAAAiC,QAAAF,EAAA,KAAAG,MAAA,IASA,OAPA,KAAAlC,EAAAmC,OAAA,UAAAnC,EAAAoC,QACAJ,EAAAK,OAAA,KAEA,KAAArC,EAAAmC,OAAAnC,EAAAoC,OAAA,MACAJ,EAAAK,OAAAL,EAAAI,OAAA,KAGAJ,EAGA,QAAAM,GAAA9C,EAAAgB,GACA,GAAA+B,KAQA,OANA/B,GAAAyB,QAAA,qCAAAO,EAAAC,EAAAC,GACAD,IACAF,EAAAE,GAAAC,KAIAH,EA3DA,GAAAI,GAAA,0OAEAC,GACA,iIAGAnE,GAAAD,QAAA,SAAAqE,GACA,GAAAC,GAAAD,EACAE,EAAAF,EAAAjB,QAAA,KACAoB,EAAAH,EAAAjB,QAAA,IAEAmB,KAAA,GAAAC,IAAA,IACAH,IAAAI,UAAA,EAAAF,GAAAF,EAAAI,UAAAF,EAAAC,GAAAf,QAAA,UAAwEY,EAAAI,UAAAD,EAAAH,EAAAT,QAOxE,KAJA,GAAAhD,GAAAuD,EAAAO,KAAAL,GAAA,IACArD,KACA2D,EAAA,GAEAA,KACA3D,EAAAoD,EAAAO,IAAA/D,EAAA+D,IAAA,EAaA,OAVAJ,KAAA,GAAAC,IAAA,IACAxD,EAAAO,OAAA+C,EACAtD,EAAA8B,KAAA9B,EAAA8B,KAAA2B,UAAA,EAAAzD,EAAA8B,KAAAc,OAAA,GAAAH,QAAA,KAAwE,KACxEzC,EAAA4D,UAAA5D,EAAA4D,UAAAnB,QAAA,QAAAA,QAAA,QAAAA,QAAA,KAAkF,KAClFzC,EAAA6D,SAAA,GAGA7D,EAAAsC,YAAAtC,IAAA,MACAA,EAAA8C,WAAA9C,IAAA,OAEAA,IJ8QM,SAAUf,EAAQD,GAEvB,YKvTDC,GAAOD,QAAU,WAAc,MAAO,gBL+ThC,SAAUC,EAAQD,EAASM,GM/MjC,QAAAwE,MAiCA,QAAAC,GAAA3C,GAGA,GAAAiC,GAAA,GAAAjC,EAAA4C,IAmBA,IAhBAhF,EAAAiF,eAAA7C,EAAA4C,MAAAhF,EAAAkF,aAAA9C,EAAA4C,OACAX,GAAAjC,EAAA+C,YAAA,KAKA/C,EAAAgD,KAAA,MAAAhD,EAAAgD,MACAf,GAAAjC,EAAAgD,IAAA,KAIA,MAAAhD,EAAA3B,KACA4D,GAAAjC,EAAA3B,IAIA,MAAA2B,EAAA2B,KAAA,CACA,GAAAsB,GAAAC,EAAAlD,EAAA2B,KACA,IAAAsB,KAAA,EAGA,MAAAE,EAFAlB,IAAAgB,EAOA,MAAAhB,GAGA,QAAAiB,GAAAjB,GACA,IACA,MAAAmB,MAAAC,UAAApB,GACG,MAAAG,GACH,UAcA,QAAAkB,GAAAtD,EAAAuD,GAEA,QAAAC,GAAAC,GACA,GAAAC,GAAAC,EAAAC,kBAAAH,GACAI,EAAAlB,EAAAe,EAAAI,QACAC,EAAAL,EAAAK,OAEAA,GAAAC,QAAAH,GACAN,EAAAQ,GAGAJ,EAAAM,YAAAjE,EAAAwD,GAUA,QAAAU,KACAlG,KAAAmG,cAAA,KAsDA,QAAAC,GAAAnC,GACA,GAAAM,GAAA,EAEA7D,GACAkE,KAAAyB,OAAApC,EAAAtB,OAAA,IAGA,UAAA/C,EAAA0G,MAAA5F,EAAAkE,MACA,MAAA2B,GAAA,uBAAA7F,EAAAkE,KAIA,IAAAhF,EAAAiF,eAAAnE,EAAAkE,MAAAhF,EAAAkF,aAAApE,EAAAkE,KAAA,CAEA,IADA,GAAA4B,GAAA,GACA,MAAAvC,EAAAtB,SAAA4B,KACAiC,GAAAvC,EAAAtB,OAAA4B,GACAA,GAAAN,EAAAT,UAEA,GAAAgD,GAAAH,OAAAG,IAAA,MAAAvC,EAAAtB,OAAA4B,GACA,SAAAkC,OAAA,sBAEA/F,GAAAqE,YAAAsB,OAAAG,GAIA,SAAAvC,EAAAtB,OAAA4B,EAAA,GAEA,IADA7D,EAAAsE,IAAA,KACAT,GAAA,CACA,GAAA9D,GAAAwD,EAAAtB,OAAA4B,EACA,UAAA9D,EAAA,KAEA,IADAC,EAAAsE,KAAAvE,EACA8D,IAAAN,EAAAT,OAAA,UAGA9C,GAAAsE,IAAA,GAIA,IAAA0B,GAAAzC,EAAAtB,OAAA4B,EAAA,EACA,SAAAmC,GAAAL,OAAAK,MAAA,CAEA,IADAhG,EAAAL,GAAA,KACAkE,GAAA,CACA,GAAA9D,GAAAwD,EAAAtB,OAAA4B,EACA,UAAA9D,GAAA4F,OAAA5F,MAAA,GACA8D,CACA,OAGA,GADA7D,EAAAL,IAAA4D,EAAAtB,OAAA4B,GACAA,IAAAN,EAAAT,OAAA,MAEA9C,EAAAL,GAAAgG,OAAA3F,EAAAL,IAIA,GAAA4D,EAAAtB,SAAA4B,GAAA,CACA,GAAAU,GAAA0B,EAAA1C,EAAAV,OAAAgB,IACAqC,EAAA3B,KAAA,IAAAvE,EAAAkE,OAAAhF,EAAAiH,OAAAC,EAAA7B,GACA,KAAA2B,EAGA,MAAAL,GAAA,kBAFA7F,GAAAiD,KAAAsB,EAOA,MAAAvE,GAGA,QAAAiG,GAAA1C,GACA,IACA,MAAAmB,MAAA2B,MAAA9C,GACG,MAAAG,GACH,UA0BA,QAAA4C,GAAAlB,GACA9F,KAAAiH,UAAAnB,EACA9F,KAAA+F,WAkCA,QAAAQ,GAAAW,GACA,OACAtC,KAAAhF,EAAAiH,MACAlD,KAAA,iBAAAuD,GAvZA,GACAC,IADAjH,EAAA,uBACAA,EAAA,IACAyF,EAAAzF,EAAA,GACA4G,EAAA5G,EAAA,GACAkH,EAAAlH,EAAA,EAQAN,GAAAyC,SAAA,EAQAzC,EAAA0G,OACA,UACA,aACA,QACA,MACA,QACA,eACA,cASA1G,EAAAyH,QAAA,EAQAzH,EAAA0H,WAAA,EAQA1H,EAAA2H,MAAA,EAQA3H,EAAA4H,IAAA,EAQA5H,EAAAiH,MAAA,EAQAjH,EAAAiF,aAAA,EAQAjF,EAAAkF,WAAA,EAQAlF,EAAA8E,UAQA9E,EAAAsG,SAUA,IAAAf,GAAAvF,EAAAiH,MAAA,gBAYAnC,GAAAxC,UAAAuF,OAAA,SAAAzF,EAAAuD,GAGA,GAAA3F,EAAAiF,eAAA7C,EAAA4C,MAAAhF,EAAAkF,aAAA9C,EAAA4C,KACAU,EAAAtD,EAAAuD,OACG,CACH,GAAAmC,GAAA/C,EAAA3C,EACAuD,IAAAmC,MA8FAP,EAAAjB,EAAAhE,WAUAgE,EAAAhE,UAAAyF,IAAA,SAAA3F,GACA,GAAA8D,EACA,oBAAA9D,GACA8D,EAAAM,EAAApE,GACApC,EAAAiF,eAAAiB,EAAAlB,MAAAhF,EAAAkF,aAAAgB,EAAAlB,MACA5E,KAAAmG,cAAA,GAAAa,GAAAlB,GAGA,IAAA9F,KAAAmG,cAAAc,UAAAlC,aACA/E,KAAA4H,KAAA,UAAA9B,IAGA9F,KAAA4H,KAAA,UAAA9B,OAEG,KAAAsB,EAAApF,OAAA6F,OAWH,SAAApB,OAAA,iBAAAzE,EAVA,KAAAhC,KAAAmG,cACA,SAAAM,OAAA,mDAEAX,GAAA9F,KAAAmG,cAAA2B,eAAA9F,GACA8D,IACA9F,KAAAmG,cAAA,KACAnG,KAAA4H,KAAA,UAAA9B,MAkGAI,EAAAhE,UAAA6F,QAAA,WACA/H,KAAAmG,eACAnG,KAAAmG,cAAA6B,0BA6BAhB,EAAA9E,UAAA4F,eAAA,SAAAG,GAEA,GADAjI,KAAA+F,QAAAmC,KAAAD,GACAjI,KAAA+F,QAAAvC,SAAAxD,KAAAiH,UAAAlC,YAAA,CACA,GAAAe,GAAAH,EAAAwC,kBAAAnI,KAAAiH,UAAAjH,KAAA+F,QAEA,OADA/F,MAAAgI,yBACAlC,EAEA,aASAkB,EAAA9E,UAAA8F,uBAAA,WACAhI,KAAAiH,UAAA,KACAjH,KAAA+F,aN+UM,SAAUlG,EAAQD,EAASM,GOttBjC,QAAAiH,GAAAnF,GACA,GAAAA,EAAA,MAAAoG,GAAApG,GAWA,QAAAoG,GAAApG,GACA,OAAAqG,KAAAlB,GAAAjF,UACAF,EAAAqG,GAAAlB,EAAAjF,UAAAmG,EAEA,OAAArG,GAzBAnC,EAAAD,QAAAuH,EAqCAA,EAAAjF,UAAAoG,GACAnB,EAAAjF,UAAAqG,iBAAA,SAAAC,EAAAC,GAIA,MAHAzI,MAAA0I,WAAA1I,KAAA0I,gBACA1I,KAAA0I,WAAA,IAAAF,GAAAxI,KAAA0I,WAAA,IAAAF,QACAN,KAAAO,GACAzI,MAaAmH,EAAAjF,UAAAyG,KAAA,SAAAH,EAAAC,GACA,QAAAH,KACAtI,KAAA4I,IAAAJ,EAAAF,GACAG,EAAAI,MAAA7I,KAAA8I,WAKA,MAFAR,GAAAG,KACAzI,KAAAsI,GAAAE,EAAAF,GACAtI,MAaAmH,EAAAjF,UAAA0G,IACAzB,EAAAjF,UAAA6G,eACA5B,EAAAjF,UAAA8G,mBACA7B,EAAAjF,UAAA+G,oBAAA,SAAAT,EAAAC,GAIA,GAHAzI,KAAA0I,WAAA1I,KAAA0I,eAGA,GAAAI,UAAAtF,OAEA,MADAxD,MAAA0I,cACA1I,IAIA,IAAAkJ,GAAAlJ,KAAA0I,WAAA,IAAAF,EACA,KAAAU,EAAA,MAAAlJ,KAGA,OAAA8I,UAAAtF,OAEA,aADAxD,MAAA0I,WAAA,IAAAF,GACAxI,IAKA,QADAmJ,GACA5E,EAAA,EAAiBA,EAAA2E,EAAA1F,OAAsBe,IAEvC,GADA4E,EAAAD,EAAA3E,GACA4E,IAAAV,GAAAU,EAAAV,OAAA,CACAS,EAAAzF,OAAAc,EAAA,EACA,OAUA,MAJA,KAAA2E,EAAA1F,cACAxD,MAAA0I,WAAA,IAAAF,GAGAxI,MAWAmH,EAAAjF,UAAA0F,KAAA,SAAAY,GACAxI,KAAA0I,WAAA1I,KAAA0I,cAKA,QAHAU,GAAA,GAAAC,OAAAP,UAAAtF,OAAA,GACA0F,EAAAlJ,KAAA0I,WAAA,IAAAF,GAEAjE,EAAA,EAAiBA,EAAAuE,UAAAtF,OAAsBe,IACvC6E,EAAA7E,EAAA,GAAAuE,UAAAvE,EAGA,IAAA2E,EAAA,CACAA,IAAAI,MAAA,EACA,QAAA/E,GAAA,EAAAgF,EAAAL,EAAA1F,OAA2Ce,EAAAgF,IAAShF,EACpD2E,EAAA3E,GAAAsE,MAAA7I,KAAAoJ,GAIA,MAAApJ,OAWAmH,EAAAjF,UAAAsH,UAAA,SAAAhB,GAEA,MADAxI,MAAA0I,WAAA1I,KAAA0I,eACA1I,KAAA0I,WAAA,IAAAF,QAWArB,EAAAjF,UAAAuH,aAAA,SAAAjB,GACA,QAAAxI,KAAAwJ,UAAAhB,GAAAhF,SP6uBM,SAAU3D,EAAQD,EAASM,GQ33BjC,QAAAwJ,GAAA/F,EAAAoC,GACA,IAAApC,EAAA,MAAAA,EAEA,IAAAyD,EAAAzD,GAAA,CACA,GAAAgG,IAAuBC,cAAA,EAAAC,IAAA9D,EAAAvC,OAEvB,OADAuC,GAAAmC,KAAAvE,GACAgG,EACG,GAAA7C,EAAAnD,GAAA,CAEH,OADAmG,GAAA,GAAAT,OAAA1F,EAAAH,QACAe,EAAA,EAAmBA,EAAAZ,EAAAH,OAAiBe,IACpCuF,EAAAvF,GAAAmF,EAAA/F,EAAAY,GAAAwB,EAEA,OAAA+D,GACG,mBAAAnG,kBAAAoG,OAAA,CACH,GAAAD,KACA,QAAAzB,KAAA1E,GACAmG,EAAAzB,GAAAqB,EAAA/F,EAAA0E,GAAAtC,EAEA,OAAA+D,GAEA,MAAAnG,GAkBA,QAAAqG,GAAArG,EAAAoC,GACA,IAAApC,EAAA,MAAAA,EAEA,IAAAA,KAAAiG,aACA,MAAA7D,GAAApC,EAAAkG,IACG,IAAA/C,EAAAnD,GACH,OAAAY,GAAA,EAAmBA,EAAAZ,EAAAH,OAAiBe,IACpCZ,EAAAY,GAAAyF,EAAArG,EAAAY,GAAAwB,OAEG,oBAAApC,GACH,OAAA0E,KAAA1E,GACAA,EAAA0E,GAAA2B,EAAArG,EAAA0E,GAAAtC,EAIA,OAAApC,GA9EA,GAAAmD,GAAA5G,EAAA,GACAkH,EAAAlH,EAAA,GACA+J,EAAAC,OAAAhI,UAAA+H,SACAE,EAAA,kBAAAC,OAAA,mBAAAA,OAAA,6BAAAH,EAAA1J,KAAA6J,MACAC,EAAA,kBAAAC,OAAA,mBAAAA,OAAA,6BAAAL,EAAA1J,KAAA+J,KAYA1K,GAAAgG,kBAAA,SAAAE,GACA,GAAAC,MACAwE,EAAAzE,EAAAnC,KACAkC,EAAAC,CAGA,OAFAD,GAAAlC,KAAA+F,EAAAa,EAAAxE,GACAF,EAAAd,YAAAgB,EAAAvC,QACUsC,OAAAD,EAAAE,YAmCVnG,EAAAuI,kBAAA,SAAArC,EAAAC,GAGA,MAFAD,GAAAnC,KAAAqG,EAAAlE,EAAAnC,KAAAoC,GACAD,EAAAf,YAAAhE,OACA+E,GA+BAlG,EAAAqG,YAAA,SAAAtC,EAAA4B,GACA,QAAAiF,GAAAxI,EAAAyI,EAAAC,GACA,IAAA1I,EAAA,MAAAA,EAGA,IAAAmI,GAAAnI,YAAAoI,OACAC,GAAArI,YAAAsI,MAAA,CACAK,GAGA,IAAAC,GAAA,GAAAC,WACAD,GAAAE,OAAA,WACAJ,EACAA,EAAAD,GAAAzK,KAAA+K,OAGAtF,EAAAzF,KAAA+K,SAIAJ,GACApF,EAAAE,IAIAmF,EAAAI,kBAAAhJ,OACK,IAAA8E,EAAA9E,GACL,OAAAuC,GAAA,EAAqBA,EAAAvC,EAAAwB,OAAgBe,IACrCiG,EAAAxI,EAAAuC,KAAAvC,OAEK,oBAAAA,KAAAoF,EAAApF,GACL,OAAAqG,KAAArG,GACAwI,EAAAxI,EAAAqG,KAAArG,GAKA,GAAA2I,GAAA,EACAlF,EAAA9B,CACA6G,GAAA/E,GACAkF,GACApF,EAAAE,KRm6BM,SAAU5F,EAAQD,GS7iCxB,GAAAqK,MAAiBA,QAEjBpK,GAAAD,QAAAyJ,MAAAvC,SAAA,SAAAmE,GACA,wBAAAhB,EAAA1J,KAAA0K,KTqjCM,SAAUpL,EAAQD,GUxiCxB,QAAAwH,GAAApF,GACA,MAAAkJ,IAAAC,OAAAC,SAAApJ,IACAqJ,IAAArJ,YAAAsJ,cAAAC,EAAAvJ,IAjBAnC,EAAAD,QAAAwH,CAEA,IAAA8D,GAAA,kBAAAC,SAAA,kBAAAA,QAAAC,SACAC,EAAA,kBAAAC,aAEAC,EAAA,SAAAvJ,GACA,wBAAAsJ,aAAAC,OAAAD,YAAAC,OAAAvJ,KAAAwJ,iBAAAF,eV2kCM,SAAUzL,EAAQD,EAASM,GAEhC,YWjjCD,SAASyB,GAASf,EAAKC,GACrB,KAAMb,eAAgB2B,IAAU,MAAO,IAAIA,GAAQf,EAAKC,EACpDD,IAAQ,+BAAoBA,GAApB,YAAAE,EAAoBF,MAC9BC,EAAOD,EACPA,EAAMG,QAERF,EAAOA,MAEPA,EAAKO,KAAOP,EAAKO,MAAQ,aACzBpB,KAAKuB,QACLvB,KAAKyL,QACLzL,KAAKa,KAAOA,EACZb,KAAK0L,aAAa7K,EAAK6K,gBAAiB,GACxC1L,KAAK2L,qBAAqB9K,EAAK8K,sBAAwBC,KACvD5L,KAAK6L,kBAAkBhL,EAAKgL,mBAAqB,KACjD7L,KAAK8L,qBAAqBjL,EAAKiL,sBAAwB,KACvD9L,KAAK+L,oBAAoBlL,EAAKkL,qBAAuB,IACrD/L,KAAKgM,QAAU,GAAIC,IACjBC,IAAKlM,KAAK6L,oBACVM,IAAKnM,KAAK8L,uBACVM,OAAQpM,KAAK+L,wBAEf/L,KAAKqM,QAAQ,MAAQxL,EAAKwL,QAAU,IAAQxL,EAAKwL,SACjDrM,KAAKsM,WAAa,SAClBtM,KAAKY,IAAMA,EACXZ,KAAKuM,cACLvM,KAAKwM,SAAW,KAChBxM,KAAK0H,UAAW,EAChB1H,KAAKyM,eACL,IAAIC,GAAU7L,EAAKsB,QAAUA,CAC7BnC,MAAK2M,QAAU,GAAID,GAAQhI,QAC3B1E,KAAK4M,QAAU,GAAIF,GAAQxG,QAC3BlG,KAAK6M,YAAchM,EAAKgM,eAAgB,EACpC7M,KAAK6M,aAAa7M,KAAK8M,OXkhC5B,GAAIhM,GAA4B,kBAAXgB,SAAoD,gBAApBA,QAAOC,SAAwB,SAAUC,GAAO,aAAcA,IAAS,SAAUA,GAAO,MAAOA,IAAyB,kBAAXF,SAAyBE,EAAIC,cAAgBH,QAAUE,IAAQF,OAAOI,UAAY,eAAkBF,IWjlCnQ+K,EAAM7M,EAAQ,IACdqC,EAASrC,EAAQ,IACjBiH,EAAUjH,EAAQ,GAClBiC,EAASjC,EAAQ,GACjBoI,EAAKpI,EAAQ,IACb8M,EAAO9M,EAAQ,IAEf8C,GADQ9C,EAAQ,GAAS,4BACfA,EAAQ,KAClB+L,EAAU/L,EAAQ,IAMlB+M,EAAM/C,OAAOhI,UAAUgL,cAM3BrN,GAAOD,QAAU+B,EAoDjBA,EAAQO,UAAUiL,QAAU,WAC1BnN,KAAK4H,KAAKiB,MAAM7I,KAAM8I,UACtB,KAAK,GAAI9D,KAAOhF,MAAKuB,KACf0L,EAAI1M,KAAKP,KAAKuB,KAAMyD,IACtBhF,KAAKuB,KAAKyD,GAAK4C,KAAKiB,MAAM7I,KAAKuB,KAAKyD,GAAM8D,YAWhDnH,EAAQO,UAAUkL,gBAAkB,WAClC,IAAK,GAAIpI,KAAOhF,MAAKuB,KACf0L,EAAI1M,KAAKP,KAAKuB,KAAMyD,KACtBhF,KAAKuB,KAAKyD,GAAK3E,GAAKL,KAAKqN,WAAWrI,KAa1CrD,EAAQO,UAAUmL,WAAa,SAAUrI,GACvC,OAAgB,MAARA,EAAc,GAAMA,EAAM,KAAQhF,KAAKsN,OAAOjN,IAOxD8G,EAAQxF,EAAQO,WAUhBP,EAAQO,UAAUwJ,aAAe,SAAU6B,GACzC,MAAKzE,WAAUtF,QACfxD,KAAKwN,gBAAkBD,EAChBvN,MAFuBA,KAAKwN,eAarC7L,EAAQO,UAAUyJ,qBAAuB,SAAU4B,GACjD,MAAKzE,WAAUtF,QACfxD,KAAKyN,sBAAwBF,EACtBvN,MAFuBA,KAAKyN,uBAarC9L,EAAQO,UAAU2J,kBAAoB,SAAU0B,GAC9C,MAAKzE,WAAUtF,QACfxD,KAAK0N,mBAAqBH,EAC1BvN,KAAKgM,SAAWhM,KAAKgM,QAAQ2B,OAAOJ,GAC7BvN,MAHuBA,KAAK0N,oBAMrC/L,EAAQO,UAAU6J,oBAAsB,SAAUwB,GAChD,MAAKzE,WAAUtF,QACfxD,KAAK4N,qBAAuBL,EAC5BvN,KAAKgM,SAAWhM,KAAKgM,QAAQ6B,UAAUN,GAChCvN,MAHuBA,KAAK4N,sBAcrCjM,EAAQO,UAAU4J,qBAAuB,SAAUyB,GACjD,MAAKzE,WAAUtF,QACfxD,KAAK8N,sBAAwBP,EAC7BvN,KAAKgM,SAAWhM,KAAKgM,QAAQ+B,OAAOR,GAC7BvN,MAHuBA,KAAK8N,uBAarCnM,EAAQO,UAAUmK,QAAU,SAAUkB,GACpC,MAAKzE,WAAUtF,QACfxD,KAAKgO,SAAWT,EACTvN,MAFuBA,KAAKgO,UAYrCrM,EAAQO,UAAU+L,qBAAuB,YAElCjO,KAAKkO,cAAgBlO,KAAKwN,eAA2C,IAA1BxN,KAAKgM,QAAQmC,UAE3DnO,KAAKoO,aAYTzM,EAAQO,UAAU4K,KAClBnL,EAAQO,UAAUI,QAAU,SAAUmG,EAAI5H,GAExC,IAAKb,KAAKsM,WAAWtJ,QAAQ,QAAS,MAAOhD,KAG7CA,MAAKsN,OAASP,EAAI/M,KAAKY,IAAKZ,KAAKa,KACjC,IAAIgB,GAAS7B,KAAKsN,OACde,EAAOrO,IACXA,MAAKsM,WAAa,UAClBtM,KAAKsO,eAAgB,CAGrB,IAAIC,GAAUjG,EAAGzG,EAAQ,OAAQ,WAC/BwM,EAAKG,SACL/F,GAAMA,MAIJgG,EAAWnG,EAAGzG,EAAQ,QAAS,SAAU8B,GAK3C,GAHA0K,EAAKK,UACLL,EAAK/B,WAAa,SAClB+B,EAAKlB,QAAQ,gBAAiBxJ,GAC1B8E,EAAI,CACN,GAAIkG,GAAM,GAAIlI,OAAM,mBACpBkI,GAAIhL,KAAOA,EACX8E,EAAGkG,OAGHN,GAAKJ,wBAKT,KAAI,IAAUjO,KAAKgO,SAAU,CAC3B,GAAI3B,GAAUrM,KAAKgO,QAGH,KAAZ3B,GACFkC,EAAQxG,SAIV,IAAI6G,GAAQC,WAAW,WAErBN,EAAQxG,UACRlG,EAAOiN,QACPjN,EAAO+F,KAAK,QAAS,WACrByG,EAAKlB,QAAQ,kBAAmBd,IAC/BA,EAEHrM,MAAKyL,KAAKvD,MACRH,QAAS,WACPgH,aAAaH,MAQnB,MAHA5O,MAAKyL,KAAKvD,KAAKqG,GACfvO,KAAKyL,KAAKvD,KAAKuG,GAERzO,MAST2B,EAAQO,UAAUsM,OAAS,WAIzBxO,KAAK0O,UAGL1O,KAAKsM,WAAa,OAClBtM,KAAK4H,KAAK,OAGV,IAAI/F,GAAS7B,KAAKsN,MAClBtN,MAAKyL,KAAKvD,KAAKI,EAAGzG,EAAQ,OAAQmL,EAAKhN,KAAM,YAC7CA,KAAKyL,KAAKvD,KAAKI,EAAGzG,EAAQ,OAAQmL,EAAKhN,KAAM,YAC7CA,KAAKyL,KAAKvD,KAAKI,EAAGzG,EAAQ,OAAQmL,EAAKhN,KAAM,YAC7CA,KAAKyL,KAAKvD,KAAKI,EAAGzG,EAAQ,QAASmL,EAAKhN,KAAM,aAC9CA,KAAKyL,KAAKvD,KAAKI,EAAGzG,EAAQ,QAASmL,EAAKhN,KAAM,aAC9CA,KAAKyL,KAAKvD,KAAKI,EAAGtI,KAAK4M,QAAS,UAAWI,EAAKhN,KAAM,gBASxD2B,EAAQO,UAAU8M,OAAS,WACzBhP,KAAKwM,SAAW,GAAIzC,MACpB/J,KAAKmN,QAAQ,SASfxL,EAAQO,UAAU+M,OAAS,WACzBjP,KAAKmN,QAAQ,OAAQ,GAAIpD,MAAS/J,KAAKwM,WASzC7K,EAAQO,UAAUgN,OAAS,SAAUvL,GACnC3D,KAAK4M,QAAQjF,IAAIhE,IASnBhC,EAAQO,UAAUiN,UAAY,SAAUrJ,GACtC9F,KAAK4H,KAAK,SAAU9B,IAStBnE,EAAQO,UAAUkN,QAAU,SAAUT,GAEpC3O,KAAKmN,QAAQ,QAASwB,IAUxBhN,EAAQO,UAAUL,OAAS,SAAUmD,EAAKnE,GAiBxC,QAASwO,MACDrM,EAAQqL,EAAK9B,WAAY1K,IAC7BwM,EAAK9B,WAAWrE,KAAKrG,GAlBzB,GAAIA,GAAS7B,KAAKuB,KAAKyD,EACvB,KAAKnD,EAAQ,CACXA,EAAS,GAAIU,GAAOvC,KAAMgF,EAAKnE,GAC/Bb,KAAKuB,KAAKyD,GAAOnD,CACjB,IAAIwM,GAAOrO,IACX6B,GAAOyG,GAAG,aAAc+G,GACxBxN,EAAOyG,GAAG,UAAW,WACnBzG,EAAOxB,GAAKgO,EAAKhB,WAAWrI,KAG1BhF,KAAK6M,aAEPwC,IAUJ,MAAOxN,IASTF,EAAQO,UAAU6F,QAAU,SAAUlG,GACpC,GAAIyN,GAAQtM,EAAQhD,KAAKuM,WAAY1K,IAChCyN,GAAOtP,KAAKuM,WAAW9I,OAAO6L,EAAO,GACtCtP,KAAKuM,WAAW/I,QAEpBxD,KAAK8O,SAUPnN,EAAQO,UAAU4D,OAAS,SAAUA,GAEnC,GAAIuI,GAAOrO,IACP8F,GAAOlE,OAAyB,IAAhBkE,EAAOlB,OAAYkB,EAAOd,KAAO,IAAMc,EAAOlE,OAE7DyM,EAAK3G,SAWR2G,EAAK5B,aAAavE,KAAKpC,IATvBuI,EAAK3G,UAAW,EAChB1H,KAAK2M,QAAQlF,OAAO3B,EAAQ,SAAUyJ,GACpC,IAAK,GAAIhL,GAAI,EAAGA,EAAIgL,EAAe/L,OAAQe,IACzC8J,EAAKf,OAAOkC,MAAMD,EAAehL,GAAIuB,EAAO2J,QAE9CpB,GAAK3G,UAAW,EAChB2G,EAAKqB,yBAcX/N,EAAQO,UAAUwN,mBAAqB,WACrC,GAAI1P,KAAKyM,aAAajJ,OAAS,IAAMxD,KAAK0H,SAAU,CAClD,GAAI7B,GAAO7F,KAAKyM,aAAakD,OAC7B3P,MAAK8F,OAAOD,KAUhBlE,EAAQO,UAAUwM,QAAU,WAI1B,IAAK,GADDkB,GAAa5P,KAAKyL,KAAKjI,OAClBe,EAAI,EAAGA,EAAIqL,EAAYrL,IAAK,CACnC,GAAIsL,GAAM7P,KAAKyL,KAAKkE,OACpBE,GAAI9H,UAGN/H,KAAKyM,gBACLzM,KAAK0H,UAAW,EAChB1H,KAAKwM,SAAW,KAEhBxM,KAAK4M,QAAQ7E,WASfpG,EAAQO,UAAU4M,MAClBnN,EAAQO,UAAU4N,WAAa,WAE7B9P,KAAKsO,eAAgB,EACrBtO,KAAKkO,cAAe,EAChB,YAAclO,KAAKsM,YAGrBtM,KAAK0O,UAEP1O,KAAKgM,QAAQ+D,QACb/P,KAAKsM,WAAa,SACdtM,KAAKsN,QAAQtN,KAAKsN,OAAOwB,SAS/BnN,EAAQO,UAAU8N,QAAU,SAAUC,GAGpCjQ,KAAK0O,UACL1O,KAAKgM,QAAQ+D,QACb/P,KAAKsM,WAAa,SAClBtM,KAAK4H,KAAK,QAASqI,GAEfjQ,KAAKwN,gBAAkBxN,KAAKsO,eAC9BtO,KAAKoO,aAUTzM,EAAQO,UAAUkM,UAAY,WAC5B,GAAIpO,KAAKkO,cAAgBlO,KAAKsO,cAAe,MAAOtO,KAEpD,IAAIqO,GAAOrO,IAEX,IAAIA,KAAKgM,QAAQmC,UAAYnO,KAAKyN,sBAEhCzN,KAAKgM,QAAQ+D,QACb/P,KAAKmN,QAAQ,oBACbnN,KAAKkO,cAAe,MACf,CACL,GAAIgC,GAAQlQ,KAAKgM,QAAQmE,UAGzBnQ,MAAKkO,cAAe,CACpB,IAAIU,GAAQC,WAAW,WACjBR,EAAKC,gBAGTD,EAAKlB,QAAQ,oBAAqBkB,EAAKrC,QAAQmC,UAC/CE,EAAKlB,QAAQ,eAAgBkB,EAAKrC,QAAQmC,UAGtCE,EAAKC,eAETD,EAAKvB,KAAK,SAAU6B,GACdA,GAEFN,EAAKH,cAAe,EACpBG,EAAKD,YACLC,EAAKlB,QAAQ,kBAAmBwB,EAAIhL,OAGpC0K,EAAK+B,kBAGRF,EAEHlQ,MAAKyL,KAAKvD,MACRH,QAAS,WACPgH,aAAaH,QAYrBjN,EAAQO,UAAUkO,YAAc,WAC9B,GAAIC,GAAUrQ,KAAKgM,QAAQmC,QAC3BnO,MAAKkO,cAAe,EACpBlO,KAAKgM,QAAQ+D,QACb/P,KAAKoN,kBACLpN,KAAKmN,QAAQ,YAAakD,KXolCtB,SAAUxQ,EAAQD,EAASM,GYlpDjCL,EAAAD,QAAAM,EAAA,IAQAL,EAAAD,QAAAuC,OAAAjC,EAAA,KZ0pDM,SAAUL,EAAQD,EAASM,GazoDjC,QAAAqC,GAAA3B,EAAAC,GACA,MAAAb,gBAAAuC,IAEA1B,QAEAD,GAAA,gBAAAA,KACAC,EAAAD,EACAA,EAAA,MAGAA,GACAA,EAAAiC,EAAAjC,GACAC,EAAAyP,SAAA1P,EAAA8B,KACA7B,EAAA0P,OAAA,UAAA3P,EAAAyB,UAAA,QAAAzB,EAAAyB,SACAxB,EAAAiC,KAAAlC,EAAAkC,KACAlC,EAAAgB,QAAAf,EAAAe,MAAAhB,EAAAgB,QACGf,EAAA6B,OACH7B,EAAAyP,SAAAzN,EAAAhC,EAAA6B,YAGA1C,KAAAuQ,OAAA,MAAA1P,EAAA0P,OAAA1P,EAAA0P,OACA,mBAAA9N,WAAA,WAAAA,SAAAJ,SAEAxB,EAAAyP,WAAAzP,EAAAiC,OAEAjC,EAAAiC,KAAA9C,KAAAuQ,OAAA,YAGAvQ,KAAAwQ,MAAA3P,EAAA2P,QAAA,EACAxQ,KAAAsQ,SAAAzP,EAAAyP,WACA,mBAAA7N,mBAAA6N,SAAA,aACAtQ,KAAA8C,KAAAjC,EAAAiC,OAAA,mBAAAL,oBAAAK,KACAL,SAAAK,KACA9C,KAAAuQ,OAAA,QACAvQ,KAAA4B,MAAAf,EAAAe,UACA,gBAAA5B,MAAA4B,QAAA5B,KAAA4B,MAAA6O,EAAAC,OAAA1Q,KAAA4B,QACA5B,KAAA2Q,SAAA,IAAA9P,EAAA8P,QACA3Q,KAAAoB,MAAAP,EAAAO,MAAA,cAAAiC,QAAA,cACArD,KAAA4Q,aAAA/P,EAAA+P,WACA5Q,KAAA6Q,OAAA,IAAAhQ,EAAAgQ,MACA7Q,KAAA8Q,cAAAjQ,EAAAiQ,YACA9Q,KAAA+Q,aAAAlQ,EAAAkQ,WACA/Q,KAAAgR,iBAAA,IAAAnQ,EAAAmQ,gBACAhR,KAAAiR,eAAApQ,EAAAoQ,gBAAA,IACAjR,KAAAkR,kBAAArQ,EAAAqQ,kBACAlR,KAAAmR,WAAAtQ,EAAAsQ,aAAA,uBACAnR,KAAAoR,iBAAAvQ,EAAAuQ,qBACApR,KAAAsM,WAAA,GACAtM,KAAAqR,eACArR,KAAAsR,cAAA,EACAtR,KAAAuR,WAAA1Q,EAAA0Q,YAAA,IACAvR,KAAAwR,gBAAA3Q,EAAA2Q,kBAAA,EACAxR,KAAAyR,WAAA,KACAzR,KAAA0R,mBAAA7Q,EAAA6Q,mBACA1R,KAAA2R,mBAAA,IAAA9Q,EAAA8Q,oBAAA9Q,EAAA8Q,wBAEA,IAAA3R,KAAA2R,oBAAA3R,KAAA2R,sBACA3R,KAAA2R,mBAAA,MAAA3R,KAAA2R,kBAAAC,YACA5R,KAAA2R,kBAAAC,UAAA,MAIA5R,KAAA6R,IAAAhR,EAAAgR,KAAA,KACA7R,KAAAqI,IAAAxH,EAAAwH,KAAA,KACArI,KAAA8R,WAAAjR,EAAAiR,YAAA,KACA9R,KAAA+R,KAAAlR,EAAAkR,MAAA,KACA/R,KAAAgS,GAAAnR,EAAAmR,IAAA,KACAhS,KAAAiS,QAAApR,EAAAoR,SAAA,KACAjS,KAAAkS,mBAAAnR,SAAAF,EAAAqR,oBAAArR,EAAAqR,mBACAlS,KAAAmS,YAAAtR,EAAAsR,UAGAnS,KAAAoS,cAAA,mBAAAC,YAAA,gBAAAA,WAAAC,SAAA,gBAAAD,UAAAC,QAAAC,eAGA,mBAAAlE,OAAArO,KAAAoS,iBACAvR,EAAA2R,cAAAtI,OAAAuI,KAAA5R,EAAA2R,cAAAhP,OAAA,IACAxD,KAAAwS,aAAA3R,EAAA2R,cAGA3R,EAAA6R,eACA1S,KAAA0S,aAAA7R,EAAA6R,eAKA1S,KAAAK,GAAA,KACAL,KAAA2S,SAAA,KACA3S,KAAA4S,aAAA,KACA5S,KAAA6S,YAAA,KAGA7S,KAAA8S,kBAAA,KACA9S,KAAA+S,iBAAA,SAEA/S,MAAA8M,QA9FA,GAAAvK,GAAA3B,EAAAC,GAsLA,QAAAmS,GAAAhR,GACA,GAAAiR,KACA,QAAA1O,KAAAvC,GACAA,EAAAkL,eAAA3I,KACA0O,EAAA1O,GAAAvC,EAAAuC,GAGA,OAAA0O,GApNA,GAAA9B,GAAAjR,EAAA,IACAiH,EAAAjH,EAAA,GAEAoP,GADApP,EAAA,8BACAA,EAAA,KACAiC,EAAAjC,EAAA,IACA2C,EAAA3C,EAAA,GACAuQ,EAAAvQ,EAAA,GAMAL,GAAAD,QAAA2C,EA4GAA,EAAA2Q,uBAAA,EAMA/L,EAAA5E,EAAAL,WAQAK,EAAAF,SAAAF,EAAAE,SAOAE,WACAA,EAAA4Q,UAAAjT,EAAA,IACAqC,EAAA4O,WAAAjR,EAAA,IACAqC,EAAAJ,OAAAjC,EAAA,IAUAqC,EAAAL,UAAAkR,gBAAA,SAAAC,GAEA,GAAAzR,GAAAoR,EAAAhT,KAAA4B,MAGAA,GAAA0R,IAAAnR,EAAAE,SAGAT,EAAA2R,UAAAF,CAGA,IAAA5D,GAAAzP,KAAAoR,iBAAAiC,MAGArT,MAAAK,KAAAuB,EAAA4R,IAAAxT,KAAAK,GAEA,IAAAkT,GAAA,GAAApC,GAAAkC,IACAzR,QACAC,OAAA7B,KACAwQ,MAAAf,EAAAe,OAAAxQ,KAAAwQ,MACAF,SAAAb,EAAAa,UAAAtQ,KAAAsQ,SACAxN,KAAA2M,EAAA3M,MAAA9C,KAAA8C,KACAyN,OAAAd,EAAAc,QAAAvQ,KAAAuQ,OACAnP,KAAAqO,EAAArO,MAAApB,KAAAoB,KACAwP,WAAAnB,EAAAmB,YAAA5Q,KAAA4Q,WACAC,MAAApB,EAAAoB,OAAA7Q,KAAA6Q,MACAC,YAAArB,EAAAqB,aAAA9Q,KAAA8Q,YACAC,WAAAtB,EAAAsB,YAAA/Q,KAAA+Q,WACAC,gBAAAvB,EAAAuB,iBAAAhR,KAAAgR,gBACAE,kBAAAzB,EAAAyB,mBAAAlR,KAAAkR,kBACAD,eAAAxB,EAAAwB,gBAAAjR,KAAAiR,eACAM,WAAA9B,EAAA8B,YAAAvR,KAAAuR,WACAM,IAAApC,EAAAoC,KAAA7R,KAAA6R,IACAxJ,IAAAoH,EAAApH,KAAArI,KAAAqI,IACAyJ,WAAArC,EAAAqC,YAAA9R,KAAA8R,WACAC,KAAAtC,EAAAsC,MAAA/R,KAAA+R,KACAC,GAAAvC,EAAAuC,IAAAhS,KAAAgS,GACAC,QAAAxC,EAAAwC,SAAAjS,KAAAiS,QACAC,mBAAAzC,EAAAyC,oBAAAlS,KAAAkS,mBACAP,kBAAAlC,EAAAkC,mBAAA3R,KAAA2R,kBACAa,aAAA/C,EAAA+C,cAAAxS,KAAAwS,aACAL,UAAA1C,EAAA0C,WAAAnS,KAAAmS,UACAO,aAAAjD,EAAAiD,cAAA1S,KAAA0S,aACAe,eAAAhE,EAAAgE,gBAAAzT,KAAAyT,eACAC,UAAAjE,EAAAiE,WAAA,OACAtB,cAAApS,KAAAoS,eAGA,OAAAmB,IAkBAhR,EAAAL,UAAA4K,KAAA,WACA,GAAAyG,EACA,IAAAvT,KAAAwR,iBAAAjP,EAAA2Q,uBAAAlT,KAAAmR,WAAAnO,QAAA,kBACAuQ,EAAA,gBACG,QAAAvT,KAAAmR,WAAA3N,OAAA,CAEH,GAAA6K,GAAArO,IAIA,YAHA6O,YAAA,WACAR,EAAAzG,KAAA,oCACK,GAGL2L,EAAAvT,KAAAmR,WAAA,GAEAnR,KAAAsM,WAAA,SAGA,KACAiH,EAAAvT,KAAAoT,gBAAAG,GACG,MAAAnP,GAGH,MAFApE,MAAAmR,WAAAxB,YACA3P,MAAA8M,OAIAyG,EAAAzG,OACA9M,KAAA2T,aAAAJ,IASAhR,EAAAL,UAAAyR,aAAA,SAAAJ,GAEA,GAAAlF,GAAArO,IAEAA,MAAAuT,WAEAvT,KAAAuT,UAAAvK,qBAIAhJ,KAAAuT,YAGAA,EACAjL,GAAA,mBACA+F,EAAAuF,YAEAtL,GAAA,kBAAAxC,GACAuI,EAAAwF,SAAA/N,KAEAwC,GAAA,iBAAAlE,GACAiK,EAAAyF,QAAA1P,KAEAkE,GAAA,mBACA+F,EAAA0F,QAAA,sBAWAxR,EAAAL,UAAA8R,MAAA,SAAAX,GAQA,QAAAY,KACA,GAAA5F,EAAAqD,mBAAA,CACA,GAAAwC,IAAAlU,KAAAmU,gBAAA9F,EAAAkF,UAAAY,cACAC,MAAAF,EAEAE,IAGAb,EAAAc,OAAqBzP,KAAA,OAAAjB,KAAA,WACrB4P,EAAA5K,KAAA,kBAAAzB,GACA,IAAAkN,EACA,YAAAlN,EAAAtC,MAAA,UAAAsC,EAAAvD,KAAA,CAIA,GAFA0K,EAAAiG,WAAA,EACAjG,EAAAzG,KAAA,YAAA2L,IACAA,EAAA,MACAhR,GAAA2Q,sBAAA,cAAAK,EAAAF,KAGAhF,EAAAkF,UAAAgB,MAAA,WACAH,GACA,WAAA/F,EAAA/B,aAGAoC,IAEAL,EAAAsF,aAAAJ,GACAA,EAAAc,OAA2BzP,KAAA,aAC3ByJ,EAAAzG,KAAA,UAAA2L,GACAA,EAAA,KACAlF,EAAAiG,WAAA,EACAjG,EAAAmG,eAEO,CAEP,GAAA7F,GAAA,GAAAlI,OAAA,cACAkI,GAAA4E,YAAAF,KACAhF,EAAAzG,KAAA,eAAA+G,OAKA,QAAA8F,KACAL,IAGAA,GAAA,EAEA1F,IAEA6E,EAAAzE,QACAyE,EAAA,MAIA,QAAAnE,GAAAT,GACA,GAAApI,GAAA,GAAAE,OAAA,gBAAAkI,EACApI,GAAAgN,YAAAF,KAEAoB,IAIApG,EAAAzG,KAAA,eAAArB,GAGA,QAAAmO,KACAtF,EAAA,oBAIA,QAAAY,KACAZ,EAAA,iBAIA,QAAAuF,GAAAC,GACArB,GAAAqB,EAAAvB,OAAAE,EAAAF,MAEAoB,IAKA,QAAA/F,KACA6E,EAAAxK,eAAA,OAAAkL,GACAV,EAAAxK,eAAA,QAAAqG,GACAmE,EAAAxK,eAAA,QAAA2L,GACArG,EAAAtF,eAAA,QAAAiH,GACA3B,EAAAtF,eAAA,YAAA4L,GA/FA,GAAApB,GAAAvT,KAAAoT,gBAAAC,GAA8CW,MAAA,IAC9CI,GAAA,EACA/F,EAAArO,IAEAuC,GAAA2Q,uBAAA,EA8FAK,EAAA5K,KAAA,OAAAsL,GACAV,EAAA5K,KAAA,QAAAyG,GACAmE,EAAA5K,KAAA,QAAA+L,GAEA1U,KAAA2I,KAAA,QAAAqH,GACAhQ,KAAA2I,KAAA,YAAAgM,GAEApB,EAAAzG,QASAvK,EAAAL,UAAA2S,OAAA,WASA,GAPA7U,KAAAsM,WAAA,OACA/J,EAAA2Q,sBAAA,cAAAlT,KAAAuT,UAAAF,KACArT,KAAA4H,KAAA,QACA5H,KAAAwU,QAIA,SAAAxU,KAAAsM,YAAAtM,KAAA2Q,SAAA3Q,KAAAuT,UAAAgB,MAEA,OAAAhQ,GAAA,EAAAuQ,EAAA9U,KAAA2S,SAAAnP,OAA6Ce,EAAAuQ,EAAOvQ,IACpDvE,KAAAgU,MAAAhU,KAAA2S,SAAApO,KAWAhC,EAAAL,UAAA2R,SAAA,SAAA/N,GACA,eAAA9F,KAAAsM,YAAA,SAAAtM,KAAAsM,YACA,YAAAtM,KAAAsM,WAQA,OALAtM,KAAA4H,KAAA,SAAA9B,GAGA9F,KAAA4H,KAAA,aAEA9B,EAAAlB,MACA,WACA5E,KAAA+U,YAAA3P,KAAA2B,MAAAjB,EAAAnC,MACA,MAEA,YACA3D,KAAAgV,UACAhV,KAAA4H,KAAA,OACA,MAEA,aACA,GAAA+G,GAAA,GAAAlI,OAAA,eACAkI,GAAAsG,KAAAnP,EAAAnC,KACA3D,KAAA8T,QAAAnF,EACA,MAEA,eACA3O,KAAA4H,KAAA,OAAA9B,EAAAnC,MACA3D,KAAA4H,KAAA,UAAA9B,EAAAnC,QAeApB,EAAAL,UAAA6S,YAAA,SAAApR,GACA3D,KAAA4H,KAAA,YAAAjE,GACA3D,KAAAK,GAAAsD,EAAA6P,IACAxT,KAAAuT,UAAA3R,MAAA4R,IAAA7P,EAAA6P,IACAxT,KAAA2S,SAAA3S,KAAAkV,eAAAvR,EAAAgP,UACA3S,KAAA4S,aAAAjP,EAAAiP,aACA5S,KAAA6S,YAAAlP,EAAAkP,YACA7S,KAAA6U,SAEA,WAAA7U,KAAAsM,aACAtM,KAAAgV,UAGAhV,KAAA+I,eAAA,YAAA/I,KAAAmV,aACAnV,KAAAsI,GAAA,YAAAtI,KAAAmV,eASA5S,EAAAL,UAAAiT,YAAA,SAAA9I,GACA0C,aAAA/O,KAAA+S,iBACA,IAAA1E,GAAArO,IACAqO,GAAA0E,iBAAAlE,WAAA,WACA,WAAAR,EAAA/B,YACA+B,EAAA0F,QAAA,iBACG1H,GAAAgC,EAAAuE,aAAAvE,EAAAwE,cAUHtQ,EAAAL,UAAA8S,QAAA,WACA,GAAA3G,GAAArO,IACA+O,cAAAV,EAAAyE,mBACAzE,EAAAyE,kBAAAjE,WAAA,WAEAR,EAAA+G,OACA/G,EAAA8G,YAAA9G,EAAAwE,cACGxE,EAAAuE,eASHrQ,EAAAL,UAAAkT,KAAA,WACA,GAAA/G,GAAArO,IACAA,MAAAqV,WAAA,kBACAhH,EAAAzG,KAAA,WAUArF,EAAAL,UAAA0R,QAAA,WACA5T,KAAAqR,YAAA5N,OAAA,EAAAzD,KAAAsR,eAKAtR,KAAAsR,cAAA,EAEA,IAAAtR,KAAAqR,YAAA7N,OACAxD,KAAA4H,KAAA,SAEA5H,KAAAwU,SAUAjS,EAAAL,UAAAsS,MAAA,WACA,WAAAxU,KAAAsM,YAAAtM,KAAAuT,UAAA+B,WACAtV,KAAAsU,WAAAtU,KAAAqR,YAAA7N,SAEAxD,KAAAuT,UAAAc,KAAArU,KAAAqR,aAGArR,KAAAsR,cAAAtR,KAAAqR,YAAA7N,OACAxD,KAAA4H,KAAA,WAcArF,EAAAL,UAAAsN,MACAjN,EAAAL,UAAAmS,KAAA,SAAAnN,EAAAuI,EAAAhH,GAEA,MADAzI,MAAAqV,WAAA,UAAAnO,EAAAuI,EAAAhH,GACAzI,MAaAuC,EAAAL,UAAAmT,WAAA,SAAAzQ,EAAAjB,EAAA8L,EAAAhH,GAWA,GAVA,kBAAA9E,KACA8E,EAAA9E,EACAA,EAAA5C,QAGA,kBAAA0O,KACAhH,EAAAgH,EACAA,EAAA,MAGA,YAAAzP,KAAAsM,YAAA,WAAAtM,KAAAsM,WAAA,CAIAmD,QACAA,EAAA8F,UAAA,IAAA9F,EAAA8F,QAEA,IAAAzP,IACAlB,OACAjB,OACA8L,UAEAzP,MAAA4H,KAAA,eAAA9B,GACA9F,KAAAqR,YAAAnJ,KAAApC,GACA2C,GAAAzI,KAAA2I,KAAA,QAAAF,GACAzI,KAAAwU,UASAjS,EAAAL,UAAA4M,MAAA,WAqBA,QAAAA,KACAT,EAAA0F,QAAA,gBAEA1F,EAAAkF,UAAAzE,QAGA,QAAA0G,KACAnH,EAAAtF,eAAA,UAAAyM,GACAnH,EAAAtF,eAAA,eAAAyM,GACA1G,IAGA,QAAA2G,KAEApH,EAAA1F,KAAA,UAAA6M,GACAnH,EAAA1F,KAAA,eAAA6M,GAnCA,eAAAxV,KAAAsM,YAAA,SAAAtM,KAAAsM,WAAA,CACAtM,KAAAsM,WAAA,SAEA,IAAA+B,GAAArO,IAEAA,MAAAqR,YAAA7N,OACAxD,KAAA2I,KAAA,mBACA3I,KAAAsU,UACAmB,IAEA3G,MAGK9O,KAAAsU,UACLmB,IAEA3G,IAsBA,MAAA9O,OASAuC,EAAAL,UAAA4R,QAAA,SAAAnF,GAEApM,EAAA2Q,uBAAA,EACAlT,KAAA4H,KAAA,QAAA+G,GACA3O,KAAA+T,QAAA,kBAAApF,IASApM,EAAAL,UAAA6R,QAAA,SAAA9D,EAAAyF,GACA,eAAA1V,KAAAsM,YAAA,SAAAtM,KAAAsM,YAAA,YAAAtM,KAAAsM,WAAA,CAEA,GAAA+B,GAAArO,IAGA+O,cAAA/O,KAAA8S,mBACA/D,aAAA/O,KAAA+S,kBAGA/S,KAAAuT,UAAAvK,mBAAA,SAGAhJ,KAAAuT,UAAAzE,QAGA9O,KAAAuT,UAAAvK,qBAGAhJ,KAAAsM,WAAA,SAGAtM,KAAAK,GAAA,KAGAL,KAAA4H,KAAA,QAAAqI,EAAAyF,GAIArH,EAAAgD,eACAhD,EAAAiD,cAAA,IAYA/O,EAAAL,UAAAgT,eAAA,SAAAvC,GAEA,OADAgD,MACApR,EAAA,EAAAqR,EAAAjD,EAAAnP,OAAsCe,EAAAqR,EAAOrR,KAC7C+K,EAAAtP,KAAAmR,WAAAwB,EAAApO,KAAAoR,EAAAzN,KAAAyK,EAAApO,GAEA,OAAAoR,Kb2qDM,SAAU9V,EAAQD,EAASM,Gc93EjC,QAAA2V,GAAAhV,GACA,GAAAiV,GACAC,GAAA,EACAC,GAAA,EACAnF,GAAA,IAAAhQ,EAAAgQ,KAEA,uBAAApO,UAAA,CACA,GAAAwT,GAAA,WAAAxT,SAAAJ,SACAS,EAAAL,SAAAK,IAGAA,KACAA,EAAAmT,EAAA,QAGAF,EAAAlV,EAAAyP,WAAA7N,SAAA6N,UAAAxN,IAAAjC,EAAAiC,KACAkT,EAAAnV,EAAA0P,SAAA0F,EAOA,GAJApV,EAAAqV,QAAAH,EACAlV,EAAAsV,QAAAH,EACAF,EAAA,GAAAM,GAAAvV,GAEA,QAAAiV,KAAAjV,EAAA+P,WACA,UAAAyF,GAAAxV,EAEA,KAAAgQ,EAAA,SAAApK,OAAA,iBACA,WAAA6P,GAAAzV,GA9CA,GAAAuV,GAAAlW,EAAA,IACAmW,EAAAnW,EAAA,IACAoW,EAAApW,EAAA,IACAqW,EAAArW,EAAA,GAMAN,GAAAiW,UACAjW,EAAA2W,adk8EM,SAAU1W,EAAQD,EAASM,Ge98EjC,GAAAsW,GAAAtW,EAAA,IACAuW,EAAAvW,EAAA,GAEAL,GAAAD,QAAA,SAAAiB,GACA,GAAAqV,GAAArV,EAAAqV,QAIAC,EAAAtV,EAAAsV,QAIApF,EAAAlQ,EAAAkQ,UAGA,KACA,sBAAAqF,mBAAAF,GAAAM,GACA,UAAAJ,gBAEG,MAAAhS,IAKH,IACA,sBAAAsS,kBAAAP,GAAApF,EACA,UAAA2F,gBAEG,MAAAtS,IAEH,IAAA8R,EACA,IACA,WAAAO,GAAA,UAAAE,OAAA,UAAAC,KAAA,4BACK,MAAAxS,Ofy9EC,SAAUvE,EAAQD,GgBn/ExB,IACAC,EAAAD,QAAA,mBAAAwW,iBACA,uBAAAA,gBACC,MAAAzH,GAGD9O,EAAAD,SAAA,IhBogFM,SAAUC,EAAQD,GiBnhFxBC,EAAAD,QAAA,WACA,yBAAAyO,MACAA,KACG,mBAAAwI,QACHA,OAEAC,SAAA,qBjB4hFM,SAAUjX,EAAQD,EAASM,GkB1gFjC,QAAA6W,MASA,QAAAV,GAAAxV,GAKA,GAJAmW,EAAAzW,KAAAP,KAAAa,GACAb,KAAAyT,eAAA5S,EAAA4S,eACAzT,KAAAwS,aAAA3R,EAAA2R,aAEA,mBAAA/P,UAAA,CACA,GAAAwT,GAAA,WAAAxT,SAAAJ,SACAS,EAAAL,SAAAK,IAGAA,KACAA,EAAAmT,EAAA,QAGAjW,KAAA+V,GAAA,mBAAAtT,WAAA5B,EAAAyP,WAAA7N,SAAA6N,UACAxN,IAAAjC,EAAAiC,KACA9C,KAAAgW,GAAAnV,EAAA0P,SAAA0F,GA8FA,QAAAgB,GAAApW,GACAb,KAAAkX,OAAArW,EAAAqW,QAAA,MACAlX,KAAAY,IAAAC,EAAAD,IACAZ,KAAA+V,KAAAlV,EAAAkV,GACA/V,KAAAgW,KAAAnV,EAAAmV,GACAhW,KAAAmX,OAAA,IAAAtW,EAAAsW,MACAnX,KAAA2D,KAAA5C,SAAAF,EAAA8C,KAAA9C,EAAA8C,KAAA,KACA3D,KAAAwQ,MAAA3P,EAAA2P,MACAxQ,KAAAoX,SAAAvW,EAAAuW,SACApX,KAAAmU,eAAAtT,EAAAsT,eACAnU,KAAA+Q,WAAAlQ,EAAAkQ,WACA/Q,KAAAgR,gBAAAnQ,EAAAmQ,gBACAhR,KAAAyT,eAAA5S,EAAA4S,eAGAzT,KAAA6R,IAAAhR,EAAAgR,IACA7R,KAAAqI,IAAAxH,EAAAwH,IACArI,KAAA8R,WAAAjR,EAAAiR,WACA9R,KAAA+R,KAAAlR,EAAAkR,KACA/R,KAAAgS,GAAAnR,EAAAmR,GACAhS,KAAAiS,QAAApR,EAAAoR,QACAjS,KAAAkS,mBAAArR,EAAAqR,mBAGAlS,KAAAwS,aAAA3R,EAAA2R,aAEAxS,KAAAqX,SAkPA,QAAAC,KACA,OAAA/S,KAAA0S,GAAAM,SACAN,EAAAM,SAAArK,eAAA3I,IACA0S,EAAAM,SAAAhT,GAAAiT,QAxZA,GAAApB,GAAAlW,EAAA,IACA8W,EAAA9W,EAAA,IACAiH,EAAAjH,EAAA,GACAuX,EAAAvX,EAAA,IAEAuW,GADAvW,EAAA,mCACAA,EAAA,IAuYA,IAjYAL,EAAAD,QAAAyW,EACAxW,EAAAD,QAAAqX,UAuCAQ,EAAApB,EAAAW,GAMAX,EAAAnU,UAAAiS,gBAAA,EASAkC,EAAAnU,UAAAwV,QAAA,SAAA7W,GAuBA,MAtBAA,SACAA,EAAAD,IAAAZ,KAAAY,MACAC,EAAAkV,GAAA/V,KAAA+V,GACAlV,EAAAmV,GAAAhW,KAAAgW,GACAnV,EAAA2P,MAAAxQ,KAAAwQ,QAAA,EACA3P,EAAAsT,eAAAnU,KAAAmU,eACAtT,EAAAkQ,WAAA/Q,KAAA+Q,WACAlQ,EAAAmQ,gBAAAhR,KAAAgR,gBAGAnQ,EAAAgR,IAAA7R,KAAA6R,IACAhR,EAAAwH,IAAArI,KAAAqI,IACAxH,EAAAiR,WAAA9R,KAAA8R,WACAjR,EAAAkR,KAAA/R,KAAA+R,KACAlR,EAAAmR,GAAAhS,KAAAgS,GACAnR,EAAAoR,QAAAjS,KAAAiS,QACApR,EAAAqR,mBAAAlS,KAAAkS,mBACArR,EAAA4S,eAAAzT,KAAAyT,eAGA5S,EAAA2R,aAAAxS,KAAAwS,aAEA,GAAAyE,GAAApW,IAWAwV,EAAAnU,UAAAyV,QAAA,SAAAhU,EAAA8E,GACA,GAAA2O,GAAA,gBAAAzT,IAAA5C,SAAA4C,EACAiU,EAAA5X,KAAA0X,SAA0BR,OAAA,OAAAvT,OAAAyT,aAC1B/I,EAAArO,IACA4X,GAAAtP,GAAA,UAAAG,GACAmP,EAAAtP,GAAA,iBAAAqG,GACAN,EAAAyF,QAAA,iBAAAnF,KAEA3O,KAAA6X,QAAAD,GASAvB,EAAAnU,UAAA4V,OAAA,WAEA,GAAAF,GAAA5X,KAAA0X,UACArJ,EAAArO,IACA4X,GAAAtP,GAAA,gBAAA3E,GACA0K,EAAA0J,OAAApU,KAEAiU,EAAAtP,GAAA,iBAAAqG,GACAN,EAAAyF,QAAA,iBAAAnF,KAEA3O,KAAAgY,QAAAJ,GA2CAzQ,EAAA8P,EAAA/U,WAQA+U,EAAA/U,UAAAmV,OAAA,WACA,GAAAxW,IAAc2P,MAAAxQ,KAAAwQ,MAAA0F,QAAAlW,KAAA+V,GAAAI,QAAAnW,KAAAgW,GAAAjF,WAAA/Q,KAAA+Q,WAGdlQ,GAAAgR,IAAA7R,KAAA6R,IACAhR,EAAAwH,IAAArI,KAAAqI,IACAxH,EAAAiR,WAAA9R,KAAA8R,WACAjR,EAAAkR,KAAA/R,KAAA+R,KACAlR,EAAAmR,GAAAhS,KAAAgS,GACAnR,EAAAoR,QAAAjS,KAAAiS,QACApR,EAAAqR,mBAAAlS,KAAAkS,kBAEA,IAAA4D,GAAA9V,KAAA8V,IAAA,GAAAM,GAAAvV,GACAwN,EAAArO,IAEA,KAEA8V,EAAAhJ,KAAA9M,KAAAkX,OAAAlX,KAAAY,IAAAZ,KAAAmX,MACA,KACA,GAAAnX,KAAAwS,aAAA,CACAsD,EAAAmC,uBAAAnC,EAAAmC,uBAAA,EACA,QAAA1T,KAAAvE,MAAAwS,aACAxS,KAAAwS,aAAAtF,eAAA3I,IACAuR,EAAAoC,iBAAA3T,EAAAvE,KAAAwS,aAAAjO,KAIK,MAAAH,IAEL,YAAApE,KAAAkX,OACA,IACAlX,KAAAoX,SACAtB,EAAAoC,iBAAA,2CAEApC,EAAAoC,iBAAA,2CAEO,MAAA9T,IAGP,IACA0R,EAAAoC,iBAAA,gBACK,MAAA9T,IAGL,mBAAA0R,KACAA,EAAA9E,gBAAAhR,KAAAgR,iBAGAhR,KAAAyT,iBACAqC,EAAAzJ,QAAArM,KAAAyT,gBAGAzT,KAAAmY,UACArC,EAAAhL,OAAA,WACAuD,EAAA+J,UAEAtC,EAAA1G,QAAA,WACAf,EAAAyF,QAAAgC,EAAAuC,gBAGAvC,EAAAwC,mBAAA,WACA,OAAAxC,EAAAxJ,WACA,IACA,GAAAiM,GAAAzC,EAAA0C,kBAAA,iBACAnK,EAAA8F,gBAAA,6BAAAoE,GAAA,4CAAAA,KACAzC,EAAA2C,aAAA,eAEW,MAAArU,IAEX,IAAA0R,EAAAxJ,aACA,MAAAwJ,EAAA4C,QAAA,OAAA5C,EAAA4C,OACArK,EAAA+J,SAIAvJ,WAAA,WACAR,EAAAyF,QAAA,gBAAAgC,GAAA4C,OAAA5C,EAAA4C,OAAA,IACW,KAMX5C,EAAAzB,KAAArU,KAAA2D,MACG,MAAAS,GAOH,WAHAyK,YAAA,WACAR,EAAAyF,QAAA1P,IACK,GAIL,mBAAAuU,YACA3Y,KAAAsP,MAAA2H,EAAA2B,gBACA3B,EAAAM,SAAAvX,KAAAsP,OAAAtP,OAUAiX,EAAA/U,UAAA2W,UAAA,WACA7Y,KAAA4H,KAAA,WACA5H,KAAA0O,WASAuI,EAAA/U,UAAA6V,OAAA,SAAApU,GACA3D,KAAA4H,KAAA,OAAAjE,GACA3D,KAAA6Y,aASA5B,EAAA/U,UAAA4R,QAAA,SAAAnF,GACA3O,KAAA4H,KAAA,QAAA+G,GACA3O,KAAA0O,SAAA,IASAuI,EAAA/U,UAAAwM,QAAA,SAAAoK,GACA,sBAAA9Y,MAAA8V,KAAA,OAAA9V,KAAA8V,IAAA,CAUA,GANA9V,KAAAmY,SACAnY,KAAA8V,IAAAhL,OAAA9K,KAAA8V,IAAA1G,QAAA2H,EAEA/W,KAAA8V,IAAAwC,mBAAAvB,EAGA+B,EACA,IACA9Y,KAAA8V,IAAA0B,QACK,MAAApT,IAGL,mBAAAuU,iBACA1B,GAAAM,SAAAvX,KAAAsP,OAGAtP,KAAA8V,IAAA,OASAmB,EAAA/U,UAAAkW,OAAA,WACA,GAAAzU,EACA,KACA,GAAA4U,EACA,KACAA,EAAAvY,KAAA8V,IAAA0C,kBAAA,gBACK,MAAApU,IAELT,EADA,6BAAA4U,GAAA,4CAAAA,EACAvY,KAAA8V,IAAAiD,UAAA/Y,KAAA8V,IAAAuC,aAEArY,KAAA8V,IAAAuC,aAEG,MAAAjU,GACHpE,KAAA8T,QAAA1P,GAEA,MAAAT,GACA3D,KAAA+X,OAAApU,IAUAsT,EAAA/U,UAAAiW,OAAA,WACA,yBAAAzB,kBAAA1W,KAAAgW,IAAAhW,KAAA+Q,YASAkG,EAAA/U,UAAAsV,MAAA,WACAxX,KAAA0O,WASAuI,EAAA2B,cAAA,EACA3B,EAAAM,YAEA,mBAAAoB,UACA,qBAAAK,aACAA,YAAA,WAAA1B,OACG,sBAAA/O,kBAAA,CACH,GAAA0Q,GAAA,cAAAxC,GAAA,mBACAlO,kBAAA0Q,EAAA3B,GAAA,KlBmjFM,SAAUzX,EAAQD,EAASM,GmBx6FjC,QAAA8W,GAAAnW,GACA,GAAAiQ,GAAAjQ,KAAAiQ,WACAoI,KAAApI,IACA9Q,KAAAmU,gBAAA,GAEAhB,EAAA5S,KAAAP,KAAAa,GAnCA,GAAAsS,GAAAjT,EAAA,IACAuQ,EAAAvQ,EAAA,IACAiC,EAAAjC,EAAA,IACAuX,EAAAvX,EAAA,IACAiZ,EAAAjZ,EAAA,GACAA,GAAA,8BAMAL,GAAAD,QAAAoX,CAMA,IAAAkC,GAAA,WACA,GAAA9C,GAAAlW,EAAA,IACA4V,EAAA,GAAAM,IAAgCF,SAAA,GAChC,cAAAJ,EAAA2C,eAsBAhB,GAAAT,EAAA7D,GAMA6D,EAAA9U,UAAAmR,KAAA,UASA2D,EAAA9U,UAAAkX,OAAA,WACApZ,KAAAqZ,QAUArC,EAAA9U,UAAAqS,MAAA,SAAA+E,GAKA,QAAA/E,KAEAlG,EAAA/B,WAAA,SACAgN,IAPA,GAAAjL,GAAArO,IAUA,IARAA,KAAAsM,WAAA,UAQAtM,KAAA6V,UAAA7V,KAAAsV,SAAA,CACA,GAAAiE,GAAA,CAEAvZ,MAAA6V,UAEA0D,IACAvZ,KAAA2I,KAAA,4BAEA4Q,GAAAhF,OAIAvU,KAAAsV,WAEAiE,IACAvZ,KAAA2I,KAAA,qBAEA4Q,GAAAhF,WAIAA,MAUAyC,EAAA9U,UAAAmX,KAAA,WAEArZ,KAAA6V,SAAA,EACA7V,KAAA8X,SACA9X,KAAA4H,KAAA,SASAoP,EAAA9U,UAAA6V,OAAA,SAAApU,GACA,GAAA0K,GAAArO,KAEAuF,EAAA,SAAAO,EAAAwJ,EAAAiK,GAOA,MALA,YAAAlL,EAAA/B,YAAA,SAAAxG,EAAAlB,MACAyJ,EAAAwG,SAIA,UAAA/O,EAAAlB,MACAyJ,EAAA0F,WACA,OAIA1F,GAAAwF,SAAA/N,GAIA3D,GAAAqX,cAAA7V,EAAA3D,KAAA6B,OAAA4P,WAAAlM,GAGA,WAAAvF,KAAAsM,aAEAtM,KAAA6V,SAAA,EACA7V,KAAA4H,KAAA,gBAEA,SAAA5H,KAAAsM,YACAtM,KAAAqZ,SAaArC,EAAA9U,UAAAuX,QAAA,WAGA,QAAA3K,KAEAT,EAAAmB,QAAiB5K,KAAA,WAJjB,GAAAyJ,GAAArO,IAOA,UAAAA,KAAAsM,WAEAwC,IAKA9O,KAAA2I,KAAA,OAAAmG,IAYAkI,EAAA9U,UAAAsN,MAAA,SAAAkK,GACA,GAAArL,GAAArO,IACAA,MAAAsV,UAAA,CACA,IAAAqE,GAAA,WACAtL,EAAAiH,UAAA,EACAjH,EAAAzG,KAAA,SAGAzF,GAAAyX,cAAAF,EAAA1Z,KAAAmU,eAAA,SAAAxQ,GACA0K,EAAAsJ,QAAAhU,EAAAgW,MAUA3C,EAAA9U,UAAAtB,IAAA,WACA,GAAAgB,GAAA5B,KAAA4B,UACAiY,EAAA7Z,KAAAuQ,OAAA,eACAzN,EAAA,IAGA,IAAA9C,KAAAkR,oBACAtP,EAAA5B,KAAAiR,gBAAAkI,KAGAnZ,KAAAmU,gBAAAvS,EAAA4R,MACA5R,EAAAkY,IAAA,GAGAlY,EAAA6O,EAAAhJ,OAAA7F,GAGA5B,KAAA8C,OAAA,UAAA+W,GAAA,MAAAxT,OAAArG,KAAA8C,OACA,SAAA+W,GAAA,KAAAxT,OAAArG,KAAA8C,SACAA,EAAA,IAAA9C,KAAA8C,MAIAlB,EAAA4B,SACA5B,EAAA,IAAAA,EAGA,IAAAmB,GAAA/C,KAAAsQ,SAAAtN,QAAA,SACA,OAAA6W,GAAA,OAAA9W,EAAA,IAAA/C,KAAAsQ,SAAA,IAAAtQ,KAAAsQ,UAAAxN,EAAA9C,KAAAoB,KAAAQ,InBk9FM,SAAU/B,EAAQD,EAASM,GoBjrGjC,QAAAiT,GAAAtS,GACAb,KAAAoB,KAAAP,EAAAO,KACApB,KAAAsQ,SAAAzP,EAAAyP,SACAtQ,KAAA8C,KAAAjC,EAAAiC,KACA9C,KAAAuQ,OAAA1P,EAAA0P,OACAvQ,KAAA4B,MAAAf,EAAAe,MACA5B,KAAAiR,eAAApQ,EAAAoQ,eACAjR,KAAAkR,kBAAArQ,EAAAqQ,kBACAlR,KAAAsM,WAAA,GACAtM,KAAAwQ,MAAA3P,EAAA2P,QAAA,EACAxQ,KAAA6B,OAAAhB,EAAAgB,OACA7B,KAAA+Q,WAAAlQ,EAAAkQ,WACA/Q,KAAAgR,gBAAAnQ,EAAAmQ,gBAGAhR,KAAA6R,IAAAhR,EAAAgR,IACA7R,KAAAqI,IAAAxH,EAAAwH,IACArI,KAAA8R,WAAAjR,EAAAiR,WACA9R,KAAA+R,KAAAlR,EAAAkR,KACA/R,KAAAgS,GAAAnR,EAAAmR,GACAhS,KAAAiS,QAAApR,EAAAoR,QACAjS,KAAAkS,mBAAArR,EAAAqR,mBACAlS,KAAAmS,UAAAtR,EAAAsR;AAGAnS,KAAAoS,cAAAvR,EAAAuR,cAGApS,KAAAwS,aAAA3R,EAAA2R,aACAxS,KAAA0S,aAAA7R,EAAA6R,aA7CA,GAAAvQ,GAAAjC,EAAA,IACAiH,EAAAjH,EAAA,EAMAL,GAAAD,QAAAuT,EA6CAhM,EAAAgM,EAAAjR,WAUAiR,EAAAjR,UAAA4R,QAAA,SAAA5M,EAAAwO,GACA,GAAA/G,GAAA,GAAAlI,OAAAS,EAIA,OAHAyH,GAAA/J,KAAA,iBACA+J,EAAAoL,YAAArE,EACA1V,KAAA4H,KAAA,QAAA+G,GACA3O,MASAmT,EAAAjR,UAAA4K,KAAA,WAMA,MALA,WAAA9M,KAAAsM,YAAA,KAAAtM,KAAAsM,aACAtM,KAAAsM,WAAA,UACAtM,KAAAoZ,UAGApZ,MASAmT,EAAAjR,UAAA4M,MAAA,WAMA,MALA,YAAA9O,KAAAsM,YAAA,SAAAtM,KAAAsM,aACAtM,KAAAyZ,UACAzZ,KAAA+T,WAGA/T,MAUAmT,EAAAjR,UAAAmS,KAAA,SAAAqF,GACA,YAAA1Z,KAAAsM,WAGA,SAAA7F,OAAA,qBAFAzG,MAAAwP,MAAAkK,IAYAvG,EAAAjR,UAAA2S,OAAA,WACA7U,KAAAsM,WAAA,OACAtM,KAAAsV,UAAA,EACAtV,KAAA4H,KAAA,SAUAuL,EAAAjR,UAAA6V,OAAA,SAAApU,GACA,GAAAmC,GAAA3D,EAAA6X,aAAArW,EAAA3D,KAAA6B,OAAA4P,WACAzR,MAAA6T,SAAA/N,IAOAqN,EAAAjR,UAAA2R,SAAA,SAAA/N,GACA9F,KAAA4H,KAAA,SAAA9B,IASAqN,EAAAjR,UAAA6R,QAAA,WACA/T,KAAAsM,WAAA,SACAtM,KAAA4H,KAAA,WpB6sGM,SAAU/H,EAAQD,EAASM,GqB9uGjC,QAAA+Z,GAAAnU,EAAAP,GAEA,GAAA2U,GAAA,IAAAta,EAAA8Z,QAAA5T,EAAAlB,MAAAkB,EAAAnC,SACA,OAAA4B,GAAA2U,GAOA,QAAAC,GAAArU,EAAAqO,EAAA5O,GACA,IAAA4O,EACA,MAAAvU,GAAAwa,mBAAAtU,EAAAP,EAGA,IAAA5B,GAAAmC,EAAAnC,KACA0W,EAAA,GAAAC,YAAA3W,GACA4W,EAAA,GAAAD,YAAA,EAAA3W,EAAA6W,WAEAD,GAAA,GAAAb,EAAA5T,EAAAlB,KACA,QAAAL,GAAA,EAAiBA,EAAA8V,EAAA7W,OAAyBe,IAC1CgW,EAAAhW,EAAA,GAAA8V,EAAA9V,EAGA,OAAAgB,GAAAgV,EAAA/O,QAGA,QAAAiP,GAAA3U,EAAAqO,EAAA5O,GACA,IAAA4O,EACA,MAAAvU,GAAAwa,mBAAAtU,EAAAP,EAGA,IAAAmV,GAAA,GAAA7P,WAIA,OAHA6P,GAAA5P,OAAA,WACAlL,EAAA+a,cAA0B/V,KAAAkB,EAAAlB,KAAAjB,KAAA+W,EAAA3P,QAAqCoJ,GAAA,EAAA5O,IAE/DmV,EAAA1P,kBAAAlF,EAAAnC,MAGA,QAAAiX,GAAA9U,EAAAqO,EAAA5O,GACA,IAAA4O,EACA,MAAAvU,GAAAwa,mBAAAtU,EAAAP,EAGA,IAAAsV,EACA,MAAAJ,GAAA3U,EAAAqO,EAAA5O,EAGA,IAAA/B,GAAA,GAAA8W,YAAA,EACA9W,GAAA,GAAAkW,EAAA5T,EAAAlB,KACA,IAAAkW,GAAA,GAAA1Q,IAAA5G,EAAAgI,OAAA1F,EAAAnC,MAEA,OAAA4B,GAAAuV,GAkFA,QAAAC,GAAApX,GACA,IACAA,EAAAqX,EAAAtK,OAAA/M,GAA8BsX,QAAA,IAC3B,MAAA7W,GACH,SAEA,MAAAT,GAgFA,QAAAuX,GAAAC,EAAAC,EAAAC,GAWA,OAVAtQ,GAAA,GAAA1B,OAAA8R,EAAA3X,QACAkD,EAAA4U,EAAAH,EAAA3X,OAAA6X,GAEAE,EAAA,SAAAhX,EAAAiX,EAAArS,GACAiS,EAAAI,EAAA,SAAAjV,EAAAW,GACA6D,EAAAxG,GAAA2C,EACAiC,EAAA5C,EAAAwE,MAIAxG,EAAA,EAAiBA,EAAA4W,EAAA3X,OAAgBe,IACjCgX,EAAAhX,EAAA4W,EAAA5W,GAAAmC,GAlWA,GAMA+U,GANAhJ,EAAAvS,EAAA,IACAwb,EAAAxb,EAAA,IACAyb,EAAAzb,EAAA,IACAob,EAAApb,EAAA,IACA8a,EAAA9a,EAAA,GAGA,oBAAAoL,eACAmQ,EAAAvb,EAAA,IAUA,IAAA0b,GAAA,mBAAAvJ,YAAA,WAAAzP,KAAAyP,UAAAwJ,WAQAC,EAAA,mBAAAzJ,YAAA,aAAAzP,KAAAyP,UAAAwJ,WAMAhB,EAAAe,GAAAE,CAMAlc,GAAAyC,SAAA,CAMA,IAAAqX,GAAA9Z,EAAA8Z,SACA5M,KAAA,EACAgC,MAAA,EACAsG,KAAA,EACA2G,KAAA,EACA7B,QAAA,EACAvJ,QAAA,EACAqL,KAAA,GAGAC,EAAAxJ,EAAAiH,GAMA/K,GAAW/J,KAAA,QAAAjB,KAAA,gBAMXyG,EAAAlK,EAAA,GAkBAN,GAAA+a,aAAA,SAAA7U,EAAAqO,EAAA+H,EAAA3W,GACA,kBAAA4O,KACA5O,EAAA4O,EACAA,GAAA,GAGA,kBAAA+H,KACA3W,EAAA2W,EACAA,EAAA,KAGA,IAAAvY,GAAA5C,SAAA+E,EAAAnC,KACA5C,OACA+E,EAAAnC,KAAA6H,QAAA1F,EAAAnC,IAEA,uBAAA2H,cAAA3H,YAAA2H,aACA,MAAA6O,GAAArU,EAAAqO,EAAA5O,EACG,uBAAA6E,IAAAzG,YAAAyG,GACH,MAAAwQ,GAAA9U,EAAAqO,EAAA5O,EAIA,IAAA5B,KAAAkE,OACA,MAAAoS,GAAAnU,EAAAP,EAIA,IAAA4W,GAAAzC,EAAA5T,EAAAlB,KAOA,OAJA7D,UAAA+E,EAAAnC,OACAwY,GAAAD,EAAAlB,EAAAvT,OAAA2U,OAAAtW,EAAAnC,OAA8DsX,QAAA,IAAgBmB,OAAAtW,EAAAnC,OAG9E4B,EAAA,GAAA4W,IAkEAvc,EAAAwa,mBAAA,SAAAtU,EAAAP,GACA,GAAA2U,GAAA,IAAAta,EAAA8Z,QAAA5T,EAAAlB,KACA,uBAAAwF,IAAAtE,EAAAnC,eAAAyG,GAAA,CACA,GAAAsQ,GAAA,GAAA7P,WAKA,OAJA6P,GAAA5P,OAAA,WACA,GAAAgP,GAAAY,EAAA3P,OAAAzH,MAAA,OACAiC,GAAA2U,EAAAJ,IAEAY,EAAA2B,cAAAvW,EAAAnC,MAGA,GAAA2Y,EACA,KACAA,EAAAF,OAAAG,aAAA1T,MAAA,QAAAyR,YAAAxU,EAAAnC,OACG,MAAAS,GAIH,OAFAoY,GAAA,GAAAlC,YAAAxU,EAAAnC,MACA8Y,EAAA,GAAApT,OAAAmT,EAAAhZ,QACAe,EAAA,EAAmBA,EAAAiY,EAAAhZ,OAAkBe,IACrCkY,EAAAlY,GAAAiY,EAAAjY,EAEA+X,GAAAF,OAAAG,aAAA1T,MAAA,KAAA4T,GAGA,MADAvC,IAAAwC,KAAAJ,GACA/W,EAAA2U,IAUAta,EAAAoa,aAAA,SAAArW,EAAA8N,EAAAkL,GACA,GAAA5b,SAAA4C,EACA,MAAAgL,EAGA,oBAAAhL,GAAA,CACA,SAAAA,EAAAhB,OAAA,GACA,MAAA/C,GAAAgd,mBAAAjZ,EAAAJ,OAAA,GAAAkO,EAGA,IAAAkL,IACAhZ,EAAAoX,EAAApX,GACAA,KAAA,GACA,MAAAgL,EAGA,IAAA/J,GAAAjB,EAAAhB,OAAA,EAEA,OAAA0D,QAAAzB,OAAAqX,EAAArX,GAIAjB,EAAAH,OAAA,GACcoB,KAAAqX,EAAArX,GAAAjB,OAAAU,UAAA,KAEAO,KAAAqX,EAAArX,IANd+J,EAUA,GAAAkO,GAAA,GAAAvC,YAAA3W,GACAiB,EAAAiY,EAAA,GACAC,EAAAnB,EAAAhY,EAAA,EAIA,OAHAyG,IAAA,SAAAqH,IACAqL,EAAA,GAAA1S,IAAA0S,MAEUlY,KAAAqX,EAAArX,GAAAjB,KAAAmZ,IAmBVld,EAAAgd,mBAAA,SAAA1V,EAAAuK,GACA,GAAA7M,GAAAqX,EAAA/U,EAAAvE,OAAA,GACA,KAAA8Y,EACA,OAAY7W,OAAAjB,MAAoBkE,QAAA,EAAAlE,KAAAuD,EAAA3D,OAAA,IAGhC,IAAAI,GAAA8X,EAAA/K,OAAAxJ,EAAA3D,OAAA,GAMA,OAJA,SAAAkO,GAAArH,IACAzG,EAAA,GAAAyG,IAAAzG,MAGUiB,OAAAjB,SAmBV/D,EAAAga,cAAA,SAAAF,EAAAvF,EAAA5O,GAoBA,QAAAwX,GAAA7C,GACA,MAAAA,GAAA1W,OAAA,IAAA0W,EAGA,QAAA8C,GAAAlX,EAAAmX,GACArd,EAAA+a,aAAA7U,IAAAsR,GAAAjD,GAAA,WAAA+F,GACA+C,EAAA,KAAAF,EAAA7C,MAzBA,kBAAA/F,KACA5O,EAAA4O,EACAA,EAAA,KAGA,IAAAiD,GAAAsE,EAAAhC,EAEA,OAAAvF,IAAAiD,EACAhN,IAAAyQ,EACAjb,EAAAsd,oBAAAxD,EAAAnU,GAGA3F,EAAAud,2BAAAzD,EAAAnU,GAGAmU,EAAAlW,WAcA0X,GAAAxB,EAAAsD,EAAA,SAAArO,EAAAyO,GACA,MAAA7X,GAAA6X,EAAAxG,KAAA,OAdArR,EAAA,OA8CA3F,EAAA4Z,cAAA,SAAA7V,EAAA8N,EAAAlM,GACA,mBAAA5B,GACA,MAAA/D,GAAAyd,sBAAA1Z,EAAA8N,EAAAlM,EAGA,mBAAAkM,KACAlM,EAAAkM,EACAA,EAAA,KAGA,IAAA3L,EACA,SAAAnC,EAEA,MAAA4B,GAAAoJ,EAAA,IAKA,QAFA2O,GAAApW,EAAA1D,EAAA,GAEAe,EAAA,EAAAuQ,EAAAnR,EAAAH,OAAkCe,EAAAuQ,EAAOvQ,IAAA,CACzC,GAAAgZ,GAAA5Z,EAAAhB,OAAA4B,EAEA,UAAAgZ,EAAA,CAKA,QAAA/Z,OAAA8Z,EAAAjX,OAAA7C,IAEA,MAAA+B,GAAAoJ,EAAA,IAKA,IAFAzH,EAAAvD,EAAAJ,OAAAgB,EAAA,EAAA+Y,GAEA9Z,GAAA0D,EAAA1D,OAEA,MAAA+B,GAAAoJ,EAAA,IAGA,IAAAzH,EAAA1D,OAAA,CAGA,GAFAsC,EAAAlG,EAAAoa,aAAA9S,EAAAuK,GAAA,GAEA9C,EAAA/J,OAAAkB,EAAAlB,MAAA+J,EAAAhL,OAAAmC,EAAAnC,KAEA,MAAA4B,GAAAoJ,EAAA,IAGA,IAAA6O,GAAAjY,EAAAO,EAAAvB,EAAA+Y,EAAAxI,EACA,SAAA0I,EAAA,OAIAjZ,GAAA+Y,EACA9Z,EAAA,OA9BAA,IAAA+Z,EAiCA,WAAA/Z,EAEA+B,EAAAoJ,EAAA,KAFA,QAqBA/O,EAAAud,2BAAA,SAAAzD,EAAAnU,GAKA,QAAAyX,GAAAlX,EAAAmX,GACArd,EAAA+a,aAAA7U,GAAA,cAAAnC,GACA,MAAAsZ,GAAA,KAAAtZ,KANA,MAAA+V,GAAAlW,WAUA0X,GAAAxB,EAAAsD,EAAA,SAAArO,EAAAY,GACA,GAAAkO,GAAAlO,EAAAmO,OAAA,SAAAC,EAAAjd,GACA,GAAA6I,EAMA,OAJAA,GADA,gBAAA7I,GACAA,EAAA8C,OAEA9C,EAAA8Z,WAEAmD,EAAApU,EAAAU,WAAAzG,OAAA+F,EAAA,GACK,GAELqU,EAAA,GAAAtD,YAAAmD,GAEAI,EAAA,CA8BA,OA7BAtO,GAAAuO,QAAA,SAAApd,GACA,GAAAqd,GAAA,gBAAArd,GACAsd,EAAAtd,CACA,IAAAqd,EAAA,CAEA,OADAE,GAAA,GAAA3D,YAAA5Z,EAAA8C,QACAe,EAAA,EAAuBA,EAAA7D,EAAA8C,OAAce,IACrC0Z,EAAA1Z,GAAA7D,EAAAwd,WAAA3Z,EAEAyZ,GAAAC,EAAAzS,OAGAuS,EACAH,EAAAC,KAAA,EAEAD,EAAAC,KAAA,CAIA,QADAM,GAAAH,EAAAxD,WAAAvQ,WACA1F,EAAA,EAAqBA,EAAA4Z,EAAA3a,OAAmBe,IACxCqZ,EAAAC,KAAAO,SAAAD,EAAA5Z,GAEAqZ,GAAAC,KAAA,GAGA,QADAI,GAAA,GAAA3D,YAAA0D,GACAzZ,EAAA,EAAqBA,EAAA0Z,EAAAza,OAAiBe,IACtCqZ,EAAAC,KAAAI,EAAA1Z,KAIAgB,EAAAqY,EAAApS,UApDAjG,EAAA,GAAA+F,aAAA,KA4DA1L,EAAAsd,oBAAA,SAAAxD,EAAAnU,GACA,QAAAyX,GAAAlX,EAAAmX,GACArd,EAAA+a,aAAA7U,GAAA,cAAAqW,GACA,GAAAkC,GAAA,GAAA/D,YAAA,EAEA,IADA+D,EAAA,KACA,gBAAAlC,GAAA,CAEA,OADA8B,GAAA,GAAA3D,YAAA6B,EAAA3Y,QACAe,EAAA,EAAuBA,EAAA4X,EAAA3Y,OAAoBe,IAC3C0Z,EAAA1Z,GAAA4X,EAAA+B,WAAA3Z,EAEA4X,GAAA8B,EAAAzS,OACA6S,EAAA,KASA,OANA9U,GAAA4S,YAAA7Q,aACA6Q,EAAA3B,WACA2B,EAAAmC,KAEAH,EAAA5U,EAAAU,WACAsU,EAAA,GAAAjE,YAAA6D,EAAA3a,OAAA,GACAe,EAAA,EAAqBA,EAAA4Z,EAAA3a,OAAmBe,IACxCga,EAAAha,GAAA6Z,SAAAD,EAAA5Z,GAIA,IAFAga,EAAAJ,EAAA3a,QAAA,IAEA4G,EAAA,CACA,GAAA0Q,GAAA,GAAA1Q,IAAAiU,EAAA7S,OAAA+S,EAAA/S,OAAA2Q,GACAc,GAAA,KAAAnC,MAKAI,EAAAxB,EAAAsD,EAAA,SAAArO,EAAAyO,GACA,MAAA7X,GAAA,GAAA6E,GAAAgT,OAaAxd,EAAAyd,sBAAA,SAAA1Z,EAAA8N,EAAAlM,GACA,kBAAAkM,KACAlM,EAAAkM,EACAA,EAAA,KAMA,KAHA,GAAA+M,GAAA7a,EACAoC,KAEAyY,EAAAhE,WAAA,IAKA,OAJAiE,GAAA,GAAAnE,YAAAkE,GACAT,EAAA,IAAAU,EAAA,GACAC,EAAA,GAEAna,EAAA,EACA,MAAAka,EAAAla,GADqBA,IAAA,CAIrB,GAAAma,EAAAlb,OAAA,IACA,MAAA+B,GAAAoJ,EAAA,IAGA+P,IAAAD,EAAAla,GAGAia,EAAA7C,EAAA6C,EAAA,EAAAE,EAAAlb,QACAkb,EAAAN,SAAAM,EAEA,IAAAxX,GAAAyU,EAAA6C,EAAA,EAAAE,EACA,IAAAX,EACA,IACA7W,EAAAkV,OAAAG,aAAA1T,MAAA,QAAAyR,YAAApT,IACO,MAAA9C,GAEP,GAAAoY,GAAA,GAAAlC,YAAApT,EACAA,GAAA,EACA,QAAA3C,GAAA,EAAuBA,EAAAiY,EAAAhZ,OAAkBe,IACzC2C,GAAAkV,OAAAG,aAAAC,EAAAjY,IAKAwB,EAAAmC,KAAAhB,GACAsX,EAAA7C,EAAA6C,EAAAE,GAGA,GAAAnF,GAAAxT,EAAAvC,MACAuC,GAAA+X,QAAA,SAAAtS,EAAAjH,GACAgB,EAAA3F,EAAAoa,aAAAxO,EAAAiG,GAAA,GAAAlN,EAAAgV,OrBq3GM,SAAU1Z,EAAQD,GsBv8HxBC,EAAAD,QAAAsK,OAAAuI,MAAA,SAAAzQ,GACA,GAAAiJ,MACAgC,EAAA/C,OAAAhI,UAAAgL,cAEA,QAAA3I,KAAAvC,GACAiL,EAAA1M,KAAAyB,EAAAuC,IACA0G,EAAA/C,KAAA3D,EAGA,OAAA0G,KtBu9HM,SAAUpL,EAAQD,EAASM,GuB38HjC,QAAAwb,GAAA1Z,GACA,IAAAA,GAAA,gBAAAA,GACA,QAGA,IAAA8E,EAAA9E,GAAA,CACA,OAAAuC,GAAA,EAAAuQ,EAAA9S,EAAAwB,OAAmCe,EAAAuQ,EAAOvQ,IAC1C,GAAAmX,EAAA1Z,EAAAuC,IACA,QAGA,UAGA,qBAAA4G,gBAAAC,UAAAD,OAAAC,SAAApJ,IACA,kBAAAsJ,cAAAtJ,YAAAsJ,cACAnB,GAAAnI,YAAAoI,OACAC,GAAArI,YAAAsI,MAEA,QAIA,IAAAtI,EAAA2c,QAAA,kBAAA3c,GAAA2c,QAAA,IAAA7V,UAAAtF,OACA,MAAAkY,GAAA1Z,EAAA2c,UAAA,EAGA,QAAAtW,KAAArG,GACA,GAAAkI,OAAAhI,UAAAgL,eAAA3M,KAAAyB,EAAAqG,IAAAqT,EAAA1Z,EAAAqG,IACA,QAIA,UAxDA,GAAAvB,GAAA5G,EAAA,GAEA+J,EAAAC,OAAAhI,UAAA+H,SACAE,EAAA,kBAAAC,OACA,mBAAAA,OAAA,6BAAAH,EAAA1J,KAAA6J,MACAC,EAAA,kBAAAC,OACA,mBAAAA,OAAA,6BAAAL,EAAA1J,KAAA+J,KAMAzK,GAAAD,QAAA8b,GvB4hIM,SAAU7b,EAAQD,GwBviIxBC,EAAAD,QAAA,SAAAgf,EAAAC,EAAAC,GACA,GAAAC,GAAAH,EAAApE,UAIA,IAHAqE,KAAA,EACAC,KAAAC,EAEAH,EAAAtV,MAA0B,MAAAsV,GAAAtV,MAAAuV,EAAAC,EAM1B,IAJAD,EAAA,IAAkBA,GAAAE,GAClBD,EAAA,IAAgBA,GAAAC,GAChBD,EAAAC,IAAoBD,EAAAC,GAEpBF,GAAAE,GAAAF,GAAAC,GAAA,IAAAC,EACA,UAAAzT,aAAA,EAKA,QAFA0T,GAAA,GAAA1E,YAAAsE,GACA7T,EAAA,GAAAuP,YAAAwE,EAAAD,GACAta,EAAAsa,EAAAI,EAAA,EAA6B1a,EAAAua,EAASva,IAAA0a,IACtClU,EAAAkU,GAAAD,EAAAza,EAEA,OAAAwG,GAAAS,SxBsjIM,SAAU3L,EAAQD,GyB/kIxB,QAAA0b,GAAA4D,EAAA3Z,EAAA4Z,GAOA,QAAAC,GAAAzQ,EAAA5D,GACA,GAAAqU,EAAAF,OAAA,EACA,SAAAzY,OAAA,iCAEA2Y,EAAAF,MAGAvQ,GACA0Q,GAAA,EACA9Z,EAAAoJ,GAEApJ,EAAA4Z,GACS,IAAAC,EAAAF,OAAAG,GACT9Z,EAAA,KAAAwF,GAnBA,GAAAsU,IAAA,CAIA,OAHAF,MAAAnD,EACAoD,EAAAF,QAEA,IAAAA,EAAA3Z,IAAA6Z,EAoBA,QAAApD,MA3BAnc,EAAAD,QAAA0b,GzBmnIM,SAAUzb,EAAQD,G0B9mIxB,QAAA0f,GAAAC,GAMA,IALA,GAGAC,GACAC,EAJAC,KACAC,EAAA,EACAnc,EAAA+b,EAAA/b,OAGAmc,EAAAnc,GACAgc,EAAAD,EAAArB,WAAAyB,KACAH,GAAA,OAAAA,GAAA,OAAAG,EAAAnc,GAEAic,EAAAF,EAAArB,WAAAyB,KACA,cAAAF,GACAC,EAAAxX,OAAA,KAAAsX,IAAA,UAAAC,GAAA,QAIAC,EAAAxX,KAAAsX,GACAG,MAGAD,EAAAxX,KAAAsX,EAGA,OAAAE,GAIA,QAAAE,GAAAC,GAKA,IAJA,GAEAL,GAFAhc,EAAAqc,EAAArc,OACA8L,GAAA,EAEAoQ,EAAA,KACApQ,EAAA9L,GACAgc,EAAAK,EAAAvQ,GACAkQ,EAAA,QACAA,GAAA,MACAE,GAAAI,EAAAN,IAAA,eACAA,EAAA,WAAAA,GAEAE,GAAAI,EAAAN,EAEA,OAAAE,GAGA,QAAAK,GAAAC,EAAA/E,GACA,GAAA+E,GAAA,OAAAA,GAAA,OACA,GAAA/E,EACA,KAAAxU,OACA,oBAAAuZ,EAAA/V,SAAA,IAAAgW,cACA,yBAGA,UAEA,SAIA,QAAAC,GAAAF,EAAArQ,GACA,MAAAmQ,GAAAE,GAAArQ,EAAA,QAGA,QAAAwQ,GAAAH,EAAA/E,GACA,kBAAA+E,GACA,MAAAF,GAAAE,EAEA,IAAAI,GAAA,EAiBA,OAhBA,gBAAAJ,GACAI,EAAAN,EAAAE,GAAA,UAEA,eAAAA,IACAD,EAAAC,EAAA/E,KACA+E,EAAA,OAEAI,EAAAN,EAAAE,GAAA,WACAI,GAAAF,EAAAF,EAAA,IAEA,eAAAA,KACAI,EAAAN,EAAAE,GAAA,UACAI,GAAAF,EAAAF,EAAA,IACAI,GAAAF,EAAAF,EAAA,IAEAI,GAAAN,EAAA,GAAAE,EAAA,KAIA,QAAA9D,GAAAqD,EAAA1e,GACAA,OAQA,KAPA,GAKAmf,GALA/E,GAAA,IAAApa,EAAAoa,OAEAoF,EAAAf,EAAAC,GACA/b,EAAA6c,EAAA7c,OACA8L,GAAA,EAEAgR,EAAA,KACAhR,EAAA9L,GACAwc,EAAAK,EAAA/Q,GACAgR,GAAAH,EAAAH,EAAA/E,EAEA,OAAAqF,GAKA,QAAAC,KACA,GAAAC,GAAAC,EACA,KAAAha,OAAA,qBAGA,IAAAia,GAAA,IAAAC,EAAAH,EAGA,IAFAA,IAEA,UAAAE,GACA,UAAAA,CAIA,MAAAja,OAAA,6BAGA,QAAAma,GAAA3F,GACA,GAAA4F,GACAC,EACAC,EACAC,EACAhB,CAEA,IAAAQ,EAAAC,EACA,KAAAha,OAAA,qBAGA,IAAA+Z,GAAAC,EACA,QAQA,IAJAI,EAAA,IAAAF,EAAAH,GACAA,IAGA,QAAAK,GACA,MAAAA,EAIA,cAAAA,GAAA,CAGA,GAFAC,EAAAP,IACAP,GAAA,GAAAa,IAAA,EAAAC,EACAd,GAAA,IACA,MAAAA,EAEA,MAAAvZ,OAAA,6BAKA,aAAAoa,GAAA,CAIA,GAHAC,EAAAP,IACAQ,EAAAR,IACAP,GAAA,GAAAa,IAAA,GAAAC,GAAA,EAAAC,EACAf,GAAA,KACA,MAAAD,GAAAC,EAAA/E,GAAA+E,EAAA,KAEA,MAAAvZ,OAAA,6BAKA,aAAAoa,KACAC,EAAAP,IACAQ,EAAAR,IACAS,EAAAT,IACAP,GAAA,EAAAa,IAAA,GAAAC,GAAA,GACAC,GAAA,EAAAC,EACAhB,GAAA,OAAAA,GAAA,SACA,MAAAA,EAIA,MAAAvZ,OAAA,0BAMA,QAAAkW,GAAA2D,EAAAzf,GACAA,OACA,IAAAoa,IAAA,IAAApa,EAAAoa,MAEA0F,GAAArB,EAAAgB,GACAG,EAAAE,EAAAnd,OACAgd,EAAA,CAGA,KAFA,GACAS,GADAZ,MAEAY,EAAAL,EAAA3F,OAAA,GACAoF,EAAAnY,KAAA+Y,EAEA,OAAArB,GAAAS;AAxMA,GAyLAM,GACAF,EACAD,EA3LAV,EAAA1D,OAAAG,YA2MA1c,GAAAD,SACAshB,QAAA,QACAzZ,OAAAyU,EACAxL,OAAAiM,I1B2nIM,SAAU9c,EAAQD,I2Bp0IxB,SAAAuhB,GACA,YAEAvhB,GAAA6H,OAAA,SAAAmX,GACA,GACAra,GADAwa,EAAA,GAAAzE,YAAAsE,GACArV,EAAAwV,EAAAvb,OAAAqE,EAAA,EAEA,KAAAtD,EAAA,EAAeA,EAAAgF,EAAShF,GAAA,EACxBsD,GAAAsZ,EAAApC,EAAAxa,IAAA,GACAsD,GAAAsZ,GAAA,EAAApC,EAAAxa,KAAA,EAAAwa,EAAAxa,EAAA,OACAsD,GAAAsZ,GAAA,GAAApC,EAAAxa,EAAA,OAAAwa,EAAAxa,EAAA,OACAsD,GAAAsZ,EAAA,GAAApC,EAAAxa,EAAA,GASA,OANAgF,GAAA,MACA1B,IAAAxD,UAAA,EAAAwD,EAAArE,OAAA,OACK+F,EAAA,QACL1B,IAAAxD,UAAA,EAAAwD,EAAArE,OAAA,SAGAqE,GAGAjI,EAAA8Q,OAAA,SAAA7I,GACA,GACAtD,GACA6c,EAAAC,EAAAC,EAAAC,EAFAC,EAAA,IAAA3Z,EAAArE,OACA+F,EAAA1B,EAAArE,OAAA9C,EAAA,CAGA,OAAAmH,IAAArE,OAAA,KACAge,IACA,MAAA3Z,IAAArE,OAAA,IACAge,IAIA,IAAA5C,GAAA,GAAAtT,aAAAkW,GACAzC,EAAA,GAAAzE,YAAAsE,EAEA,KAAAra,EAAA,EAAeA,EAAAgF,EAAShF,GAAA,EACxB6c,EAAAD,EAAAne,QAAA6E,EAAAtD,IACA8c,EAAAF,EAAAne,QAAA6E,EAAAtD,EAAA,IACA+c,EAAAH,EAAAne,QAAA6E,EAAAtD,EAAA,IACAgd,EAAAJ,EAAAne,QAAA6E,EAAAtD,EAAA,IAEAwa,EAAAre,KAAA0gB,GAAA,EAAAC,GAAA,EACAtC,EAAAre,MAAA,GAAA2gB,IAAA,EAAAC,GAAA,EACAvC,EAAAre,MAAA,EAAA4gB,IAAA,KAAAC,CAGA,OAAA3C,KAEC,qE3Bk1IK,SAAU/e,EAAQD,G4Bz1IxB,QAAA6hB,GAAAtG,GACA,MAAAA,GAAAD,IAAA,SAAAwG,GACA,GAAAA,EAAAlW,iBAAAF,aAAA,CACA,GAAA9E,GAAAkb,EAAAlW,MAIA,IAAAkW,EAAAlH,aAAAhU,EAAAgU,WAAA,CACA,GAAAmH,GAAA,GAAArH,YAAAoH,EAAAlH,WACAmH,GAAAC,IAAA,GAAAtH,YAAA9T,EAAAkb,EAAAG,WAAAH,EAAAlH,aACAhU,EAAAmb,EAAAnW,OAGA,MAAAhF,GAGA,MAAAkb,KAIA,QAAAI,GAAA3G,EAAA1L,GACAA,OAEA,IAAAsS,GAAA,GAAAC,EAKA,OAJAP,GAAAtG,GAAA2C,QAAA,SAAAmE,GACAF,EAAAG,OAAAD,KAGAxS,EAAA,KAAAsS,EAAAI,QAAA1S,EAAA7K,MAAAmd,EAAAI,UAGA,QAAAC,GAAAjH,EAAA1L,GACA,UAAArF,MAAAqX,EAAAtG,GAAA1L,OA/EA,GAAAuS,GAAA,mBAAAA,KACA,mBAAAK,qCACA,mBAAAC,6BACA,mBAAAC,gCAOAC,EAAA,WACA,IACA,GAAAC,GAAA,GAAArY,OAAA,MACA,YAAAqY,EAAAnE,KACG,MAAAla,GACH,aASAse,EAAAF,GAAA,WACA,IACA,GAAAre,GAAA,GAAAiG,OAAA,GAAAkQ,aAAA,OACA,YAAAnW,EAAAma,KACG,MAAAla,GACH,aAQAue,EAAAX,GACAA,EAAA9f,UAAAggB,QACAF,EAAA9f,UAAAigB,OA2CA,oBAAA/X,QACA0X,EAAA5f,UAAAkI,KAAAlI,UACAkgB,EAAAlgB,UAAAkI,KAAAlI,WAGArC,EAAAD,QAAA,WACA,MAAA4iB,GACAE,EAAAtY,KAAAgY,EACGO,EACHb,EAEA,W5Bq5IM,SAAUjiB,EAAQD,G6B9+IxBA,EAAA6H,OAAA,SAAAzF,GACA,GAAAiC,GAAA,EAEA,QAAAM,KAAAvC,GACAA,EAAAkL,eAAA3I,KACAN,EAAAT,SAAAS,GAAA,KACAA,GAAA2e,mBAAAre,GAAA,IAAAqe,mBAAA5gB,EAAAuC,IAIA,OAAAN,IAUArE,EAAA8Q,OAAA,SAAAmS,GAGA,OAFAC,MACAC,EAAAF,EAAAvf,MAAA,KACAiB,EAAA,EAAAuQ,EAAAiO,EAAAvf,OAAmCe,EAAAuQ,EAAOvQ,IAAA,CAC1C,GAAAye,GAAAD,EAAAxe,GAAAjB,MAAA,IACAwf,GAAAG,mBAAAD,EAAA,KAAAC,mBAAAD,EAAA,IAEA,MAAAF,K7B8/IM,SAAUjjB,EAAQD,G8BhiJxBC,EAAAD,QAAA,SAAA6iB,EAAAte,GACA,GAAAsE,GAAA,YACAA,GAAAvG,UAAAiC,EAAAjC,UACAugB,EAAAvgB,UAAA,GAAAuG,GACAga,EAAAvgB,UAAAD,YAAAwgB,I9BwiJM,SAAU5iB,EAAQD,G+B7iJxB,YAgBA,SAAA6H,GAAAoC,GACA,GAAAsS,GAAA,EAEA,GACAA,GAAA+G,EAAArZ,EAAArG,GAAA2Y,EACAtS,EAAAsZ,KAAAC,MAAAvZ,EAAArG,SACGqG,EAAA,EAEH,OAAAsS,GAUA,QAAAzL,GAAAzM,GACA,GAAAof,GAAA,CAEA,KAAA9e,EAAA,EAAaA,EAAAN,EAAAT,OAAgBe,IAC7B8e,IAAA7f,EAAA0X,EAAAjX,EAAAtB,OAAA4B,GAGA,OAAA8e,GASA,QAAAlK,KACA,GAAAmK,GAAA7b,GAAA,GAAAsC,MAEA,OAAAuZ,KAAAC,GAAAC,EAAA,EAAAD,EAAAD,GACAA,EAAA,IAAA7b,EAAA+b,KAMA,IA1DA,GAKAD,GALAL,EAAA,mEAAA5f,MAAA,IACAE,EAAA,GACA0X,KACAsI,EAAA,EACAjf,EAAA,EAsDMA,EAAAf,EAAYe,IAAA2W,EAAAgI,EAAA3e,KAKlB4U,GAAA1R,SACA0R,EAAAzI,SACA7Q,EAAAD,QAAAuZ,G/BojJM,SAAUtZ,EAAQD,EAASM,GgCxlJjC,QAAA6W,MASA,QAAA0M,GAAA5iB,GACAmW,EAAAzW,KAAAP,KAAAa,GAEAb,KAAA4B,MAAA5B,KAAA4B,UAIAsH,IAEAA,EAAAuN,EAAAiN,OAAAjN,EAAAiN,YAIA1jB,KAAAsP,MAAApG,EAAA1F,MAGA,IAAA6K,GAAArO,IACAkJ,GAAAhB,KAAA,SAAAhB,GACAmH,EAAA0J,OAAA7Q,KAIAlH,KAAA4B,MAAAgU,EAAA5V,KAAAsP,MAGA,kBAAA/G,mBACAA,iBAAA,0BACA8F,EAAAsV,SAAAtV,EAAAsV,OAAAvU,QAAA2H,KACK,GAhEL,GAAAC,GAAA9W,EAAA,IACAuX,EAAAvX,EAAA,IACAuW,EAAAvW,EAAA,GAMAL,GAAAD,QAAA6jB,CAMA,IAOAva,GAPA0a,EAAA,MACAC,EAAA,MAyDApM,GAAAgM,EAAAzM,GAMAyM,EAAAvhB,UAAAiS,gBAAA,EAQAsP,EAAAvhB,UAAAuX,QAAA,WACAzZ,KAAA2jB,SACA3jB,KAAA2jB,OAAAG,WAAAC,YAAA/jB,KAAA2jB,QACA3jB,KAAA2jB,OAAA,MAGA3jB,KAAAgkB,OACAhkB,KAAAgkB,KAAAF,WAAAC,YAAA/jB,KAAAgkB,MACAhkB,KAAAgkB,KAAA,KACAhkB,KAAAikB,OAAA,MAGAjN,EAAA9U,UAAAuX,QAAAlZ,KAAAP,OASAyjB,EAAAvhB,UAAA4V,OAAA,WACA,GAAAzJ,GAAArO,KACA2jB,EAAAhL,SAAAuL,cAAA,SAEAlkB,MAAA2jB,SACA3jB,KAAA2jB,OAAAG,WAAAC,YAAA/jB,KAAA2jB,QACA3jB,KAAA2jB,OAAA,MAGAA,EAAAxM,OAAA,EACAwM,EAAAzf,IAAAlE,KAAAY,MACA+iB,EAAAvU,QAAA,SAAAhL,GACAiK,EAAAyF,QAAA,mBAAA1P,GAGA,IAAA+f,GAAAxL,SAAAyL,qBAAA,YACAD,GACAA,EAAAL,WAAAO,aAAAV,EAAAQ,IAEAxL,SAAA2L,MAAA3L,SAAA4L,MAAAC,YAAAb,GAEA3jB,KAAA2jB,QAEA,IAAAc,GAAA,mBAAApS,YAAA,SAAAzP,KAAAyP,UAAAwJ,UAEA4I,IACA5V,WAAA,WACA,GAAAoV,GAAAtL,SAAAuL,cAAA,SACAvL,UAAA4L,KAAAC,YAAAP,GACAtL,SAAA4L,KAAAR,YAAAE,IACK,MAYLR,EAAAvhB,UAAAyV,QAAA,SAAAhU,EAAA8E,GA0BA,QAAAic,KACAC,IACAlc,IAGA,QAAAkc,KACA,GAAAtW,EAAA4V,OACA,IACA5V,EAAA2V,KAAAD,YAAA1V,EAAA4V,QACO,MAAA7f,GACPiK,EAAAyF,QAAA,qCAAA1P,GAIA,IAEA,GAAAwgB,GAAA,oCAAAvW,EAAAwW,SAAA,IACAZ,GAAAtL,SAAAuL,cAAAU,GACK,MAAAxgB,GACL6f,EAAAtL,SAAAuL,cAAA,UACAD,EAAA5Q,KAAAhF,EAAAwW,SACAZ,EAAA/f,IAAA,eAGA+f,EAAA5jB,GAAAgO,EAAAwW,SAEAxW,EAAA2V,KAAAQ,YAAAP,GACA5V,EAAA4V,SApDA,GAAA5V,GAAArO,IAEA,KAAAA,KAAAgkB,KAAA,CACA,GAGAC,GAHAD,EAAArL,SAAAuL,cAAA,QACAY,EAAAnM,SAAAuL,cAAA,YACA7jB,EAAAL,KAAA6kB,SAAA,cAAA7kB,KAAAsP,KAGA0U,GAAAe,UAAA,WACAf,EAAAgB,MAAAC,SAAA,WACAjB,EAAAgB,MAAAE,IAAA,UACAlB,EAAAgB,MAAAG,KAAA,UACAnB,EAAAoB,OAAA/kB,EACA2jB,EAAA9M,OAAA,OACA8M,EAAAqB,aAAA,0BACAP,EAAAzR,KAAA,IACA2Q,EAAAQ,YAAAM,GACAnM,SAAA4L,KAAAC,YAAAR,GAEAhkB,KAAAgkB,OACAhkB,KAAA8kB,OAGA9kB,KAAAgkB,KAAAsB,OAAAtlB,KAAAY,MAgCA+jB,IAIAhhB,IAAAN,QAAAwgB,EAAA,QACA7jB,KAAA8kB,KAAAtF,MAAA7b,EAAAN,QAAAugB,EAAA,MAEA,KACA5jB,KAAAgkB,KAAAuB,SACG,MAAAnhB,IAEHpE,KAAAikB,OAAAjL,YACAhZ,KAAAikB,OAAA3L,mBAAA,WACA,aAAAjK,EAAA4V,OAAA3X,YACAoY,KAIA1kB,KAAAikB,OAAAnZ,OAAA4Z,IhCgoJM,SAAU7kB,EAAQD,EAASM,GiCrzJjC,QAAAslB,GAAA3kB,GACA,GAAAiQ,GAAAjQ,KAAAiQ,WACAA,KACA9Q,KAAAmU,gBAAA,GAEAnU,KAAA2R,kBAAA9Q,EAAA8Q,kBACA3R,KAAAylB,sBAAAC,IAAA7kB,EAAAsR,UACAnS,KAAA0T,UAAA7S,EAAA6S,UACA1T,KAAAylB,wBACAE,EAAAC,GAEAzS,EAAA5S,KAAAP,KAAAa,GArDA,GAOA6kB,GAAAE,EAPAzS,EAAAjT,EAAA,IACAiC,EAAAjC,EAAA,IACAuQ,EAAAvQ,EAAA,IACAuX,EAAAvX,EAAA,IACAiZ,EAAAjZ,EAAA,GACAA,GAAA,gCAUA,IANA,mBAAA2lB,WACAH,EAAAG,UACC,mBAAAxX,QACDqX,EAAArX,KAAAwX,WAAAxX,KAAAyX,cAGA,mBAAAjP,QACA,IACA+O,EAAA1lB,EAAA,IACG,MAAAkE,IASH,GAAAuhB,GAAAD,GAAAE,CAMA/lB,GAAAD,QAAA4lB,EA2BA/N,EAAA+N,EAAArS,GAQAqS,EAAAtjB,UAAAmR,KAAA,YAMAmS,EAAAtjB,UAAAiS,gBAAA,EAQAqR,EAAAtjB,UAAAkX,OAAA,WACA,GAAApZ,KAAA+lB,QAAA,CAKA,GAAAnlB,GAAAZ,KAAAY,MACA8S,EAAA1T,KAAA0T,UAEA7S,IAEAb,MAAAoS,gBACAvR,EAAA2P,MAAAxQ,KAAAwQ,MACA3P,EAAA8Q,kBAAA3R,KAAA2R,kBAGA9Q,EAAAgR,IAAA7R,KAAA6R,IACAhR,EAAAwH,IAAArI,KAAAqI,IACAxH,EAAAiR,WAAA9R,KAAA8R,WACAjR,EAAAkR,KAAA/R,KAAA+R,KACAlR,EAAAmR,GAAAhS,KAAAgS,GACAnR,EAAAoR,QAAAjS,KAAAiS,QACApR,EAAAqR,mBAAAlS,KAAAkS,oBAGAlS,KAAAwS,eACA3R,EAAAmlB,QAAAhmB,KAAAwS,cAEAxS,KAAA0S,eACA7R,EAAA6R,aAAA1S,KAAA0S,aAGA,KACA1S,KAAAimB,GACAjmB,KAAAylB,wBAAAzlB,KAAAoS,cACAsB,EACA,GAAAiS,GAAA/kB,EAAA8S,GACA,GAAAiS,GAAA/kB,GACA,GAAA+kB,GAAA/kB,EAAA8S,EAAA7S,GACG,MAAA8N,GACH,MAAA3O,MAAA4H,KAAA,QAAA+G,GAGA5N,SAAAf,KAAAimB,GAAAxU,aACAzR,KAAAmU,gBAAA,GAGAnU,KAAAimB,GAAAC,UAAAlmB,KAAAimB,GAAAC,SAAAvgB,QACA3F,KAAAmU,gBAAA,EACAnU,KAAAimB,GAAAxU,WAAA,cAEAzR,KAAAimB,GAAAxU,WAAA,cAGAzR,KAAAmmB,sBASAX,EAAAtjB,UAAAikB,kBAAA,WACA,GAAA9X,GAAArO,IAEAA,MAAAimB,GAAAzX,OAAA,WACAH,EAAAwG,UAEA7U,KAAAimB,GAAAjW,QAAA,WACA3B,EAAA0F,WAEA/T,KAAAimB,GAAAG,UAAA,SAAAC,GACAhY,EAAA0J,OAAAsO,EAAA1iB,OAEA3D,KAAAimB,GAAA7W,QAAA,SAAAhL,GACAiK,EAAAyF,QAAA,kBAAA1P,KAWAohB,EAAAtjB,UAAAsN,MAAA,SAAAkK,GA4CA,QAAA2B,KACAhN,EAAAzG,KAAA,SAIAiH,WAAA,WACAR,EAAAiH,UAAA,EACAjH,EAAAzG,KAAA,UACK,GAnDL,GAAAyG,GAAArO,IACAA,MAAAsV,UAAA,CAKA,QADAiE,GAAAG,EAAAlW,OACAe,EAAA,EAAAuQ,EAAAyE,EAA4BhV,EAAAuQ,EAAOvQ,KACnC,SAAAuB,GACA3D,EAAAwY,aAAA7U,EAAAuI,EAAA8F,eAAA,SAAAxQ,GACA,IAAA0K,EAAAoX,sBAAA,CAEA,GAAA5kB,KAKA,IAJAiF,EAAA2J,UACA5O,EAAA0U,SAAAzP,EAAA2J,QAAA8F,UAGAlH,EAAAsD,kBAAA,CACA,GAAApI,GAAA,gBAAA5F,GAAAwH,OAAAqP,WAAA7W,KAAAH,MACA+F,GAAA8E,EAAAsD,kBAAAC,YACA/Q,EAAA0U,UAAA,IAQA,IACAlH,EAAAoX,sBAEApX,EAAA4X,GAAA5R,KAAA1Q,GAEA0K,EAAA4X,GAAA5R,KAAA1Q,EAAA9C,GAES,MAAAuD,MAITmV,GAAA8B,OAEK3B,EAAAnV,KAqBLihB,EAAAtjB,UAAA6R,QAAA,WACAZ,EAAAjR,UAAA6R,QAAAxT,KAAAP,OASAwlB,EAAAtjB,UAAAuX,QAAA,WACA,mBAAAzZ,MAAAimB,IACAjmB,KAAAimB,GAAAnX,SAUA0W,EAAAtjB,UAAAtB,IAAA,WACA,GAAAgB,GAAA5B,KAAA4B,UACAiY,EAAA7Z,KAAAuQ,OAAA,WACAzN,EAAA,EAGA9C,MAAA8C,OAAA,QAAA+W,GAAA,MAAAxT,OAAArG,KAAA8C,OACA,OAAA+W,GAAA,KAAAxT,OAAArG,KAAA8C,SACAA,EAAA,IAAA9C,KAAA8C,MAIA9C,KAAAkR,oBACAtP,EAAA5B,KAAAiR,gBAAAkI,KAIAnZ,KAAAmU,iBACAvS,EAAAkY,IAAA,GAGAlY,EAAA6O,EAAAhJ,OAAA7F,GAGAA,EAAA4B,SACA5B,EAAA,IAAAA,EAGA,IAAAmB,GAAA/C,KAAAsQ,SAAAtN,QAAA,SACA,OAAA6W,GAAA,OAAA9W,EAAA,IAAA/C,KAAAsQ,SAAA,IAAAtQ,KAAAsQ,UAAAxN,EAAA9C,KAAAoB,KAAAQ,GAUA4jB,EAAAtjB,UAAA6jB,MAAA,WACA,SAAAJ,GAAA,gBAAAA,IAAA3lB,KAAAqT,OAAAmS,EAAAtjB,UAAAmR,QjC22JM,SAAUxT,EAAQD,KAMlB,SAAUC,EAAQD,GkCzpKxB,GAAAoD,aAEAnD,GAAAD,QAAA,SAAAqL,EAAAjJ,GACA,GAAAgB,EAAA,MAAAiI,GAAAjI,QAAAhB,EACA,QAAAuC,GAAA,EAAiBA,EAAA0G,EAAAzH,SAAgBe,EACjC,GAAA0G,EAAA1G,KAAAvC,EAAA,MAAAuC,EAEA,YlCiqKM,SAAU1E,EAAQD,EAASM,GAEhC,YmCpnKD,SAASqC,GAAQvB,EAAIgE,EAAKnE,GACxBb,KAAKgB,GAAKA,EACVhB,KAAKgF,IAAMA,EACXhF,KAAKsmB,KAAOtmB,KACZA,KAAKumB,IAAM,EACXvmB,KAAKwmB,QACLxmB,KAAKymB,iBACLzmB,KAAK0mB,cACL1mB,KAAK2mB,WAAY,EACjB3mB,KAAK4mB,cAAe,EACpB5mB,KAAK6mB,SACDhmB,GAAQA,EAAKe,QACf5B,KAAK4B,MAAQf,EAAKe,OAEhB5B,KAAKgB,GAAG6L,aAAa7M,KAAK8M,OnCwmK/B,GAAIhM,GAA4B,kBAAXgB,SAAoD,gBAApBA,QAAOC,SAAwB,SAAUC,GAAO,aAAcA,IAAS,SAAUA,GAAO,MAAOA,IAAyB,kBAAXF,SAAyBE,EAAIC,cAAgBH,QAAUE,IAAQF,OAAOI,UAAY,eAAkBF,ImCxqKnQG,EAASjC,EAAQ,GACjBiH,EAAUjH,EAAQ,GAClB4mB,EAAU5mB,EAAQ,IAClBoI,EAAKpI,EAAQ,IACb8M,EAAO9M,EAAQ,IAEfuQ,GADQvQ,EAAQ,GAAS,2BACfA,EAAQ,KAClB6mB,EAAS7mB,EAAQ,GAMrBL,GAAOD,QAAUA,EAAU2C,CAS3B,IAAIykB,IACF1kB,QAAS,EACT2kB,cAAe,EACfC,gBAAiB,EACjB3a,WAAY,EACZuD,WAAY,EACZvJ,MAAO,EACP6H,UAAW,EACX+Y,kBAAmB,EACnBC,iBAAkB,EAClBC,gBAAiB,EACjBnZ,aAAc,EACdkH,KAAM,EACN2G,KAAM,GAOJnU,EAAOT,EAAQjF,UAAU0F,IA6B7BT,GAAQ5E,EAAOL,WAQfK,EAAOL,UAAUolB,UAAY,WAC3B,IAAItnB,KAAKyL,KAAT,CAEA,GAAIzK,GAAKhB,KAAKgB,EACdhB,MAAKyL,MACHnD,EAAGtH,EAAI,OAAQgM,EAAKhN,KAAM,WAC1BsI,EAAGtH,EAAI,SAAUgM,EAAKhN,KAAM,aAC5BsI,EAAGtH,EAAI,QAASgM,EAAKhN,KAAM,eAU/BuC,EAAOL,UAAU4K,KACjBvK,EAAOL,UAAUI,QAAU,WACzB,MAAItC,MAAK2mB,UAAkB3mB,MAE3BA,KAAKsnB,YACAtnB,KAAKgB,GAAGkN,cAAclO,KAAKgB,GAAG8L,OAC/B,SAAW9M,KAAKgB,GAAGsL,YAAYtM,KAAKwO,SACxCxO,KAAK4H,KAAK,cACH5H,OAUTuC,EAAOL,UAAUmS,KAAO,WACtB,GAAIjL,GAAO0d,EAAQhe,UAGnB,OAFAM,GAAKpD,QAAQ,WACbhG,KAAK4H,KAAKiB,MAAM7I,KAAMoJ,GACfpJ,MAYTuC,EAAOL,UAAU0F,KAAO,SAAUye,GAChC,GAAIW,EAAO9Z,eAAemZ,GAExB,MADAze,GAAKiB,MAAM7I,KAAM8I,WACV9I,IAGT,IAAIoJ,GAAO0d,EAAQhe,WACfhD,GACFlB,MAA6B7D,SAAtBf,KAAK6mB,MAAMlhB,OAAuB3F,KAAK6mB,MAAMlhB,OAASohB,EAAO3d,IAASjH,EAAO0C,aAAe1C,EAAOoF,MAC1G5D,KAAMyF,EAqBR,OAlBAtD,GAAO2J,WACP3J,EAAO2J,QAAQ8F,UAAYvV,KAAK6mB,QAAS,IAAU7mB,KAAK6mB,MAAMtR,SAG1D,kBAAsBnM,GAAKA,EAAK5F,OAAS,KAE3CxD,KAAKwmB,KAAKxmB,KAAKumB,KAAOnd,EAAKme,MAC3BzhB,EAAOzF,GAAKL,KAAKumB,OAGfvmB,KAAK2mB,UACP3mB,KAAK8F,OAAOA,GAEZ9F,KAAK0mB,WAAWxe,KAAKpC,GAGvB9F,KAAK6mB,SAEE7mB,MAUTuC,EAAOL,UAAU4D,OAAS,SAAUA,GAClCA,EAAOd,IAAMhF,KAAKgF,IAClBhF,KAAKgB,GAAG8E,OAAOA,IASjBvD,EAAOL,UAAUsM,OAAS,WAIxB,GAAI,MAAQxO,KAAKgF,IACf,GAAIhF,KAAK4B,MAAO,CACd,GAAIA,GAA8B,WAAtBd,EAAOd,KAAK4B,OAAqB6O,EAAQhJ,OAAOzH,KAAK4B,OAAS5B,KAAK4B,KAE/E5B,MAAK8F,QAAQlB,KAAMzC,EAAOkF,QAASzF,MAAOA,QAE1C5B,MAAK8F,QAAQlB,KAAMzC,EAAOkF,WAYhC9E,EAAOL,UAAU8N,QAAU,SAAUC,GAEnCjQ,KAAK2mB,WAAY,EACjB3mB,KAAK4mB,cAAe,QACb5mB,MAAKK,GACZL,KAAK4H,KAAK,aAAcqI,IAU1B1N,EAAOL,UAAUslB,SAAW,SAAU1hB,GACpC,GAAIzE,GAAgByE,EAAOd,MAAQhF,KAAKgF,IACpCyiB,EAAqB3hB,EAAOlB,OAASzC,EAAO0E,OAAwB,MAAff,EAAOd,GAEhE,IAAK3D,GAAkBomB,EAEvB,OAAQ3hB,EAAOlB,MACb,IAAKzC,GAAOkF,QACVrH,KAAK0nB,WACL,MAEF,KAAKvlB,GAAOoF,MACVvH,KAAK2nB,QAAQ7hB,EACb,MAEF,KAAK3D,GAAO0C,aACV7E,KAAK2nB,QAAQ7hB,EACb,MAEF,KAAK3D,GAAOqF,IACVxH,KAAK4nB,MAAM9hB,EACX,MAEF,KAAK3D,GAAO2C,WACV9E,KAAK4nB,MAAM9hB,EACX,MAEF,KAAK3D,GAAOmF,WACVtH,KAAK6nB,cACL,MAEF,KAAK1lB,GAAO0E,MACV7G,KAAK4H,KAAK,QAAS9B,EAAOnC,QAYhCpB,EAAOL,UAAUylB,QAAU,SAAU7hB,GACnC,GAAIsD,GAAOtD,EAAOnC,QAGd,OAAQmC,EAAOzF,IAEjB+I,EAAKlB,KAAKlI,KAAK8nB,IAAIhiB,EAAOzF,KAGxBL,KAAK2mB,UACP/e,EAAKiB,MAAM7I,KAAMoJ,GAEjBpJ,KAAKymB,cAAcve,KAAKkB,IAU5B7G,EAAOL,UAAU4lB,IAAM,SAAUznB,GAC/B,GAAIgO,GAAOrO,KACP+nB,GAAO,CACX,OAAO,YAEL,IAAIA,EAAJ,CACAA,GAAO,CACP,IAAI3e,GAAO0d,EAAQhe,UAGnBuF,GAAKvI,QACHlB,KAAMmiB,EAAO3d,GAAQjH,EAAO2C,WAAa3C,EAAOqF,IAChDnH,GAAIA,EACJsD,KAAMyF,OAYZ7G,EAAOL,UAAU0lB,MAAQ,SAAU9hB,GACjC,GAAIgiB,GAAM9nB,KAAKwmB,KAAK1gB,EAAOzF,GACvB,mBAAsBynB,KAExBA,EAAIjf,MAAM7I,KAAM8F,EAAOnC,YAChB3D,MAAKwmB,KAAK1gB,EAAOzF,MAY5BkC,EAAOL,UAAUwlB,UAAY,WAC3B1nB,KAAK2mB,WAAY,EACjB3mB,KAAK4mB,cAAe,EACpB5mB,KAAKgoB,eACLhoB,KAAK4H,KAAK,YASZrF,EAAOL,UAAU8lB,aAAe,WAC9B,GAAIzjB,EACJ,KAAKA,EAAI,EAAGA,EAAIvE,KAAKymB,cAAcjjB,OAAQe,IACzCqD,EAAKiB,MAAM7I,KAAMA,KAAKymB,cAAcliB,GAItC,KAFAvE,KAAKymB,iBAEAliB,EAAI,EAAGA,EAAIvE,KAAK0mB,WAAWljB,OAAQe,IACtCvE,KAAK8F,OAAO9F,KAAK0mB,WAAWniB,GAE9BvE,MAAK0mB,eASPnkB,EAAOL,UAAU2lB,aAAe,WAE9B7nB,KAAK+H,UACL/H,KAAKgQ,QAAQ,yBAWfzN,EAAOL,UAAU6F,QAAU,WACzB,GAAI/H,KAAKyL,KAAM,CAEb,IAAK,GAAIlH,GAAI,EAAGA,EAAIvE,KAAKyL,KAAKjI,OAAQe,IACpCvE,KAAKyL,KAAKlH,GAAGwD,SAEf/H,MAAKyL,KAAO,KAGdzL,KAAKgB,GAAG+G,QAAQ/H,OAUlBuC,EAAOL,UAAU4M,MACjBvM,EAAOL,UAAU4N,WAAa,WAa5B,MAZI9P,MAAK2mB,WAEP3mB,KAAK8F,QAASlB,KAAMzC,EAAOmF,aAI7BtH,KAAK+H,UAED/H,KAAK2mB,WAEP3mB,KAAKgQ,QAAQ,wBAERhQ,MAWTuC,EAAOL,UAAUqT,SAAW,SAAUA,GAEpC,MADAvV,MAAK6mB,MAAMtR,SAAWA,EACfvV,MAWTuC,EAAOL,UAAUyD,OAAS,SAAUA,GAElC,MADA3F,MAAK6mB,MAAMlhB,OAASA,EACb3F,OnCwqKH,SAAUH,EAAQD,GoC1lLxB,QAAAknB,GAAAmB,EAAA3Y,GACA,GAAAuQ,KAEAvQ,MAAA,CAEA,QAAA/K,GAAA+K,GAAA,EAA4B/K,EAAA0jB,EAAAzkB,OAAiBe,IAC7Csb,EAAAtb,EAAA+K,GAAA2Y,EAAA1jB,EAGA,OAAAsb,GAXAhgB,EAAAD,QAAAknB,GpC+mLM,SAAUjnB,EAAQD,GAEvB,YqCjmLD,SAAS0I,GAAItG,EAAKqkB,EAAI5d,GAEpB,MADAzG,GAAIsG,GAAG+d,EAAI5d,IAETV,QAAS,WACP/F,EAAI+G,eAAesd,EAAI5d,KAf7B5I,EAAOD,QAAU0I,GrCwoLX,SAAUzI,EAAQD,GsCzoLxB,GAAA0J,WAWAzJ,GAAAD,QAAA,SAAAoC,EAAAyG,GAEA,GADA,gBAAAA,OAAAzG,EAAAyG,IACA,kBAAAA,GAAA,SAAAhC,OAAA,6BACA,IAAA2C,GAAAE,EAAA/I,KAAAuI,UAAA,EACA,mBACA,MAAAL,GAAAI,MAAA7G,EAAAoH,EAAAuN,OAAArN,EAAA/I,KAAAuI,gBtCspLM,SAAUjJ,EAAQD,GuCvpLxB,QAAAqM,GAAApL,GACAA,QACAb,KAAAkoB,GAAArnB,EAAAqL,KAAA,IACAlM,KAAAmM,IAAAtL,EAAAsL,KAAA,IACAnM,KAAAmoB,OAAAtnB,EAAAsnB,QAAA,EACAnoB,KAAAoM,OAAAvL,EAAAuL,OAAA,GAAAvL,EAAAuL,QAAA,EAAAvL,EAAAuL,OAAA,EACApM,KAAAmO,SAAA,EApBAtO,EAAAD,QAAAqM,EA8BAA,EAAA/J,UAAAiO,SAAA,WACA,GAAA+X,GAAAloB,KAAAkoB,GAAA/E,KAAAiF,IAAApoB,KAAAmoB,OAAAnoB,KAAAmO,WACA,IAAAnO,KAAAoM,OAAA,CACA,GAAAic,GAAAlF,KAAAmF,SACAC,EAAApF,KAAAC,MAAAiF,EAAAroB,KAAAoM,OAAA8b,EACAA,GAAA,MAAA/E,KAAAC,MAAA,GAAAiF,IAAAH,EAAAK,EAAAL,EAAAK,EAEA,SAAApF,KAAAjX,IAAAgc,EAAAloB,KAAAmM,MASAF,EAAA/J,UAAA6N,MAAA,WACA/P,KAAAmO,SAAA,GASAlC,EAAA/J,UAAAyL,OAAA,SAAAzB,GACAlM,KAAAkoB,GAAAhc,GASAD,EAAA/J,UAAA6L,OAAA,SAAA5B,GACAnM,KAAAmM,OASAF,EAAA/J,UAAA2L,UAAA,SAAAzB,GACApM,KAAAoM", "file": "socket.io.slim.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"io\"] = factory();\n\telse\n\t\troot[\"io\"] = factory();\n})(this, function() {\nreturn \n\n\n// WEBPACK FOOTER //\n// webpack/universalModuleDefinition", "(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"io\"] = factory();\n\telse\n\t\troot[\"io\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId])\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\texports: {},\n/******/ \t\t\tid: moduleId,\n/******/ \t\t\tloaded: false\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.loaded = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(0);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t'use strict';\n\t\n\tvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar url = __webpack_require__(1);\n\tvar parser = __webpack_require__(4);\n\tvar Manager = __webpack_require__(9);\n\tvar debug = __webpack_require__(3)('socket.io-client');\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = exports = lookup;\n\t\n\t/**\n\t * Managers cache.\n\t */\n\t\n\tvar cache = exports.managers = {};\n\t\n\t/**\n\t * Looks up an existing `Manager` for multiplexing.\n\t * If the user summons:\n\t *\n\t *   `io('http://localhost/a');`\n\t *   `io('http://localhost/b');`\n\t *\n\t * We reuse the existing instance based on same scheme/port/host,\n\t * and we initialize sockets for each namespace.\n\t *\n\t * @api public\n\t */\n\t\n\tfunction lookup(uri, opts) {\n\t  if ((typeof uri === 'undefined' ? 'undefined' : _typeof(uri)) === 'object') {\n\t    opts = uri;\n\t    uri = undefined;\n\t  }\n\t\n\t  opts = opts || {};\n\t\n\t  var parsed = url(uri);\n\t  var source = parsed.source;\n\t  var id = parsed.id;\n\t  var path = parsed.path;\n\t  var sameNamespace = cache[id] && path in cache[id].nsps;\n\t  var newConnection = opts.forceNew || opts['force new connection'] || false === opts.multiplex || sameNamespace;\n\t\n\t  var io;\n\t\n\t  if (newConnection) {\n\t\n\t    io = Manager(source, opts);\n\t  } else {\n\t    if (!cache[id]) {\n\t\n\t      cache[id] = Manager(source, opts);\n\t    }\n\t    io = cache[id];\n\t  }\n\t  if (parsed.query && !opts.query) {\n\t    opts.query = parsed.query;\n\t  }\n\t  return io.socket(parsed.path, opts);\n\t}\n\t\n\t/**\n\t * Protocol version.\n\t *\n\t * @api public\n\t */\n\t\n\texports.protocol = parser.protocol;\n\t\n\t/**\n\t * `connect`.\n\t *\n\t * @param {String} uri\n\t * @api public\n\t */\n\t\n\texports.connect = lookup;\n\t\n\t/**\n\t * Expose constructors for standalone build.\n\t *\n\t * @api public\n\t */\n\t\n\texports.Manager = __webpack_require__(9);\n\texports.Socket = __webpack_require__(34);\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t'use strict';\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar parseuri = __webpack_require__(2);\n\tvar debug = __webpack_require__(3)('socket.io-client:url');\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = url;\n\t\n\t/**\n\t * URL parser.\n\t *\n\t * @param {String} url\n\t * @param {Object} An object meant to mimic window.location.\n\t *                 Defaults to window.location.\n\t * @api public\n\t */\n\t\n\tfunction url(uri, loc) {\n\t  var obj = uri;\n\t\n\t  // default to window.location\n\t  loc = loc || typeof location !== 'undefined' && location;\n\t  if (null == uri) uri = loc.protocol + '//' + loc.host;\n\t\n\t  // relative path support\n\t  if ('string' === typeof uri) {\n\t    if ('/' === uri.charAt(0)) {\n\t      if ('/' === uri.charAt(1)) {\n\t        uri = loc.protocol + uri;\n\t      } else {\n\t        uri = loc.host + uri;\n\t      }\n\t    }\n\t\n\t    if (!/^(https?|wss?):\\/\\//.test(uri)) {\n\t\n\t      if ('undefined' !== typeof loc) {\n\t        uri = loc.protocol + '//' + uri;\n\t      } else {\n\t        uri = 'https://' + uri;\n\t      }\n\t    }\n\t\n\t    // parse\n\t\n\t    obj = parseuri(uri);\n\t  }\n\t\n\t  // make sure we treat `localhost:80` and `localhost` equally\n\t  if (!obj.port) {\n\t    if (/^(http|ws)$/.test(obj.protocol)) {\n\t      obj.port = '80';\n\t    } else if (/^(http|ws)s$/.test(obj.protocol)) {\n\t      obj.port = '443';\n\t    }\n\t  }\n\t\n\t  obj.path = obj.path || '/';\n\t\n\t  var ipv6 = obj.host.indexOf(':') !== -1;\n\t  var host = ipv6 ? '[' + obj.host + ']' : obj.host;\n\t\n\t  // define unique id\n\t  obj.id = obj.protocol + '://' + host + ':' + obj.port;\n\t  // define href\n\t  obj.href = obj.protocol + '://' + host + (loc && loc.port === obj.port ? '' : ':' + obj.port);\n\t\n\t  return obj;\n\t}\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports) {\n\n\t/**\n\t * Parses an URI\n\t *\n\t * <AUTHOR> Levithan <stevenlevithan.com> (MIT license)\n\t * @api private\n\t */\n\t\n\tvar re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\n\t\n\tvar parts = [\n\t    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n\t];\n\t\n\tmodule.exports = function parseuri(str) {\n\t    var src = str,\n\t        b = str.indexOf('['),\n\t        e = str.indexOf(']');\n\t\n\t    if (b != -1 && e != -1) {\n\t        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n\t    }\n\t\n\t    var m = re.exec(str || ''),\n\t        uri = {},\n\t        i = 14;\n\t\n\t    while (i--) {\n\t        uri[parts[i]] = m[i] || '';\n\t    }\n\t\n\t    if (b != -1 && e != -1) {\n\t        uri.source = src;\n\t        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n\t        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n\t        uri.ipv6uri = true;\n\t    }\n\t\n\t    uri.pathNames = pathNames(uri, uri['path']);\n\t    uri.queryKey = queryKey(uri, uri['query']);\n\t\n\t    return uri;\n\t};\n\t\n\tfunction pathNames(obj, path) {\n\t    var regx = /\\/{2,9}/g,\n\t        names = path.replace(regx, \"/\").split(\"/\");\n\t\n\t    if (path.substr(0, 1) == '/' || path.length === 0) {\n\t        names.splice(0, 1);\n\t    }\n\t    if (path.substr(path.length - 1, 1) == '/') {\n\t        names.splice(names.length - 1, 1);\n\t    }\n\t\n\t    return names;\n\t}\n\t\n\tfunction queryKey(uri, query) {\n\t    var data = {};\n\t\n\t    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n\t        if ($1) {\n\t            data[$1] = $2;\n\t        }\n\t    });\n\t\n\t    return data;\n\t}\n\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports) {\n\n\t\"use strict\";\n\t\n\tmodule.exports = function () {\n\t  return function () {};\n\t};\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar debug = __webpack_require__(3)('socket.io-parser');\n\tvar Emitter = __webpack_require__(5);\n\tvar binary = __webpack_require__(6);\n\tvar isArray = __webpack_require__(7);\n\tvar isBuf = __webpack_require__(8);\n\t\n\t/**\n\t * Protocol version.\n\t *\n\t * @api public\n\t */\n\t\n\texports.protocol = 4;\n\t\n\t/**\n\t * Packet types.\n\t *\n\t * @api public\n\t */\n\t\n\texports.types = [\n\t  'CONNECT',\n\t  'DISCONNECT',\n\t  'EVENT',\n\t  'ACK',\n\t  'ERROR',\n\t  'BINARY_EVENT',\n\t  'BINARY_ACK'\n\t];\n\t\n\t/**\n\t * Packet type `connect`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.CONNECT = 0;\n\t\n\t/**\n\t * Packet type `disconnect`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.DISCONNECT = 1;\n\t\n\t/**\n\t * Packet type `event`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.EVENT = 2;\n\t\n\t/**\n\t * Packet type `ack`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.ACK = 3;\n\t\n\t/**\n\t * Packet type `error`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.ERROR = 4;\n\t\n\t/**\n\t * Packet type 'binary event'\n\t *\n\t * @api public\n\t */\n\t\n\texports.BINARY_EVENT = 5;\n\t\n\t/**\n\t * Packet type `binary ack`. For acks with binary arguments.\n\t *\n\t * @api public\n\t */\n\t\n\texports.BINARY_ACK = 6;\n\t\n\t/**\n\t * Encoder constructor.\n\t *\n\t * @api public\n\t */\n\t\n\texports.Encoder = Encoder;\n\t\n\t/**\n\t * Decoder constructor.\n\t *\n\t * @api public\n\t */\n\t\n\texports.Decoder = Decoder;\n\t\n\t/**\n\t * A socket.io Encoder instance\n\t *\n\t * @api public\n\t */\n\t\n\tfunction Encoder() {}\n\t\n\tvar ERROR_PACKET = exports.ERROR + '\"encode error\"';\n\t\n\t/**\n\t * Encode a packet as a single string if non-binary, or as a\n\t * buffer sequence, depending on packet type.\n\t *\n\t * @param {Object} obj - packet object\n\t * @param {Function} callback - function to handle encodings (likely engine.write)\n\t * @return Calls callback with Array of encodings\n\t * @api public\n\t */\n\t\n\tEncoder.prototype.encode = function(obj, callback){\n\t\n\t\n\t  if (exports.BINARY_EVENT === obj.type || exports.BINARY_ACK === obj.type) {\n\t    encodeAsBinary(obj, callback);\n\t  } else {\n\t    var encoding = encodeAsString(obj);\n\t    callback([encoding]);\n\t  }\n\t};\n\t\n\t/**\n\t * Encode packet as string.\n\t *\n\t * @param {Object} packet\n\t * @return {String} encoded\n\t * @api private\n\t */\n\t\n\tfunction encodeAsString(obj) {\n\t\n\t  // first is type\n\t  var str = '' + obj.type;\n\t\n\t  // attachments if we have them\n\t  if (exports.BINARY_EVENT === obj.type || exports.BINARY_ACK === obj.type) {\n\t    str += obj.attachments + '-';\n\t  }\n\t\n\t  // if we have a namespace other than `/`\n\t  // we append it followed by a comma `,`\n\t  if (obj.nsp && '/' !== obj.nsp) {\n\t    str += obj.nsp + ',';\n\t  }\n\t\n\t  // immediately followed by the id\n\t  if (null != obj.id) {\n\t    str += obj.id;\n\t  }\n\t\n\t  // json data\n\t  if (null != obj.data) {\n\t    var payload = tryStringify(obj.data);\n\t    if (payload !== false) {\n\t      str += payload;\n\t    } else {\n\t      return ERROR_PACKET;\n\t    }\n\t  }\n\t\n\t\n\t  return str;\n\t}\n\t\n\tfunction tryStringify(str) {\n\t  try {\n\t    return JSON.stringify(str);\n\t  } catch(e){\n\t    return false;\n\t  }\n\t}\n\t\n\t/**\n\t * Encode packet as 'buffer sequence' by removing blobs, and\n\t * deconstructing packet into object with placeholders and\n\t * a list of buffers.\n\t *\n\t * @param {Object} packet\n\t * @return {Buffer} encoded\n\t * @api private\n\t */\n\t\n\tfunction encodeAsBinary(obj, callback) {\n\t\n\t  function writeEncoding(bloblessData) {\n\t    var deconstruction = binary.deconstructPacket(bloblessData);\n\t    var pack = encodeAsString(deconstruction.packet);\n\t    var buffers = deconstruction.buffers;\n\t\n\t    buffers.unshift(pack); // add packet info to beginning of data list\n\t    callback(buffers); // write all the buffers\n\t  }\n\t\n\t  binary.removeBlobs(obj, writeEncoding);\n\t}\n\t\n\t/**\n\t * A socket.io Decoder instance\n\t *\n\t * @return {Object} decoder\n\t * @api public\n\t */\n\t\n\tfunction Decoder() {\n\t  this.reconstructor = null;\n\t}\n\t\n\t/**\n\t * Mix in `Emitter` with Decoder.\n\t */\n\t\n\tEmitter(Decoder.prototype);\n\t\n\t/**\n\t * Decodes an encoded packet string into packet JSON.\n\t *\n\t * @param {String} obj - encoded packet\n\t * @return {Object} packet\n\t * @api public\n\t */\n\t\n\tDecoder.prototype.add = function(obj) {\n\t  var packet;\n\t  if (typeof obj === 'string') {\n\t    packet = decodeString(obj);\n\t    if (exports.BINARY_EVENT === packet.type || exports.BINARY_ACK === packet.type) { // binary packet's json\n\t      this.reconstructor = new BinaryReconstructor(packet);\n\t\n\t      // no attachments, labeled binary but no binary data to follow\n\t      if (this.reconstructor.reconPack.attachments === 0) {\n\t        this.emit('decoded', packet);\n\t      }\n\t    } else { // non-binary full packet\n\t      this.emit('decoded', packet);\n\t    }\n\t  } else if (isBuf(obj) || obj.base64) { // raw binary data\n\t    if (!this.reconstructor) {\n\t      throw new Error('got binary data when not reconstructing a packet');\n\t    } else {\n\t      packet = this.reconstructor.takeBinaryData(obj);\n\t      if (packet) { // received final buffer\n\t        this.reconstructor = null;\n\t        this.emit('decoded', packet);\n\t      }\n\t    }\n\t  } else {\n\t    throw new Error('Unknown type: ' + obj);\n\t  }\n\t};\n\t\n\t/**\n\t * Decode a packet String (JSON data)\n\t *\n\t * @param {String} str\n\t * @return {Object} packet\n\t * @api private\n\t */\n\t\n\tfunction decodeString(str) {\n\t  var i = 0;\n\t  // look up type\n\t  var p = {\n\t    type: Number(str.charAt(0))\n\t  };\n\t\n\t  if (null == exports.types[p.type]) {\n\t    return error('unknown packet type ' + p.type);\n\t  }\n\t\n\t  // look up attachments if type binary\n\t  if (exports.BINARY_EVENT === p.type || exports.BINARY_ACK === p.type) {\n\t    var buf = '';\n\t    while (str.charAt(++i) !== '-') {\n\t      buf += str.charAt(i);\n\t      if (i == str.length) break;\n\t    }\n\t    if (buf != Number(buf) || str.charAt(i) !== '-') {\n\t      throw new Error('Illegal attachments');\n\t    }\n\t    p.attachments = Number(buf);\n\t  }\n\t\n\t  // look up namespace (if any)\n\t  if ('/' === str.charAt(i + 1)) {\n\t    p.nsp = '';\n\t    while (++i) {\n\t      var c = str.charAt(i);\n\t      if (',' === c) break;\n\t      p.nsp += c;\n\t      if (i === str.length) break;\n\t    }\n\t  } else {\n\t    p.nsp = '/';\n\t  }\n\t\n\t  // look up id\n\t  var next = str.charAt(i + 1);\n\t  if ('' !== next && Number(next) == next) {\n\t    p.id = '';\n\t    while (++i) {\n\t      var c = str.charAt(i);\n\t      if (null == c || Number(c) != c) {\n\t        --i;\n\t        break;\n\t      }\n\t      p.id += str.charAt(i);\n\t      if (i === str.length) break;\n\t    }\n\t    p.id = Number(p.id);\n\t  }\n\t\n\t  // look up json data\n\t  if (str.charAt(++i)) {\n\t    var payload = tryParse(str.substr(i));\n\t    var isPayloadValid = payload !== false && (p.type === exports.ERROR || isArray(payload));\n\t    if (isPayloadValid) {\n\t      p.data = payload;\n\t    } else {\n\t      return error('invalid payload');\n\t    }\n\t  }\n\t\n\t\n\t  return p;\n\t}\n\t\n\tfunction tryParse(str) {\n\t  try {\n\t    return JSON.parse(str);\n\t  } catch(e){\n\t    return false;\n\t  }\n\t}\n\t\n\t/**\n\t * Deallocates a parser's resources\n\t *\n\t * @api public\n\t */\n\t\n\tDecoder.prototype.destroy = function() {\n\t  if (this.reconstructor) {\n\t    this.reconstructor.finishedReconstruction();\n\t  }\n\t};\n\t\n\t/**\n\t * A manager of a binary event's 'buffer sequence'. Should\n\t * be constructed whenever a packet of type BINARY_EVENT is\n\t * decoded.\n\t *\n\t * @param {Object} packet\n\t * @return {BinaryReconstructor} initialized reconstructor\n\t * @api private\n\t */\n\t\n\tfunction BinaryReconstructor(packet) {\n\t  this.reconPack = packet;\n\t  this.buffers = [];\n\t}\n\t\n\t/**\n\t * Method to be called when binary data received from connection\n\t * after a BINARY_EVENT packet.\n\t *\n\t * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n\t * @return {null | Object} returns null if more binary data is expected or\n\t *   a reconstructed packet object if all buffers have been received.\n\t * @api private\n\t */\n\t\n\tBinaryReconstructor.prototype.takeBinaryData = function(binData) {\n\t  this.buffers.push(binData);\n\t  if (this.buffers.length === this.reconPack.attachments) { // done with buffer list\n\t    var packet = binary.reconstructPacket(this.reconPack, this.buffers);\n\t    this.finishedReconstruction();\n\t    return packet;\n\t  }\n\t  return null;\n\t};\n\t\n\t/**\n\t * Cleans up binary packet reconstruction variables.\n\t *\n\t * @api private\n\t */\n\t\n\tBinaryReconstructor.prototype.finishedReconstruction = function() {\n\t  this.reconPack = null;\n\t  this.buffers = [];\n\t};\n\t\n\tfunction error(msg) {\n\t  return {\n\t    type: exports.ERROR,\n\t    data: 'parser error: ' + msg\n\t  };\n\t}\n\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\r\n\t/**\r\n\t * Expose `Emitter`.\r\n\t */\r\n\t\r\n\tif (true) {\r\n\t  module.exports = Emitter;\r\n\t}\r\n\t\r\n\t/**\r\n\t * Initialize a new `Emitter`.\r\n\t *\r\n\t * @api public\r\n\t */\r\n\t\r\n\tfunction Emitter(obj) {\r\n\t  if (obj) return mixin(obj);\r\n\t};\r\n\t\r\n\t/**\r\n\t * Mixin the emitter properties.\r\n\t *\r\n\t * @param {Object} obj\r\n\t * @return {Object}\r\n\t * @api private\r\n\t */\r\n\t\r\n\tfunction mixin(obj) {\r\n\t  for (var key in Emitter.prototype) {\r\n\t    obj[key] = Emitter.prototype[key];\r\n\t  }\r\n\t  return obj;\r\n\t}\r\n\t\r\n\t/**\r\n\t * Listen on the given `event` with `fn`.\r\n\t *\r\n\t * @param {String} event\r\n\t * @param {Function} fn\r\n\t * @return {Emitter}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.on =\r\n\tEmitter.prototype.addEventListener = function(event, fn){\r\n\t  this._callbacks = this._callbacks || {};\r\n\t  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\r\n\t    .push(fn);\r\n\t  return this;\r\n\t};\r\n\t\r\n\t/**\r\n\t * Adds an `event` listener that will be invoked a single\r\n\t * time then automatically removed.\r\n\t *\r\n\t * @param {String} event\r\n\t * @param {Function} fn\r\n\t * @return {Emitter}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.once = function(event, fn){\r\n\t  function on() {\r\n\t    this.off(event, on);\r\n\t    fn.apply(this, arguments);\r\n\t  }\r\n\t\r\n\t  on.fn = fn;\r\n\t  this.on(event, on);\r\n\t  return this;\r\n\t};\r\n\t\r\n\t/**\r\n\t * Remove the given callback for `event` or all\r\n\t * registered callbacks.\r\n\t *\r\n\t * @param {String} event\r\n\t * @param {Function} fn\r\n\t * @return {Emitter}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.off =\r\n\tEmitter.prototype.removeListener =\r\n\tEmitter.prototype.removeAllListeners =\r\n\tEmitter.prototype.removeEventListener = function(event, fn){\r\n\t  this._callbacks = this._callbacks || {};\r\n\t\r\n\t  // all\r\n\t  if (0 == arguments.length) {\r\n\t    this._callbacks = {};\r\n\t    return this;\r\n\t  }\r\n\t\r\n\t  // specific event\r\n\t  var callbacks = this._callbacks['$' + event];\r\n\t  if (!callbacks) return this;\r\n\t\r\n\t  // remove all handlers\r\n\t  if (1 == arguments.length) {\r\n\t    delete this._callbacks['$' + event];\r\n\t    return this;\r\n\t  }\r\n\t\r\n\t  // remove specific handler\r\n\t  var cb;\r\n\t  for (var i = 0; i < callbacks.length; i++) {\r\n\t    cb = callbacks[i];\r\n\t    if (cb === fn || cb.fn === fn) {\r\n\t      callbacks.splice(i, 1);\r\n\t      break;\r\n\t    }\r\n\t  }\r\n\t\r\n\t  // Remove event specific arrays for event types that no\r\n\t  // one is subscribed for to avoid memory leak.\r\n\t  if (callbacks.length === 0) {\r\n\t    delete this._callbacks['$' + event];\r\n\t  }\r\n\t\r\n\t  return this;\r\n\t};\r\n\t\r\n\t/**\r\n\t * Emit `event` with the given args.\r\n\t *\r\n\t * @param {String} event\r\n\t * @param {Mixed} ...\r\n\t * @return {Emitter}\r\n\t */\r\n\t\r\n\tEmitter.prototype.emit = function(event){\r\n\t  this._callbacks = this._callbacks || {};\r\n\t\r\n\t  var args = new Array(arguments.length - 1)\r\n\t    , callbacks = this._callbacks['$' + event];\r\n\t\r\n\t  for (var i = 1; i < arguments.length; i++) {\r\n\t    args[i - 1] = arguments[i];\r\n\t  }\r\n\t\r\n\t  if (callbacks) {\r\n\t    callbacks = callbacks.slice(0);\r\n\t    for (var i = 0, len = callbacks.length; i < len; ++i) {\r\n\t      callbacks[i].apply(this, args);\r\n\t    }\r\n\t  }\r\n\t\r\n\t  return this;\r\n\t};\r\n\t\r\n\t/**\r\n\t * Return array of callbacks for `event`.\r\n\t *\r\n\t * @param {String} event\r\n\t * @return {Array}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.listeners = function(event){\r\n\t  this._callbacks = this._callbacks || {};\r\n\t  return this._callbacks['$' + event] || [];\r\n\t};\r\n\t\r\n\t/**\r\n\t * Check if this emitter has `event` handlers.\r\n\t *\r\n\t * @param {String} event\r\n\t * @return {Boolean}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.hasListeners = function(event){\r\n\t  return !! this.listeners(event).length;\r\n\t};\r\n\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/*global Blob,File*/\n\t\n\t/**\n\t * Module requirements\n\t */\n\t\n\tvar isArray = __webpack_require__(7);\n\tvar isBuf = __webpack_require__(8);\n\tvar toString = Object.prototype.toString;\n\tvar withNativeBlob = typeof Blob === 'function' || (typeof Blob !== 'undefined' && toString.call(Blob) === '[object BlobConstructor]');\n\tvar withNativeFile = typeof File === 'function' || (typeof File !== 'undefined' && toString.call(File) === '[object FileConstructor]');\n\t\n\t/**\n\t * Replaces every Buffer | ArrayBuffer in packet with a numbered placeholder.\n\t * Anything with blobs or files should be fed through removeBlobs before coming\n\t * here.\n\t *\n\t * @param {Object} packet - socket.io event packet\n\t * @return {Object} with deconstructed packet and list of buffers\n\t * @api public\n\t */\n\t\n\texports.deconstructPacket = function(packet) {\n\t  var buffers = [];\n\t  var packetData = packet.data;\n\t  var pack = packet;\n\t  pack.data = _deconstructPacket(packetData, buffers);\n\t  pack.attachments = buffers.length; // number of binary 'attachments'\n\t  return {packet: pack, buffers: buffers};\n\t};\n\t\n\tfunction _deconstructPacket(data, buffers) {\n\t  if (!data) return data;\n\t\n\t  if (isBuf(data)) {\n\t    var placeholder = { _placeholder: true, num: buffers.length };\n\t    buffers.push(data);\n\t    return placeholder;\n\t  } else if (isArray(data)) {\n\t    var newData = new Array(data.length);\n\t    for (var i = 0; i < data.length; i++) {\n\t      newData[i] = _deconstructPacket(data[i], buffers);\n\t    }\n\t    return newData;\n\t  } else if (typeof data === 'object' && !(data instanceof Date)) {\n\t    var newData = {};\n\t    for (var key in data) {\n\t      newData[key] = _deconstructPacket(data[key], buffers);\n\t    }\n\t    return newData;\n\t  }\n\t  return data;\n\t}\n\t\n\t/**\n\t * Reconstructs a binary packet from its placeholder packet and buffers\n\t *\n\t * @param {Object} packet - event packet with placeholders\n\t * @param {Array} buffers - binary buffers to put in placeholder positions\n\t * @return {Object} reconstructed packet\n\t * @api public\n\t */\n\t\n\texports.reconstructPacket = function(packet, buffers) {\n\t  packet.data = _reconstructPacket(packet.data, buffers);\n\t  packet.attachments = undefined; // no longer useful\n\t  return packet;\n\t};\n\t\n\tfunction _reconstructPacket(data, buffers) {\n\t  if (!data) return data;\n\t\n\t  if (data && data._placeholder) {\n\t    return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n\t  } else if (isArray(data)) {\n\t    for (var i = 0; i < data.length; i++) {\n\t      data[i] = _reconstructPacket(data[i], buffers);\n\t    }\n\t  } else if (typeof data === 'object') {\n\t    for (var key in data) {\n\t      data[key] = _reconstructPacket(data[key], buffers);\n\t    }\n\t  }\n\t\n\t  return data;\n\t}\n\t\n\t/**\n\t * Asynchronously removes Blobs or Files from data via\n\t * FileReader's readAsArrayBuffer method. Used before encoding\n\t * data as msgpack. Calls callback with the blobless data.\n\t *\n\t * @param {Object} data\n\t * @param {Function} callback\n\t * @api private\n\t */\n\t\n\texports.removeBlobs = function(data, callback) {\n\t  function _removeBlobs(obj, curKey, containingObject) {\n\t    if (!obj) return obj;\n\t\n\t    // convert any blob\n\t    if ((withNativeBlob && obj instanceof Blob) ||\n\t        (withNativeFile && obj instanceof File)) {\n\t      pendingBlobs++;\n\t\n\t      // async filereader\n\t      var fileReader = new FileReader();\n\t      fileReader.onload = function() { // this.result == arraybuffer\n\t        if (containingObject) {\n\t          containingObject[curKey] = this.result;\n\t        }\n\t        else {\n\t          bloblessData = this.result;\n\t        }\n\t\n\t        // if nothing pending its callback time\n\t        if(! --pendingBlobs) {\n\t          callback(bloblessData);\n\t        }\n\t      };\n\t\n\t      fileReader.readAsArrayBuffer(obj); // blob -> arraybuffer\n\t    } else if (isArray(obj)) { // handle array\n\t      for (var i = 0; i < obj.length; i++) {\n\t        _removeBlobs(obj[i], i, obj);\n\t      }\n\t    } else if (typeof obj === 'object' && !isBuf(obj)) { // and object\n\t      for (var key in obj) {\n\t        _removeBlobs(obj[key], key, obj);\n\t      }\n\t    }\n\t  }\n\t\n\t  var pendingBlobs = 0;\n\t  var bloblessData = data;\n\t  _removeBlobs(bloblessData);\n\t  if (!pendingBlobs) {\n\t    callback(bloblessData);\n\t  }\n\t};\n\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports) {\n\n\tvar toString = {}.toString;\n\t\n\tmodule.exports = Array.isArray || function (arr) {\n\t  return toString.call(arr) == '[object Array]';\n\t};\n\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports) {\n\n\t\n\tmodule.exports = isBuf;\n\t\n\tvar withNativeBuffer = typeof Buffer === 'function' && typeof Buffer.isBuffer === 'function';\n\tvar withNativeArrayBuffer = typeof ArrayBuffer === 'function';\n\t\n\tvar isView = function (obj) {\n\t  return typeof ArrayBuffer.isView === 'function' ? ArrayBuffer.isView(obj) : (obj.buffer instanceof ArrayBuffer);\n\t};\n\t\n\t/**\n\t * Returns true if obj is a buffer or an arraybuffer.\n\t *\n\t * @api private\n\t */\n\t\n\tfunction isBuf(obj) {\n\t  return (withNativeBuffer && Buffer.isBuffer(obj)) ||\n\t          (withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj)));\n\t}\n\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t'use strict';\n\t\n\tvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar eio = __webpack_require__(10);\n\tvar Socket = __webpack_require__(34);\n\tvar Emitter = __webpack_require__(5);\n\tvar parser = __webpack_require__(4);\n\tvar on = __webpack_require__(36);\n\tvar bind = __webpack_require__(37);\n\tvar debug = __webpack_require__(3)('socket.io-client:manager');\n\tvar indexOf = __webpack_require__(33);\n\tvar Backoff = __webpack_require__(38);\n\t\n\t/**\n\t * IE6+ hasOwnProperty\n\t */\n\t\n\tvar has = Object.prototype.hasOwnProperty;\n\t\n\t/**\n\t * Module exports\n\t */\n\t\n\tmodule.exports = Manager;\n\t\n\t/**\n\t * `Manager` constructor.\n\t *\n\t * @param {String} engine instance or engine uri/opts\n\t * @param {Object} options\n\t * @api public\n\t */\n\t\n\tfunction Manager(uri, opts) {\n\t  if (!(this instanceof Manager)) return new Manager(uri, opts);\n\t  if (uri && 'object' === (typeof uri === 'undefined' ? 'undefined' : _typeof(uri))) {\n\t    opts = uri;\n\t    uri = undefined;\n\t  }\n\t  opts = opts || {};\n\t\n\t  opts.path = opts.path || '/socket.io';\n\t  this.nsps = {};\n\t  this.subs = [];\n\t  this.opts = opts;\n\t  this.reconnection(opts.reconnection !== false);\n\t  this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n\t  this.reconnectionDelay(opts.reconnectionDelay || 1000);\n\t  this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n\t  this.randomizationFactor(opts.randomizationFactor || 0.5);\n\t  this.backoff = new Backoff({\n\t    min: this.reconnectionDelay(),\n\t    max: this.reconnectionDelayMax(),\n\t    jitter: this.randomizationFactor()\n\t  });\n\t  this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n\t  this.readyState = 'closed';\n\t  this.uri = uri;\n\t  this.connecting = [];\n\t  this.lastPing = null;\n\t  this.encoding = false;\n\t  this.packetBuffer = [];\n\t  var _parser = opts.parser || parser;\n\t  this.encoder = new _parser.Encoder();\n\t  this.decoder = new _parser.Decoder();\n\t  this.autoConnect = opts.autoConnect !== false;\n\t  if (this.autoConnect) this.open();\n\t}\n\t\n\t/**\n\t * Propagate given event to sockets and emit on `this`\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.emitAll = function () {\n\t  this.emit.apply(this, arguments);\n\t  for (var nsp in this.nsps) {\n\t    if (has.call(this.nsps, nsp)) {\n\t      this.nsps[nsp].emit.apply(this.nsps[nsp], arguments);\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * Update `socket.id` of all sockets\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.updateSocketIds = function () {\n\t  for (var nsp in this.nsps) {\n\t    if (has.call(this.nsps, nsp)) {\n\t      this.nsps[nsp].id = this.generateId(nsp);\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * generate `socket.id` for the given `nsp`\n\t *\n\t * @param {String} nsp\n\t * @return {String}\n\t * @api private\n\t */\n\t\n\tManager.prototype.generateId = function (nsp) {\n\t  return (nsp === '/' ? '' : nsp + '#') + this.engine.id;\n\t};\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Manager.prototype);\n\t\n\t/**\n\t * Sets the `reconnection` config.\n\t *\n\t * @param {Boolean} true/false if it should automatically reconnect\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.reconnection = function (v) {\n\t  if (!arguments.length) return this._reconnection;\n\t  this._reconnection = !!v;\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the reconnection attempts config.\n\t *\n\t * @param {Number} max reconnection attempts before giving up\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.reconnectionAttempts = function (v) {\n\t  if (!arguments.length) return this._reconnectionAttempts;\n\t  this._reconnectionAttempts = v;\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the delay between reconnections.\n\t *\n\t * @param {Number} delay\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.reconnectionDelay = function (v) {\n\t  if (!arguments.length) return this._reconnectionDelay;\n\t  this._reconnectionDelay = v;\n\t  this.backoff && this.backoff.setMin(v);\n\t  return this;\n\t};\n\t\n\tManager.prototype.randomizationFactor = function (v) {\n\t  if (!arguments.length) return this._randomizationFactor;\n\t  this._randomizationFactor = v;\n\t  this.backoff && this.backoff.setJitter(v);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the maximum delay between reconnections.\n\t *\n\t * @param {Number} delay\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.reconnectionDelayMax = function (v) {\n\t  if (!arguments.length) return this._reconnectionDelayMax;\n\t  this._reconnectionDelayMax = v;\n\t  this.backoff && this.backoff.setMax(v);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the connection timeout. `false` to disable\n\t *\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.timeout = function (v) {\n\t  if (!arguments.length) return this._timeout;\n\t  this._timeout = v;\n\t  return this;\n\t};\n\t\n\t/**\n\t * Starts trying to reconnect if reconnection is enabled and we have not\n\t * started reconnecting yet\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.maybeReconnectOnOpen = function () {\n\t  // Only try to reconnect if it's the first time we're connecting\n\t  if (!this.reconnecting && this._reconnection && this.backoff.attempts === 0) {\n\t    // keeps reconnection from firing twice for the same reconnection loop\n\t    this.reconnect();\n\t  }\n\t};\n\t\n\t/**\n\t * Sets the current transport `socket`.\n\t *\n\t * @param {Function} optional, callback\n\t * @return {Manager} self\n\t * @api public\n\t */\n\t\n\tManager.prototype.open = Manager.prototype.connect = function (fn, opts) {\n\t\n\t  if (~this.readyState.indexOf('open')) return this;\n\t\n\t  this.engine = eio(this.uri, this.opts);\n\t  var socket = this.engine;\n\t  var self = this;\n\t  this.readyState = 'opening';\n\t  this.skipReconnect = false;\n\t\n\t  // emit `open`\n\t  var openSub = on(socket, 'open', function () {\n\t    self.onopen();\n\t    fn && fn();\n\t  });\n\t\n\t  // emit `connect_error`\n\t  var errorSub = on(socket, 'error', function (data) {\n\t\n\t    self.cleanup();\n\t    self.readyState = 'closed';\n\t    self.emitAll('connect_error', data);\n\t    if (fn) {\n\t      var err = new Error('Connection error');\n\t      err.data = data;\n\t      fn(err);\n\t    } else {\n\t      // Only do this if there is no fn to handle the error\n\t      self.maybeReconnectOnOpen();\n\t    }\n\t  });\n\t\n\t  // emit `connect_timeout`\n\t  if (false !== this._timeout) {\n\t    var timeout = this._timeout;\n\t\n\t    if (timeout === 0) {\n\t      openSub.destroy(); // prevents a race condition with the 'open' event\n\t    }\n\t\n\t    // set timer\n\t    var timer = setTimeout(function () {\n\t\n\t      openSub.destroy();\n\t      socket.close();\n\t      socket.emit('error', 'timeout');\n\t      self.emitAll('connect_timeout', timeout);\n\t    }, timeout);\n\t\n\t    this.subs.push({\n\t      destroy: function destroy() {\n\t        clearTimeout(timer);\n\t      }\n\t    });\n\t  }\n\t\n\t  this.subs.push(openSub);\n\t  this.subs.push(errorSub);\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Called upon transport open.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onopen = function () {\n\t\n\t  // clear old subs\n\t  this.cleanup();\n\t\n\t  // mark as open\n\t  this.readyState = 'open';\n\t  this.emit('open');\n\t\n\t  // add new subs\n\t  var socket = this.engine;\n\t  this.subs.push(on(socket, 'data', bind(this, 'ondata')));\n\t  this.subs.push(on(socket, 'ping', bind(this, 'onping')));\n\t  this.subs.push(on(socket, 'pong', bind(this, 'onpong')));\n\t  this.subs.push(on(socket, 'error', bind(this, 'onerror')));\n\t  this.subs.push(on(socket, 'close', bind(this, 'onclose')));\n\t  this.subs.push(on(this.decoder, 'decoded', bind(this, 'ondecoded')));\n\t};\n\t\n\t/**\n\t * Called upon a ping.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onping = function () {\n\t  this.lastPing = new Date();\n\t  this.emitAll('ping');\n\t};\n\t\n\t/**\n\t * Called upon a packet.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onpong = function () {\n\t  this.emitAll('pong', new Date() - this.lastPing);\n\t};\n\t\n\t/**\n\t * Called with data.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.ondata = function (data) {\n\t  this.decoder.add(data);\n\t};\n\t\n\t/**\n\t * Called when parser fully decodes a packet.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.ondecoded = function (packet) {\n\t  this.emit('packet', packet);\n\t};\n\t\n\t/**\n\t * Called upon socket error.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onerror = function (err) {\n\t\n\t  this.emitAll('error', err);\n\t};\n\t\n\t/**\n\t * Creates a new socket for the given `nsp`.\n\t *\n\t * @return {Socket}\n\t * @api public\n\t */\n\t\n\tManager.prototype.socket = function (nsp, opts) {\n\t  var socket = this.nsps[nsp];\n\t  if (!socket) {\n\t    socket = new Socket(this, nsp, opts);\n\t    this.nsps[nsp] = socket;\n\t    var self = this;\n\t    socket.on('connecting', onConnecting);\n\t    socket.on('connect', function () {\n\t      socket.id = self.generateId(nsp);\n\t    });\n\t\n\t    if (this.autoConnect) {\n\t      // manually call here since connecting event is fired before listening\n\t      onConnecting();\n\t    }\n\t  }\n\t\n\t  function onConnecting() {\n\t    if (!~indexOf(self.connecting, socket)) {\n\t      self.connecting.push(socket);\n\t    }\n\t  }\n\t\n\t  return socket;\n\t};\n\t\n\t/**\n\t * Called upon a socket close.\n\t *\n\t * @param {Socket} socket\n\t */\n\t\n\tManager.prototype.destroy = function (socket) {\n\t  var index = indexOf(this.connecting, socket);\n\t  if (~index) this.connecting.splice(index, 1);\n\t  if (this.connecting.length) return;\n\t\n\t  this.close();\n\t};\n\t\n\t/**\n\t * Writes a packet.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tManager.prototype.packet = function (packet) {\n\t\n\t  var self = this;\n\t  if (packet.query && packet.type === 0) packet.nsp += '?' + packet.query;\n\t\n\t  if (!self.encoding) {\n\t    // encode, then write to engine with result\n\t    self.encoding = true;\n\t    this.encoder.encode(packet, function (encodedPackets) {\n\t      for (var i = 0; i < encodedPackets.length; i++) {\n\t        self.engine.write(encodedPackets[i], packet.options);\n\t      }\n\t      self.encoding = false;\n\t      self.processPacketQueue();\n\t    });\n\t  } else {\n\t    // add packet to the queue\n\t    self.packetBuffer.push(packet);\n\t  }\n\t};\n\t\n\t/**\n\t * If packet buffer is non-empty, begins encoding the\n\t * next packet in line.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.processPacketQueue = function () {\n\t  if (this.packetBuffer.length > 0 && !this.encoding) {\n\t    var pack = this.packetBuffer.shift();\n\t    this.packet(pack);\n\t  }\n\t};\n\t\n\t/**\n\t * Clean up transport subscriptions and packet buffer.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.cleanup = function () {\n\t\n\t  var subsLength = this.subs.length;\n\t  for (var i = 0; i < subsLength; i++) {\n\t    var sub = this.subs.shift();\n\t    sub.destroy();\n\t  }\n\t\n\t  this.packetBuffer = [];\n\t  this.encoding = false;\n\t  this.lastPing = null;\n\t\n\t  this.decoder.destroy();\n\t};\n\t\n\t/**\n\t * Close the current socket.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.close = Manager.prototype.disconnect = function () {\n\t\n\t  this.skipReconnect = true;\n\t  this.reconnecting = false;\n\t  if ('opening' === this.readyState) {\n\t    // `onclose` will not fire because\n\t    // an open event never happened\n\t    this.cleanup();\n\t  }\n\t  this.backoff.reset();\n\t  this.readyState = 'closed';\n\t  if (this.engine) this.engine.close();\n\t};\n\t\n\t/**\n\t * Called upon engine close.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onclose = function (reason) {\n\t\n\t  this.cleanup();\n\t  this.backoff.reset();\n\t  this.readyState = 'closed';\n\t  this.emit('close', reason);\n\t\n\t  if (this._reconnection && !this.skipReconnect) {\n\t    this.reconnect();\n\t  }\n\t};\n\t\n\t/**\n\t * Attempt a reconnection.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.reconnect = function () {\n\t  if (this.reconnecting || this.skipReconnect) return this;\n\t\n\t  var self = this;\n\t\n\t  if (this.backoff.attempts >= this._reconnectionAttempts) {\n\t\n\t    this.backoff.reset();\n\t    this.emitAll('reconnect_failed');\n\t    this.reconnecting = false;\n\t  } else {\n\t    var delay = this.backoff.duration();\n\t\n\t    this.reconnecting = true;\n\t    var timer = setTimeout(function () {\n\t      if (self.skipReconnect) return;\n\t\n\t      self.emitAll('reconnect_attempt', self.backoff.attempts);\n\t      self.emitAll('reconnecting', self.backoff.attempts);\n\t\n\t      // check again for the case socket closed in above events\n\t      if (self.skipReconnect) return;\n\t\n\t      self.open(function (err) {\n\t        if (err) {\n\t\n\t          self.reconnecting = false;\n\t          self.reconnect();\n\t          self.emitAll('reconnect_error', err.data);\n\t        } else {\n\t\n\t          self.onreconnect();\n\t        }\n\t      });\n\t    }, delay);\n\t\n\t    this.subs.push({\n\t      destroy: function destroy() {\n\t        clearTimeout(timer);\n\t      }\n\t    });\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon successful reconnect.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onreconnect = function () {\n\t  var attempt = this.backoff.attempts;\n\t  this.reconnecting = false;\n\t  this.backoff.reset();\n\t  this.updateSocketIds();\n\t  this.emitAll('reconnect', attempt);\n\t};\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\n\tmodule.exports = __webpack_require__(11);\n\t\n\t/**\n\t * Exports parser\n\t *\n\t * @api public\n\t *\n\t */\n\tmodule.exports.parser = __webpack_require__(19);\n\n\n/***/ }),\n/* 11 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar transports = __webpack_require__(12);\n\tvar Emitter = __webpack_require__(5);\n\tvar debug = __webpack_require__(3)('engine.io-client:socket');\n\tvar index = __webpack_require__(33);\n\tvar parser = __webpack_require__(19);\n\tvar parseuri = __webpack_require__(2);\n\tvar parseqs = __webpack_require__(27);\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = Socket;\n\t\n\t/**\n\t * Socket constructor.\n\t *\n\t * @param {String|Object} uri or options\n\t * @param {Object} options\n\t * @api public\n\t */\n\t\n\tfunction Socket (uri, opts) {\n\t  if (!(this instanceof Socket)) return new Socket(uri, opts);\n\t\n\t  opts = opts || {};\n\t\n\t  if (uri && 'object' === typeof uri) {\n\t    opts = uri;\n\t    uri = null;\n\t  }\n\t\n\t  if (uri) {\n\t    uri = parseuri(uri);\n\t    opts.hostname = uri.host;\n\t    opts.secure = uri.protocol === 'https' || uri.protocol === 'wss';\n\t    opts.port = uri.port;\n\t    if (uri.query) opts.query = uri.query;\n\t  } else if (opts.host) {\n\t    opts.hostname = parseuri(opts.host).host;\n\t  }\n\t\n\t  this.secure = null != opts.secure ? opts.secure\n\t    : (typeof location !== 'undefined' && 'https:' === location.protocol);\n\t\n\t  if (opts.hostname && !opts.port) {\n\t    // if no port is specified manually, use the protocol default\n\t    opts.port = this.secure ? '443' : '80';\n\t  }\n\t\n\t  this.agent = opts.agent || false;\n\t  this.hostname = opts.hostname ||\n\t    (typeof location !== 'undefined' ? location.hostname : 'localhost');\n\t  this.port = opts.port || (typeof location !== 'undefined' && location.port\n\t      ? location.port\n\t      : (this.secure ? 443 : 80));\n\t  this.query = opts.query || {};\n\t  if ('string' === typeof this.query) this.query = parseqs.decode(this.query);\n\t  this.upgrade = false !== opts.upgrade;\n\t  this.path = (opts.path || '/engine.io').replace(/\\/$/, '') + '/';\n\t  this.forceJSONP = !!opts.forceJSONP;\n\t  this.jsonp = false !== opts.jsonp;\n\t  this.forceBase64 = !!opts.forceBase64;\n\t  this.enablesXDR = !!opts.enablesXDR;\n\t  this.withCredentials = false !== opts.withCredentials;\n\t  this.timestampParam = opts.timestampParam || 't';\n\t  this.timestampRequests = opts.timestampRequests;\n\t  this.transports = opts.transports || ['polling', 'websocket'];\n\t  this.transportOptions = opts.transportOptions || {};\n\t  this.readyState = '';\n\t  this.writeBuffer = [];\n\t  this.prevBufferLen = 0;\n\t  this.policyPort = opts.policyPort || 843;\n\t  this.rememberUpgrade = opts.rememberUpgrade || false;\n\t  this.binaryType = null;\n\t  this.onlyBinaryUpgrades = opts.onlyBinaryUpgrades;\n\t  this.perMessageDeflate = false !== opts.perMessageDeflate ? (opts.perMessageDeflate || {}) : false;\n\t\n\t  if (true === this.perMessageDeflate) this.perMessageDeflate = {};\n\t  if (this.perMessageDeflate && null == this.perMessageDeflate.threshold) {\n\t    this.perMessageDeflate.threshold = 1024;\n\t  }\n\t\n\t  // SSL options for Node.js client\n\t  this.pfx = opts.pfx || null;\n\t  this.key = opts.key || null;\n\t  this.passphrase = opts.passphrase || null;\n\t  this.cert = opts.cert || null;\n\t  this.ca = opts.ca || null;\n\t  this.ciphers = opts.ciphers || null;\n\t  this.rejectUnauthorized = opts.rejectUnauthorized === undefined ? true : opts.rejectUnauthorized;\n\t  this.forceNode = !!opts.forceNode;\n\t\n\t  // detect ReactNative environment\n\t  this.isReactNative = (typeof navigator !== 'undefined' && typeof navigator.product === 'string' && navigator.product.toLowerCase() === 'reactnative');\n\t\n\t  // other options for Node.js or ReactNative client\n\t  if (typeof self === 'undefined' || this.isReactNative) {\n\t    if (opts.extraHeaders && Object.keys(opts.extraHeaders).length > 0) {\n\t      this.extraHeaders = opts.extraHeaders;\n\t    }\n\t\n\t    if (opts.localAddress) {\n\t      this.localAddress = opts.localAddress;\n\t    }\n\t  }\n\t\n\t  // set on handshake\n\t  this.id = null;\n\t  this.upgrades = null;\n\t  this.pingInterval = null;\n\t  this.pingTimeout = null;\n\t\n\t  // set on heartbeat\n\t  this.pingIntervalTimer = null;\n\t  this.pingTimeoutTimer = null;\n\t\n\t  this.open();\n\t}\n\t\n\tSocket.priorWebsocketSuccess = false;\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Socket.prototype);\n\t\n\t/**\n\t * Protocol version.\n\t *\n\t * @api public\n\t */\n\t\n\tSocket.protocol = parser.protocol; // this is an int\n\t\n\t/**\n\t * Expose deps for legacy compatibility\n\t * and standalone browser access.\n\t */\n\t\n\tSocket.Socket = Socket;\n\tSocket.Transport = __webpack_require__(18);\n\tSocket.transports = __webpack_require__(12);\n\tSocket.parser = __webpack_require__(19);\n\t\n\t/**\n\t * Creates transport of the given type.\n\t *\n\t * @param {String} transport name\n\t * @return {Transport}\n\t * @api private\n\t */\n\t\n\tSocket.prototype.createTransport = function (name) {\n\t\n\t  var query = clone(this.query);\n\t\n\t  // append engine.io protocol identifier\n\t  query.EIO = parser.protocol;\n\t\n\t  // transport name\n\t  query.transport = name;\n\t\n\t  // per-transport options\n\t  var options = this.transportOptions[name] || {};\n\t\n\t  // session id if we already have one\n\t  if (this.id) query.sid = this.id;\n\t\n\t  var transport = new transports[name]({\n\t    query: query,\n\t    socket: this,\n\t    agent: options.agent || this.agent,\n\t    hostname: options.hostname || this.hostname,\n\t    port: options.port || this.port,\n\t    secure: options.secure || this.secure,\n\t    path: options.path || this.path,\n\t    forceJSONP: options.forceJSONP || this.forceJSONP,\n\t    jsonp: options.jsonp || this.jsonp,\n\t    forceBase64: options.forceBase64 || this.forceBase64,\n\t    enablesXDR: options.enablesXDR || this.enablesXDR,\n\t    withCredentials: options.withCredentials || this.withCredentials,\n\t    timestampRequests: options.timestampRequests || this.timestampRequests,\n\t    timestampParam: options.timestampParam || this.timestampParam,\n\t    policyPort: options.policyPort || this.policyPort,\n\t    pfx: options.pfx || this.pfx,\n\t    key: options.key || this.key,\n\t    passphrase: options.passphrase || this.passphrase,\n\t    cert: options.cert || this.cert,\n\t    ca: options.ca || this.ca,\n\t    ciphers: options.ciphers || this.ciphers,\n\t    rejectUnauthorized: options.rejectUnauthorized || this.rejectUnauthorized,\n\t    perMessageDeflate: options.perMessageDeflate || this.perMessageDeflate,\n\t    extraHeaders: options.extraHeaders || this.extraHeaders,\n\t    forceNode: options.forceNode || this.forceNode,\n\t    localAddress: options.localAddress || this.localAddress,\n\t    requestTimeout: options.requestTimeout || this.requestTimeout,\n\t    protocols: options.protocols || void (0),\n\t    isReactNative: this.isReactNative\n\t  });\n\t\n\t  return transport;\n\t};\n\t\n\tfunction clone (obj) {\n\t  var o = {};\n\t  for (var i in obj) {\n\t    if (obj.hasOwnProperty(i)) {\n\t      o[i] = obj[i];\n\t    }\n\t  }\n\t  return o;\n\t}\n\t\n\t/**\n\t * Initializes transport to use and starts probe.\n\t *\n\t * @api private\n\t */\n\tSocket.prototype.open = function () {\n\t  var transport;\n\t  if (this.rememberUpgrade && Socket.priorWebsocketSuccess && this.transports.indexOf('websocket') !== -1) {\n\t    transport = 'websocket';\n\t  } else if (0 === this.transports.length) {\n\t    // Emit error on next tick so it can be listened to\n\t    var self = this;\n\t    setTimeout(function () {\n\t      self.emit('error', 'No transports available');\n\t    }, 0);\n\t    return;\n\t  } else {\n\t    transport = this.transports[0];\n\t  }\n\t  this.readyState = 'opening';\n\t\n\t  // Retry with the next transport if the transport is disabled (jsonp: false)\n\t  try {\n\t    transport = this.createTransport(transport);\n\t  } catch (e) {\n\t    this.transports.shift();\n\t    this.open();\n\t    return;\n\t  }\n\t\n\t  transport.open();\n\t  this.setTransport(transport);\n\t};\n\t\n\t/**\n\t * Sets the current transport. Disables the existing one (if any).\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.setTransport = function (transport) {\n\t\n\t  var self = this;\n\t\n\t  if (this.transport) {\n\t\n\t    this.transport.removeAllListeners();\n\t  }\n\t\n\t  // set up transport\n\t  this.transport = transport;\n\t\n\t  // set up transport listeners\n\t  transport\n\t  .on('drain', function () {\n\t    self.onDrain();\n\t  })\n\t  .on('packet', function (packet) {\n\t    self.onPacket(packet);\n\t  })\n\t  .on('error', function (e) {\n\t    self.onError(e);\n\t  })\n\t  .on('close', function () {\n\t    self.onClose('transport close');\n\t  });\n\t};\n\t\n\t/**\n\t * Probes a transport.\n\t *\n\t * @param {String} transport name\n\t * @api private\n\t */\n\t\n\tSocket.prototype.probe = function (name) {\n\t\n\t  var transport = this.createTransport(name, { probe: 1 });\n\t  var failed = false;\n\t  var self = this;\n\t\n\t  Socket.priorWebsocketSuccess = false;\n\t\n\t  function onTransportOpen () {\n\t    if (self.onlyBinaryUpgrades) {\n\t      var upgradeLosesBinary = !this.supportsBinary && self.transport.supportsBinary;\n\t      failed = failed || upgradeLosesBinary;\n\t    }\n\t    if (failed) return;\n\t\n\t\n\t    transport.send([{ type: 'ping', data: 'probe' }]);\n\t    transport.once('packet', function (msg) {\n\t      if (failed) return;\n\t      if ('pong' === msg.type && 'probe' === msg.data) {\n\t\n\t        self.upgrading = true;\n\t        self.emit('upgrading', transport);\n\t        if (!transport) return;\n\t        Socket.priorWebsocketSuccess = 'websocket' === transport.name;\n\t\n\t\n\t        self.transport.pause(function () {\n\t          if (failed) return;\n\t          if ('closed' === self.readyState) return;\n\t\n\t\n\t          cleanup();\n\t\n\t          self.setTransport(transport);\n\t          transport.send([{ type: 'upgrade' }]);\n\t          self.emit('upgrade', transport);\n\t          transport = null;\n\t          self.upgrading = false;\n\t          self.flush();\n\t        });\n\t      } else {\n\t\n\t        var err = new Error('probe error');\n\t        err.transport = transport.name;\n\t        self.emit('upgradeError', err);\n\t      }\n\t    });\n\t  }\n\t\n\t  function freezeTransport () {\n\t    if (failed) return;\n\t\n\t    // Any callback called by transport should be ignored since now\n\t    failed = true;\n\t\n\t    cleanup();\n\t\n\t    transport.close();\n\t    transport = null;\n\t  }\n\t\n\t  // Handle any error that happens while probing\n\t  function onerror (err) {\n\t    var error = new Error('probe error: ' + err);\n\t    error.transport = transport.name;\n\t\n\t    freezeTransport();\n\t\n\t\n\t\n\t    self.emit('upgradeError', error);\n\t  }\n\t\n\t  function onTransportClose () {\n\t    onerror('transport closed');\n\t  }\n\t\n\t  // When the socket is closed while we're probing\n\t  function onclose () {\n\t    onerror('socket closed');\n\t  }\n\t\n\t  // When the socket is upgraded while we're probing\n\t  function onupgrade (to) {\n\t    if (transport && to.name !== transport.name) {\n\t\n\t      freezeTransport();\n\t    }\n\t  }\n\t\n\t  // Remove all listeners on the transport and on self\n\t  function cleanup () {\n\t    transport.removeListener('open', onTransportOpen);\n\t    transport.removeListener('error', onerror);\n\t    transport.removeListener('close', onTransportClose);\n\t    self.removeListener('close', onclose);\n\t    self.removeListener('upgrading', onupgrade);\n\t  }\n\t\n\t  transport.once('open', onTransportOpen);\n\t  transport.once('error', onerror);\n\t  transport.once('close', onTransportClose);\n\t\n\t  this.once('close', onclose);\n\t  this.once('upgrading', onupgrade);\n\t\n\t  transport.open();\n\t};\n\t\n\t/**\n\t * Called when connection is deemed open.\n\t *\n\t * @api public\n\t */\n\t\n\tSocket.prototype.onOpen = function () {\n\t\n\t  this.readyState = 'open';\n\t  Socket.priorWebsocketSuccess = 'websocket' === this.transport.name;\n\t  this.emit('open');\n\t  this.flush();\n\t\n\t  // we check for `readyState` in case an `open`\n\t  // listener already closed the socket\n\t  if ('open' === this.readyState && this.upgrade && this.transport.pause) {\n\t\n\t    for (var i = 0, l = this.upgrades.length; i < l; i++) {\n\t      this.probe(this.upgrades[i]);\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * Handles a packet.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onPacket = function (packet) {\n\t  if ('opening' === this.readyState || 'open' === this.readyState ||\n\t      'closing' === this.readyState) {\n\t\n\t\n\t    this.emit('packet', packet);\n\t\n\t    // Socket is live - any packet counts\n\t    this.emit('heartbeat');\n\t\n\t    switch (packet.type) {\n\t      case 'open':\n\t        this.onHandshake(JSON.parse(packet.data));\n\t        break;\n\t\n\t      case 'pong':\n\t        this.setPing();\n\t        this.emit('pong');\n\t        break;\n\t\n\t      case 'error':\n\t        var err = new Error('server error');\n\t        err.code = packet.data;\n\t        this.onError(err);\n\t        break;\n\t\n\t      case 'message':\n\t        this.emit('data', packet.data);\n\t        this.emit('message', packet.data);\n\t        break;\n\t    }\n\t  } else {\n\t\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon handshake completion.\n\t *\n\t * @param {Object} handshake obj\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onHandshake = function (data) {\n\t  this.emit('handshake', data);\n\t  this.id = data.sid;\n\t  this.transport.query.sid = data.sid;\n\t  this.upgrades = this.filterUpgrades(data.upgrades);\n\t  this.pingInterval = data.pingInterval;\n\t  this.pingTimeout = data.pingTimeout;\n\t  this.onOpen();\n\t  // In case open handler closes socket\n\t  if ('closed' === this.readyState) return;\n\t  this.setPing();\n\t\n\t  // Prolong liveness of socket on heartbeat\n\t  this.removeListener('heartbeat', this.onHeartbeat);\n\t  this.on('heartbeat', this.onHeartbeat);\n\t};\n\t\n\t/**\n\t * Resets ping timeout.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onHeartbeat = function (timeout) {\n\t  clearTimeout(this.pingTimeoutTimer);\n\t  var self = this;\n\t  self.pingTimeoutTimer = setTimeout(function () {\n\t    if ('closed' === self.readyState) return;\n\t    self.onClose('ping timeout');\n\t  }, timeout || (self.pingInterval + self.pingTimeout));\n\t};\n\t\n\t/**\n\t * Pings server every `this.pingInterval` and expects response\n\t * within `this.pingTimeout` or closes connection.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.setPing = function () {\n\t  var self = this;\n\t  clearTimeout(self.pingIntervalTimer);\n\t  self.pingIntervalTimer = setTimeout(function () {\n\t\n\t    self.ping();\n\t    self.onHeartbeat(self.pingTimeout);\n\t  }, self.pingInterval);\n\t};\n\t\n\t/**\n\t* Sends a ping packet.\n\t*\n\t* @api private\n\t*/\n\t\n\tSocket.prototype.ping = function () {\n\t  var self = this;\n\t  this.sendPacket('ping', function () {\n\t    self.emit('ping');\n\t  });\n\t};\n\t\n\t/**\n\t * Called on `drain` event\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onDrain = function () {\n\t  this.writeBuffer.splice(0, this.prevBufferLen);\n\t\n\t  // setting prevBufferLen = 0 is very important\n\t  // for example, when upgrading, upgrade packet is sent over,\n\t  // and a nonzero prevBufferLen could cause problems on `drain`\n\t  this.prevBufferLen = 0;\n\t\n\t  if (0 === this.writeBuffer.length) {\n\t    this.emit('drain');\n\t  } else {\n\t    this.flush();\n\t  }\n\t};\n\t\n\t/**\n\t * Flush write buffers.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.flush = function () {\n\t  if ('closed' !== this.readyState && this.transport.writable &&\n\t    !this.upgrading && this.writeBuffer.length) {\n\t\n\t    this.transport.send(this.writeBuffer);\n\t    // keep track of current length of writeBuffer\n\t    // splice writeBuffer and callbackBuffer on `drain`\n\t    this.prevBufferLen = this.writeBuffer.length;\n\t    this.emit('flush');\n\t  }\n\t};\n\t\n\t/**\n\t * Sends a message.\n\t *\n\t * @param {String} message.\n\t * @param {Function} callback function.\n\t * @param {Object} options.\n\t * @return {Socket} for chaining.\n\t * @api public\n\t */\n\t\n\tSocket.prototype.write =\n\tSocket.prototype.send = function (msg, options, fn) {\n\t  this.sendPacket('message', msg, options, fn);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sends a packet.\n\t *\n\t * @param {String} packet type.\n\t * @param {String} data.\n\t * @param {Object} options.\n\t * @param {Function} callback function.\n\t * @api private\n\t */\n\t\n\tSocket.prototype.sendPacket = function (type, data, options, fn) {\n\t  if ('function' === typeof data) {\n\t    fn = data;\n\t    data = undefined;\n\t  }\n\t\n\t  if ('function' === typeof options) {\n\t    fn = options;\n\t    options = null;\n\t  }\n\t\n\t  if ('closing' === this.readyState || 'closed' === this.readyState) {\n\t    return;\n\t  }\n\t\n\t  options = options || {};\n\t  options.compress = false !== options.compress;\n\t\n\t  var packet = {\n\t    type: type,\n\t    data: data,\n\t    options: options\n\t  };\n\t  this.emit('packetCreate', packet);\n\t  this.writeBuffer.push(packet);\n\t  if (fn) this.once('flush', fn);\n\t  this.flush();\n\t};\n\t\n\t/**\n\t * Closes the connection.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.close = function () {\n\t  if ('opening' === this.readyState || 'open' === this.readyState) {\n\t    this.readyState = 'closing';\n\t\n\t    var self = this;\n\t\n\t    if (this.writeBuffer.length) {\n\t      this.once('drain', function () {\n\t        if (this.upgrading) {\n\t          waitForUpgrade();\n\t        } else {\n\t          close();\n\t        }\n\t      });\n\t    } else if (this.upgrading) {\n\t      waitForUpgrade();\n\t    } else {\n\t      close();\n\t    }\n\t  }\n\t\n\t  function close () {\n\t    self.onClose('forced close');\n\t\n\t    self.transport.close();\n\t  }\n\t\n\t  function cleanupAndClose () {\n\t    self.removeListener('upgrade', cleanupAndClose);\n\t    self.removeListener('upgradeError', cleanupAndClose);\n\t    close();\n\t  }\n\t\n\t  function waitForUpgrade () {\n\t    // wait for upgrade to finish since we can't send packets while pausing a transport\n\t    self.once('upgrade', cleanupAndClose);\n\t    self.once('upgradeError', cleanupAndClose);\n\t  }\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Called upon transport error\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onError = function (err) {\n\t\n\t  Socket.priorWebsocketSuccess = false;\n\t  this.emit('error', err);\n\t  this.onClose('transport error', err);\n\t};\n\t\n\t/**\n\t * Called upon transport close.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onClose = function (reason, desc) {\n\t  if ('opening' === this.readyState || 'open' === this.readyState || 'closing' === this.readyState) {\n\t\n\t    var self = this;\n\t\n\t    // clear timers\n\t    clearTimeout(this.pingIntervalTimer);\n\t    clearTimeout(this.pingTimeoutTimer);\n\t\n\t    // stop event from firing again for transport\n\t    this.transport.removeAllListeners('close');\n\t\n\t    // ensure transport won't stay open\n\t    this.transport.close();\n\t\n\t    // ignore further transport communication\n\t    this.transport.removeAllListeners();\n\t\n\t    // set ready state\n\t    this.readyState = 'closed';\n\t\n\t    // clear session id\n\t    this.id = null;\n\t\n\t    // emit close event\n\t    this.emit('close', reason, desc);\n\t\n\t    // clean buffers after, so users can still\n\t    // grab the buffers on `close` event\n\t    self.writeBuffer = [];\n\t    self.prevBufferLen = 0;\n\t  }\n\t};\n\t\n\t/**\n\t * Filters upgrades, returning only those matching client transports.\n\t *\n\t * @param {Array} server upgrades\n\t * @api private\n\t *\n\t */\n\t\n\tSocket.prototype.filterUpgrades = function (upgrades) {\n\t  var filteredUpgrades = [];\n\t  for (var i = 0, j = upgrades.length; i < j; i++) {\n\t    if (~index(this.transports, upgrades[i])) filteredUpgrades.push(upgrades[i]);\n\t  }\n\t  return filteredUpgrades;\n\t};\n\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies\n\t */\n\t\n\tvar XMLHttpRequest = __webpack_require__(13);\n\tvar XHR = __webpack_require__(16);\n\tvar JSONP = __webpack_require__(30);\n\tvar websocket = __webpack_require__(31);\n\t\n\t/**\n\t * Export transports.\n\t */\n\t\n\texports.polling = polling;\n\texports.websocket = websocket;\n\t\n\t/**\n\t * Polling transport polymorphic constructor.\n\t * Decides on xhr vs jsonp based on feature detection.\n\t *\n\t * @api private\n\t */\n\t\n\tfunction polling (opts) {\n\t  var xhr;\n\t  var xd = false;\n\t  var xs = false;\n\t  var jsonp = false !== opts.jsonp;\n\t\n\t  if (typeof location !== 'undefined') {\n\t    var isSSL = 'https:' === location.protocol;\n\t    var port = location.port;\n\t\n\t    // some user agents have empty `location.port`\n\t    if (!port) {\n\t      port = isSSL ? 443 : 80;\n\t    }\n\t\n\t    xd = opts.hostname !== location.hostname || port !== opts.port;\n\t    xs = opts.secure !== isSSL;\n\t  }\n\t\n\t  opts.xdomain = xd;\n\t  opts.xscheme = xs;\n\t  xhr = new XMLHttpRequest(opts);\n\t\n\t  if ('open' in xhr && !opts.forceJSONP) {\n\t    return new XHR(opts);\n\t  } else {\n\t    if (!jsonp) throw new Error('JSONP disabled');\n\t    return new JSONP(opts);\n\t  }\n\t}\n\n\n/***/ }),\n/* 13 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t// browser shim for xmlhttprequest module\n\t\n\tvar hasCORS = __webpack_require__(14);\n\tvar globalThis = __webpack_require__(15);\n\t\n\tmodule.exports = function (opts) {\n\t  var xdomain = opts.xdomain;\n\t\n\t  // scheme must be same when usign XDomainRequest\n\t  // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n\t  var xscheme = opts.xscheme;\n\t\n\t  // XDomainRequest has a flow of not sending cookie, therefore it should be disabled as a default.\n\t  // https://github.com/Automattic/engine.io-client/pull/217\n\t  var enablesXDR = opts.enablesXDR;\n\t\n\t  // XMLHttpRequest can be disabled on IE\n\t  try {\n\t    if ('undefined' !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n\t      return new XMLHttpRequest();\n\t    }\n\t  } catch (e) { }\n\t\n\t  // Use XDomainRequest for IE8 if enablesXDR is true\n\t  // because loading bar keeps flashing when using jsonp-polling\n\t  // https://github.com/yujiosaka/socke.io-ie8-loading-example\n\t  try {\n\t    if ('undefined' !== typeof XDomainRequest && !xscheme && enablesXDR) {\n\t      return new XDomainRequest();\n\t    }\n\t  } catch (e) { }\n\t\n\t  if (!xdomain) {\n\t    try {\n\t      return new globalThis[['Active'].concat('Object').join('X')]('Microsoft.XMLHTTP');\n\t    } catch (e) { }\n\t  }\n\t};\n\n\n/***/ }),\n/* 14 */\n/***/ (function(module, exports) {\n\n\t\n\t/**\n\t * Module exports.\n\t *\n\t * Logic borrowed from Modernizr:\n\t *\n\t *   - https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cors.js\n\t */\n\t\n\ttry {\n\t  module.exports = typeof XMLHttpRequest !== 'undefined' &&\n\t    'withCredentials' in new XMLHttpRequest();\n\t} catch (err) {\n\t  // if XMLHttp support is disabled in IE then it will throw\n\t  // when trying to create\n\t  module.exports = false;\n\t}\n\n\n/***/ }),\n/* 15 */\n/***/ (function(module, exports) {\n\n\tmodule.exports = (function () {\n\t  if (typeof self !== 'undefined') {\n\t    return self;\n\t  } else if (typeof window !== 'undefined') {\n\t    return window;\n\t  } else {\n\t    return Function('return this')(); // eslint-disable-line no-new-func\n\t  }\n\t})();\n\n\n/***/ }),\n/* 16 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* global attachEvent */\n\t\n\t/**\n\t * Module requirements.\n\t */\n\t\n\tvar XMLHttpRequest = __webpack_require__(13);\n\tvar Polling = __webpack_require__(17);\n\tvar Emitter = __webpack_require__(5);\n\tvar inherit = __webpack_require__(28);\n\tvar debug = __webpack_require__(3)('engine.io-client:polling-xhr');\n\tvar globalThis = __webpack_require__(15);\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = XHR;\n\tmodule.exports.Request = Request;\n\t\n\t/**\n\t * Empty function\n\t */\n\t\n\tfunction empty () {}\n\t\n\t/**\n\t * XHR Polling constructor.\n\t *\n\t * @param {Object} opts\n\t * @api public\n\t */\n\t\n\tfunction XHR (opts) {\n\t  Polling.call(this, opts);\n\t  this.requestTimeout = opts.requestTimeout;\n\t  this.extraHeaders = opts.extraHeaders;\n\t\n\t  if (typeof location !== 'undefined') {\n\t    var isSSL = 'https:' === location.protocol;\n\t    var port = location.port;\n\t\n\t    // some user agents have empty `location.port`\n\t    if (!port) {\n\t      port = isSSL ? 443 : 80;\n\t    }\n\t\n\t    this.xd = (typeof location !== 'undefined' && opts.hostname !== location.hostname) ||\n\t      port !== opts.port;\n\t    this.xs = opts.secure !== isSSL;\n\t  }\n\t}\n\t\n\t/**\n\t * Inherits from Polling.\n\t */\n\t\n\tinherit(XHR, Polling);\n\t\n\t/**\n\t * XHR supports binary\n\t */\n\t\n\tXHR.prototype.supportsBinary = true;\n\t\n\t/**\n\t * Creates a request.\n\t *\n\t * @param {String} method\n\t * @api private\n\t */\n\t\n\tXHR.prototype.request = function (opts) {\n\t  opts = opts || {};\n\t  opts.uri = this.uri();\n\t  opts.xd = this.xd;\n\t  opts.xs = this.xs;\n\t  opts.agent = this.agent || false;\n\t  opts.supportsBinary = this.supportsBinary;\n\t  opts.enablesXDR = this.enablesXDR;\n\t  opts.withCredentials = this.withCredentials;\n\t\n\t  // SSL options for Node.js client\n\t  opts.pfx = this.pfx;\n\t  opts.key = this.key;\n\t  opts.passphrase = this.passphrase;\n\t  opts.cert = this.cert;\n\t  opts.ca = this.ca;\n\t  opts.ciphers = this.ciphers;\n\t  opts.rejectUnauthorized = this.rejectUnauthorized;\n\t  opts.requestTimeout = this.requestTimeout;\n\t\n\t  // other options for Node.js client\n\t  opts.extraHeaders = this.extraHeaders;\n\t\n\t  return new Request(opts);\n\t};\n\t\n\t/**\n\t * Sends data.\n\t *\n\t * @param {String} data to send.\n\t * @param {Function} called upon flush.\n\t * @api private\n\t */\n\t\n\tXHR.prototype.doWrite = function (data, fn) {\n\t  var isBinary = typeof data !== 'string' && data !== undefined;\n\t  var req = this.request({ method: 'POST', data: data, isBinary: isBinary });\n\t  var self = this;\n\t  req.on('success', fn);\n\t  req.on('error', function (err) {\n\t    self.onError('xhr post error', err);\n\t  });\n\t  this.sendXhr = req;\n\t};\n\t\n\t/**\n\t * Starts a poll cycle.\n\t *\n\t * @api private\n\t */\n\t\n\tXHR.prototype.doPoll = function () {\n\t\n\t  var req = this.request();\n\t  var self = this;\n\t  req.on('data', function (data) {\n\t    self.onData(data);\n\t  });\n\t  req.on('error', function (err) {\n\t    self.onError('xhr poll error', err);\n\t  });\n\t  this.pollXhr = req;\n\t};\n\t\n\t/**\n\t * Request constructor\n\t *\n\t * @param {Object} options\n\t * @api public\n\t */\n\t\n\tfunction Request (opts) {\n\t  this.method = opts.method || 'GET';\n\t  this.uri = opts.uri;\n\t  this.xd = !!opts.xd;\n\t  this.xs = !!opts.xs;\n\t  this.async = false !== opts.async;\n\t  this.data = undefined !== opts.data ? opts.data : null;\n\t  this.agent = opts.agent;\n\t  this.isBinary = opts.isBinary;\n\t  this.supportsBinary = opts.supportsBinary;\n\t  this.enablesXDR = opts.enablesXDR;\n\t  this.withCredentials = opts.withCredentials;\n\t  this.requestTimeout = opts.requestTimeout;\n\t\n\t  // SSL options for Node.js client\n\t  this.pfx = opts.pfx;\n\t  this.key = opts.key;\n\t  this.passphrase = opts.passphrase;\n\t  this.cert = opts.cert;\n\t  this.ca = opts.ca;\n\t  this.ciphers = opts.ciphers;\n\t  this.rejectUnauthorized = opts.rejectUnauthorized;\n\t\n\t  // other options for Node.js client\n\t  this.extraHeaders = opts.extraHeaders;\n\t\n\t  this.create();\n\t}\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Request.prototype);\n\t\n\t/**\n\t * Creates the XHR object and sends the request.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.create = function () {\n\t  var opts = { agent: this.agent, xdomain: this.xd, xscheme: this.xs, enablesXDR: this.enablesXDR };\n\t\n\t  // SSL options for Node.js client\n\t  opts.pfx = this.pfx;\n\t  opts.key = this.key;\n\t  opts.passphrase = this.passphrase;\n\t  opts.cert = this.cert;\n\t  opts.ca = this.ca;\n\t  opts.ciphers = this.ciphers;\n\t  opts.rejectUnauthorized = this.rejectUnauthorized;\n\t\n\t  var xhr = this.xhr = new XMLHttpRequest(opts);\n\t  var self = this;\n\t\n\t  try {\n\t\n\t    xhr.open(this.method, this.uri, this.async);\n\t    try {\n\t      if (this.extraHeaders) {\n\t        xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n\t        for (var i in this.extraHeaders) {\n\t          if (this.extraHeaders.hasOwnProperty(i)) {\n\t            xhr.setRequestHeader(i, this.extraHeaders[i]);\n\t          }\n\t        }\n\t      }\n\t    } catch (e) {}\n\t\n\t    if ('POST' === this.method) {\n\t      try {\n\t        if (this.isBinary) {\n\t          xhr.setRequestHeader('Content-type', 'application/octet-stream');\n\t        } else {\n\t          xhr.setRequestHeader('Content-type', 'text/plain;charset=UTF-8');\n\t        }\n\t      } catch (e) {}\n\t    }\n\t\n\t    try {\n\t      xhr.setRequestHeader('Accept', '*/*');\n\t    } catch (e) {}\n\t\n\t    // ie6 check\n\t    if ('withCredentials' in xhr) {\n\t      xhr.withCredentials = this.withCredentials;\n\t    }\n\t\n\t    if (this.requestTimeout) {\n\t      xhr.timeout = this.requestTimeout;\n\t    }\n\t\n\t    if (this.hasXDR()) {\n\t      xhr.onload = function () {\n\t        self.onLoad();\n\t      };\n\t      xhr.onerror = function () {\n\t        self.onError(xhr.responseText);\n\t      };\n\t    } else {\n\t      xhr.onreadystatechange = function () {\n\t        if (xhr.readyState === 2) {\n\t          try {\n\t            var contentType = xhr.getResponseHeader('Content-Type');\n\t            if (self.supportsBinary && contentType === 'application/octet-stream' || contentType === 'application/octet-stream; charset=UTF-8') {\n\t              xhr.responseType = 'arraybuffer';\n\t            }\n\t          } catch (e) {}\n\t        }\n\t        if (4 !== xhr.readyState) return;\n\t        if (200 === xhr.status || 1223 === xhr.status) {\n\t          self.onLoad();\n\t        } else {\n\t          // make sure the `error` event handler that's user-set\n\t          // does not throw in the same tick and gets caught here\n\t          setTimeout(function () {\n\t            self.onError(typeof xhr.status === 'number' ? xhr.status : 0);\n\t          }, 0);\n\t        }\n\t      };\n\t    }\n\t\n\t\n\t    xhr.send(this.data);\n\t  } catch (e) {\n\t    // Need to defer since .create() is called directly fhrom the constructor\n\t    // and thus the 'error' event can only be only bound *after* this exception\n\t    // occurs.  Therefore, also, we cannot throw here at all.\n\t    setTimeout(function () {\n\t      self.onError(e);\n\t    }, 0);\n\t    return;\n\t  }\n\t\n\t  if (typeof document !== 'undefined') {\n\t    this.index = Request.requestsCount++;\n\t    Request.requests[this.index] = this;\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon successful response.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.onSuccess = function () {\n\t  this.emit('success');\n\t  this.cleanup();\n\t};\n\t\n\t/**\n\t * Called if we have data.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.onData = function (data) {\n\t  this.emit('data', data);\n\t  this.onSuccess();\n\t};\n\t\n\t/**\n\t * Called upon error.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.onError = function (err) {\n\t  this.emit('error', err);\n\t  this.cleanup(true);\n\t};\n\t\n\t/**\n\t * Cleans up house.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.cleanup = function (fromError) {\n\t  if ('undefined' === typeof this.xhr || null === this.xhr) {\n\t    return;\n\t  }\n\t  // xmlhttprequest\n\t  if (this.hasXDR()) {\n\t    this.xhr.onload = this.xhr.onerror = empty;\n\t  } else {\n\t    this.xhr.onreadystatechange = empty;\n\t  }\n\t\n\t  if (fromError) {\n\t    try {\n\t      this.xhr.abort();\n\t    } catch (e) {}\n\t  }\n\t\n\t  if (typeof document !== 'undefined') {\n\t    delete Request.requests[this.index];\n\t  }\n\t\n\t  this.xhr = null;\n\t};\n\t\n\t/**\n\t * Called upon load.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.onLoad = function () {\n\t  var data;\n\t  try {\n\t    var contentType;\n\t    try {\n\t      contentType = this.xhr.getResponseHeader('Content-Type');\n\t    } catch (e) {}\n\t    if (contentType === 'application/octet-stream' || contentType === 'application/octet-stream; charset=UTF-8') {\n\t      data = this.xhr.response || this.xhr.responseText;\n\t    } else {\n\t      data = this.xhr.responseText;\n\t    }\n\t  } catch (e) {\n\t    this.onError(e);\n\t  }\n\t  if (null != data) {\n\t    this.onData(data);\n\t  }\n\t};\n\t\n\t/**\n\t * Check if it has XDomainRequest.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.hasXDR = function () {\n\t  return typeof XDomainRequest !== 'undefined' && !this.xs && this.enablesXDR;\n\t};\n\t\n\t/**\n\t * Aborts the request.\n\t *\n\t * @api public\n\t */\n\t\n\tRequest.prototype.abort = function () {\n\t  this.cleanup();\n\t};\n\t\n\t/**\n\t * Aborts pending requests when unloading the window. This is needed to prevent\n\t * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n\t * emitted.\n\t */\n\t\n\tRequest.requestsCount = 0;\n\tRequest.requests = {};\n\t\n\tif (typeof document !== 'undefined') {\n\t  if (typeof attachEvent === 'function') {\n\t    attachEvent('onunload', unloadHandler);\n\t  } else if (typeof addEventListener === 'function') {\n\t    var terminationEvent = 'onpagehide' in globalThis ? 'pagehide' : 'unload';\n\t    addEventListener(terminationEvent, unloadHandler, false);\n\t  }\n\t}\n\t\n\tfunction unloadHandler () {\n\t  for (var i in Request.requests) {\n\t    if (Request.requests.hasOwnProperty(i)) {\n\t      Request.requests[i].abort();\n\t    }\n\t  }\n\t}\n\n\n/***/ }),\n/* 17 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar Transport = __webpack_require__(18);\n\tvar parseqs = __webpack_require__(27);\n\tvar parser = __webpack_require__(19);\n\tvar inherit = __webpack_require__(28);\n\tvar yeast = __webpack_require__(29);\n\tvar debug = __webpack_require__(3)('engine.io-client:polling');\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = Polling;\n\t\n\t/**\n\t * Is XHR2 supported?\n\t */\n\t\n\tvar hasXHR2 = (function () {\n\t  var XMLHttpRequest = __webpack_require__(13);\n\t  var xhr = new XMLHttpRequest({ xdomain: false });\n\t  return null != xhr.responseType;\n\t})();\n\t\n\t/**\n\t * Polling interface.\n\t *\n\t * @param {Object} opts\n\t * @api private\n\t */\n\t\n\tfunction Polling (opts) {\n\t  var forceBase64 = (opts && opts.forceBase64);\n\t  if (!hasXHR2 || forceBase64) {\n\t    this.supportsBinary = false;\n\t  }\n\t  Transport.call(this, opts);\n\t}\n\t\n\t/**\n\t * Inherits from Transport.\n\t */\n\t\n\tinherit(Polling, Transport);\n\t\n\t/**\n\t * Transport name.\n\t */\n\t\n\tPolling.prototype.name = 'polling';\n\t\n\t/**\n\t * Opens the socket (triggers polling). We write a PING message to determine\n\t * when the transport is open.\n\t *\n\t * @api private\n\t */\n\t\n\tPolling.prototype.doOpen = function () {\n\t  this.poll();\n\t};\n\t\n\t/**\n\t * Pauses polling.\n\t *\n\t * @param {Function} callback upon buffers are flushed and transport is paused\n\t * @api private\n\t */\n\t\n\tPolling.prototype.pause = function (onPause) {\n\t  var self = this;\n\t\n\t  this.readyState = 'pausing';\n\t\n\t  function pause () {\n\t\n\t    self.readyState = 'paused';\n\t    onPause();\n\t  }\n\t\n\t  if (this.polling || !this.writable) {\n\t    var total = 0;\n\t\n\t    if (this.polling) {\n\t\n\t      total++;\n\t      this.once('pollComplete', function () {\n\t\n\t        --total || pause();\n\t      });\n\t    }\n\t\n\t    if (!this.writable) {\n\t\n\t      total++;\n\t      this.once('drain', function () {\n\t\n\t        --total || pause();\n\t      });\n\t    }\n\t  } else {\n\t    pause();\n\t  }\n\t};\n\t\n\t/**\n\t * Starts polling cycle.\n\t *\n\t * @api public\n\t */\n\t\n\tPolling.prototype.poll = function () {\n\t\n\t  this.polling = true;\n\t  this.doPoll();\n\t  this.emit('poll');\n\t};\n\t\n\t/**\n\t * Overloads onData to detect payloads.\n\t *\n\t * @api private\n\t */\n\t\n\tPolling.prototype.onData = function (data) {\n\t  var self = this;\n\t\n\t  var callback = function (packet, index, total) {\n\t    // if its the first message we consider the transport open\n\t    if ('opening' === self.readyState && packet.type === 'open') {\n\t      self.onOpen();\n\t    }\n\t\n\t    // if its a close packet, we close the ongoing requests\n\t    if ('close' === packet.type) {\n\t      self.onClose();\n\t      return false;\n\t    }\n\t\n\t    // otherwise bypass onData and handle the message\n\t    self.onPacket(packet);\n\t  };\n\t\n\t  // decode payload\n\t  parser.decodePayload(data, this.socket.binaryType, callback);\n\t\n\t  // if an event did not trigger closing\n\t  if ('closed' !== this.readyState) {\n\t    // if we got data we're not polling\n\t    this.polling = false;\n\t    this.emit('pollComplete');\n\t\n\t    if ('open' === this.readyState) {\n\t      this.poll();\n\t    } else {\n\t\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * For polling, send a close packet.\n\t *\n\t * @api private\n\t */\n\t\n\tPolling.prototype.doClose = function () {\n\t  var self = this;\n\t\n\t  function close () {\n\t\n\t    self.write([{ type: 'close' }]);\n\t  }\n\t\n\t  if ('open' === this.readyState) {\n\t\n\t    close();\n\t  } else {\n\t    // in case we're trying to close while\n\t    // handshaking is in progress (GH-164)\n\t\n\t    this.once('open', close);\n\t  }\n\t};\n\t\n\t/**\n\t * Writes a packets payload.\n\t *\n\t * @param {Array} data packets\n\t * @param {Function} drain callback\n\t * @api private\n\t */\n\t\n\tPolling.prototype.write = function (packets) {\n\t  var self = this;\n\t  this.writable = false;\n\t  var callbackfn = function () {\n\t    self.writable = true;\n\t    self.emit('drain');\n\t  };\n\t\n\t  parser.encodePayload(packets, this.supportsBinary, function (data) {\n\t    self.doWrite(data, callbackfn);\n\t  });\n\t};\n\t\n\t/**\n\t * Generates uri for connection.\n\t *\n\t * @api private\n\t */\n\t\n\tPolling.prototype.uri = function () {\n\t  var query = this.query || {};\n\t  var schema = this.secure ? 'https' : 'http';\n\t  var port = '';\n\t\n\t  // cache busting is forced\n\t  if (false !== this.timestampRequests) {\n\t    query[this.timestampParam] = yeast();\n\t  }\n\t\n\t  if (!this.supportsBinary && !query.sid) {\n\t    query.b64 = 1;\n\t  }\n\t\n\t  query = parseqs.encode(query);\n\t\n\t  // avoid port if default for schema\n\t  if (this.port && (('https' === schema && Number(this.port) !== 443) ||\n\t     ('http' === schema && Number(this.port) !== 80))) {\n\t    port = ':' + this.port;\n\t  }\n\t\n\t  // prepend ? to query\n\t  if (query.length) {\n\t    query = '?' + query;\n\t  }\n\t\n\t  var ipv6 = this.hostname.indexOf(':') !== -1;\n\t  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n\t};\n\n\n/***/ }),\n/* 18 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar parser = __webpack_require__(19);\n\tvar Emitter = __webpack_require__(5);\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = Transport;\n\t\n\t/**\n\t * Transport abstract constructor.\n\t *\n\t * @param {Object} options.\n\t * @api private\n\t */\n\t\n\tfunction Transport (opts) {\n\t  this.path = opts.path;\n\t  this.hostname = opts.hostname;\n\t  this.port = opts.port;\n\t  this.secure = opts.secure;\n\t  this.query = opts.query;\n\t  this.timestampParam = opts.timestampParam;\n\t  this.timestampRequests = opts.timestampRequests;\n\t  this.readyState = '';\n\t  this.agent = opts.agent || false;\n\t  this.socket = opts.socket;\n\t  this.enablesXDR = opts.enablesXDR;\n\t  this.withCredentials = opts.withCredentials;\n\t\n\t  // SSL options for Node.js client\n\t  this.pfx = opts.pfx;\n\t  this.key = opts.key;\n\t  this.passphrase = opts.passphrase;\n\t  this.cert = opts.cert;\n\t  this.ca = opts.ca;\n\t  this.ciphers = opts.ciphers;\n\t  this.rejectUnauthorized = opts.rejectUnauthorized;\n\t  this.forceNode = opts.forceNode;\n\t\n\t  // results of ReactNative environment detection\n\t  this.isReactNative = opts.isReactNative;\n\t\n\t  // other options for Node.js client\n\t  this.extraHeaders = opts.extraHeaders;\n\t  this.localAddress = opts.localAddress;\n\t}\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Transport.prototype);\n\t\n\t/**\n\t * Emits an error.\n\t *\n\t * @param {String} str\n\t * @return {Transport} for chaining\n\t * @api public\n\t */\n\t\n\tTransport.prototype.onError = function (msg, desc) {\n\t  var err = new Error(msg);\n\t  err.type = 'TransportError';\n\t  err.description = desc;\n\t  this.emit('error', err);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Opens the transport.\n\t *\n\t * @api public\n\t */\n\t\n\tTransport.prototype.open = function () {\n\t  if ('closed' === this.readyState || '' === this.readyState) {\n\t    this.readyState = 'opening';\n\t    this.doOpen();\n\t  }\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Closes the transport.\n\t *\n\t * @api private\n\t */\n\t\n\tTransport.prototype.close = function () {\n\t  if ('opening' === this.readyState || 'open' === this.readyState) {\n\t    this.doClose();\n\t    this.onClose();\n\t  }\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sends multiple packets.\n\t *\n\t * @param {Array} packets\n\t * @api private\n\t */\n\t\n\tTransport.prototype.send = function (packets) {\n\t  if ('open' === this.readyState) {\n\t    this.write(packets);\n\t  } else {\n\t    throw new Error('Transport not open');\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon open\n\t *\n\t * @api private\n\t */\n\t\n\tTransport.prototype.onOpen = function () {\n\t  this.readyState = 'open';\n\t  this.writable = true;\n\t  this.emit('open');\n\t};\n\t\n\t/**\n\t * Called with data.\n\t *\n\t * @param {String} data\n\t * @api private\n\t */\n\t\n\tTransport.prototype.onData = function (data) {\n\t  var packet = parser.decodePacket(data, this.socket.binaryType);\n\t  this.onPacket(packet);\n\t};\n\t\n\t/**\n\t * Called with a decoded packet.\n\t */\n\t\n\tTransport.prototype.onPacket = function (packet) {\n\t  this.emit('packet', packet);\n\t};\n\t\n\t/**\n\t * Called upon close.\n\t *\n\t * @api private\n\t */\n\t\n\tTransport.prototype.onClose = function () {\n\t  this.readyState = 'closed';\n\t  this.emit('close');\n\t};\n\n\n/***/ }),\n/* 19 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar keys = __webpack_require__(20);\n\tvar hasBinary = __webpack_require__(21);\n\tvar sliceBuffer = __webpack_require__(22);\n\tvar after = __webpack_require__(23);\n\tvar utf8 = __webpack_require__(24);\n\t\n\tvar base64encoder;\n\tif (typeof ArrayBuffer !== 'undefined') {\n\t  base64encoder = __webpack_require__(25);\n\t}\n\t\n\t/**\n\t * Check if we are running an android browser. That requires us to use\n\t * ArrayBuffer with polling transports...\n\t *\n\t * http://ghinda.net/jpeg-blob-ajax-android/\n\t */\n\t\n\tvar isAndroid = typeof navigator !== 'undefined' && /Android/i.test(navigator.userAgent);\n\t\n\t/**\n\t * Check if we are running in PhantomJS.\n\t * Uploading a Blob with PhantomJS does not work correctly, as reported here:\n\t * https://github.com/ariya/phantomjs/issues/11395\n\t * @type boolean\n\t */\n\tvar isPhantomJS = typeof navigator !== 'undefined' && /PhantomJS/i.test(navigator.userAgent);\n\t\n\t/**\n\t * When true, avoids using Blobs to encode payloads.\n\t * @type boolean\n\t */\n\tvar dontSendBlobs = isAndroid || isPhantomJS;\n\t\n\t/**\n\t * Current protocol version.\n\t */\n\t\n\texports.protocol = 3;\n\t\n\t/**\n\t * Packet types.\n\t */\n\t\n\tvar packets = exports.packets = {\n\t    open:     0    // non-ws\n\t  , close:    1    // non-ws\n\t  , ping:     2\n\t  , pong:     3\n\t  , message:  4\n\t  , upgrade:  5\n\t  , noop:     6\n\t};\n\t\n\tvar packetslist = keys(packets);\n\t\n\t/**\n\t * Premade error packet.\n\t */\n\t\n\tvar err = { type: 'error', data: 'parser error' };\n\t\n\t/**\n\t * Create a blob api even for blob builder when vendor prefixes exist\n\t */\n\t\n\tvar Blob = __webpack_require__(26);\n\t\n\t/**\n\t * Encodes a packet.\n\t *\n\t *     <packet type id> [ <data> ]\n\t *\n\t * Example:\n\t *\n\t *     5hello world\n\t *     3\n\t *     4\n\t *\n\t * Binary is encoded in an identical principle\n\t *\n\t * @api private\n\t */\n\t\n\texports.encodePacket = function (packet, supportsBinary, utf8encode, callback) {\n\t  if (typeof supportsBinary === 'function') {\n\t    callback = supportsBinary;\n\t    supportsBinary = false;\n\t  }\n\t\n\t  if (typeof utf8encode === 'function') {\n\t    callback = utf8encode;\n\t    utf8encode = null;\n\t  }\n\t\n\t  var data = (packet.data === undefined)\n\t    ? undefined\n\t    : packet.data.buffer || packet.data;\n\t\n\t  if (typeof ArrayBuffer !== 'undefined' && data instanceof ArrayBuffer) {\n\t    return encodeArrayBuffer(packet, supportsBinary, callback);\n\t  } else if (typeof Blob !== 'undefined' && data instanceof Blob) {\n\t    return encodeBlob(packet, supportsBinary, callback);\n\t  }\n\t\n\t  // might be an object with { base64: true, data: dataAsBase64String }\n\t  if (data && data.base64) {\n\t    return encodeBase64Object(packet, callback);\n\t  }\n\t\n\t  // Sending data as a utf-8 string\n\t  var encoded = packets[packet.type];\n\t\n\t  // data fragment is optional\n\t  if (undefined !== packet.data) {\n\t    encoded += utf8encode ? utf8.encode(String(packet.data), { strict: false }) : String(packet.data);\n\t  }\n\t\n\t  return callback('' + encoded);\n\t\n\t};\n\t\n\tfunction encodeBase64Object(packet, callback) {\n\t  // packet data is an object { base64: true, data: dataAsBase64String }\n\t  var message = 'b' + exports.packets[packet.type] + packet.data.data;\n\t  return callback(message);\n\t}\n\t\n\t/**\n\t * Encode packet helpers for binary types\n\t */\n\t\n\tfunction encodeArrayBuffer(packet, supportsBinary, callback) {\n\t  if (!supportsBinary) {\n\t    return exports.encodeBase64Packet(packet, callback);\n\t  }\n\t\n\t  var data = packet.data;\n\t  var contentArray = new Uint8Array(data);\n\t  var resultBuffer = new Uint8Array(1 + data.byteLength);\n\t\n\t  resultBuffer[0] = packets[packet.type];\n\t  for (var i = 0; i < contentArray.length; i++) {\n\t    resultBuffer[i+1] = contentArray[i];\n\t  }\n\t\n\t  return callback(resultBuffer.buffer);\n\t}\n\t\n\tfunction encodeBlobAsArrayBuffer(packet, supportsBinary, callback) {\n\t  if (!supportsBinary) {\n\t    return exports.encodeBase64Packet(packet, callback);\n\t  }\n\t\n\t  var fr = new FileReader();\n\t  fr.onload = function() {\n\t    exports.encodePacket({ type: packet.type, data: fr.result }, supportsBinary, true, callback);\n\t  };\n\t  return fr.readAsArrayBuffer(packet.data);\n\t}\n\t\n\tfunction encodeBlob(packet, supportsBinary, callback) {\n\t  if (!supportsBinary) {\n\t    return exports.encodeBase64Packet(packet, callback);\n\t  }\n\t\n\t  if (dontSendBlobs) {\n\t    return encodeBlobAsArrayBuffer(packet, supportsBinary, callback);\n\t  }\n\t\n\t  var length = new Uint8Array(1);\n\t  length[0] = packets[packet.type];\n\t  var blob = new Blob([length.buffer, packet.data]);\n\t\n\t  return callback(blob);\n\t}\n\t\n\t/**\n\t * Encodes a packet with binary data in a base64 string\n\t *\n\t * @param {Object} packet, has `type` and `data`\n\t * @return {String} base64 encoded message\n\t */\n\t\n\texports.encodeBase64Packet = function(packet, callback) {\n\t  var message = 'b' + exports.packets[packet.type];\n\t  if (typeof Blob !== 'undefined' && packet.data instanceof Blob) {\n\t    var fr = new FileReader();\n\t    fr.onload = function() {\n\t      var b64 = fr.result.split(',')[1];\n\t      callback(message + b64);\n\t    };\n\t    return fr.readAsDataURL(packet.data);\n\t  }\n\t\n\t  var b64data;\n\t  try {\n\t    b64data = String.fromCharCode.apply(null, new Uint8Array(packet.data));\n\t  } catch (e) {\n\t    // iPhone Safari doesn't let you apply with typed arrays\n\t    var typed = new Uint8Array(packet.data);\n\t    var basic = new Array(typed.length);\n\t    for (var i = 0; i < typed.length; i++) {\n\t      basic[i] = typed[i];\n\t    }\n\t    b64data = String.fromCharCode.apply(null, basic);\n\t  }\n\t  message += btoa(b64data);\n\t  return callback(message);\n\t};\n\t\n\t/**\n\t * Decodes a packet. Changes format to Blob if requested.\n\t *\n\t * @return {Object} with `type` and `data` (if any)\n\t * @api private\n\t */\n\t\n\texports.decodePacket = function (data, binaryType, utf8decode) {\n\t  if (data === undefined) {\n\t    return err;\n\t  }\n\t  // String data\n\t  if (typeof data === 'string') {\n\t    if (data.charAt(0) === 'b') {\n\t      return exports.decodeBase64Packet(data.substr(1), binaryType);\n\t    }\n\t\n\t    if (utf8decode) {\n\t      data = tryDecode(data);\n\t      if (data === false) {\n\t        return err;\n\t      }\n\t    }\n\t    var type = data.charAt(0);\n\t\n\t    if (Number(type) != type || !packetslist[type]) {\n\t      return err;\n\t    }\n\t\n\t    if (data.length > 1) {\n\t      return { type: packetslist[type], data: data.substring(1) };\n\t    } else {\n\t      return { type: packetslist[type] };\n\t    }\n\t  }\n\t\n\t  var asArray = new Uint8Array(data);\n\t  var type = asArray[0];\n\t  var rest = sliceBuffer(data, 1);\n\t  if (Blob && binaryType === 'blob') {\n\t    rest = new Blob([rest]);\n\t  }\n\t  return { type: packetslist[type], data: rest };\n\t};\n\t\n\tfunction tryDecode(data) {\n\t  try {\n\t    data = utf8.decode(data, { strict: false });\n\t  } catch (e) {\n\t    return false;\n\t  }\n\t  return data;\n\t}\n\t\n\t/**\n\t * Decodes a packet encoded in a base64 string\n\t *\n\t * @param {String} base64 encoded message\n\t * @return {Object} with `type` and `data` (if any)\n\t */\n\t\n\texports.decodeBase64Packet = function(msg, binaryType) {\n\t  var type = packetslist[msg.charAt(0)];\n\t  if (!base64encoder) {\n\t    return { type: type, data: { base64: true, data: msg.substr(1) } };\n\t  }\n\t\n\t  var data = base64encoder.decode(msg.substr(1));\n\t\n\t  if (binaryType === 'blob' && Blob) {\n\t    data = new Blob([data]);\n\t  }\n\t\n\t  return { type: type, data: data };\n\t};\n\t\n\t/**\n\t * Encodes multiple messages (payload).\n\t *\n\t *     <length>:data\n\t *\n\t * Example:\n\t *\n\t *     11:hello world2:hi\n\t *\n\t * If any contents are binary, they will be encoded as base64 strings. Base64\n\t * encoded strings are marked with a b before the length specifier\n\t *\n\t * @param {Array} packets\n\t * @api private\n\t */\n\t\n\texports.encodePayload = function (packets, supportsBinary, callback) {\n\t  if (typeof supportsBinary === 'function') {\n\t    callback = supportsBinary;\n\t    supportsBinary = null;\n\t  }\n\t\n\t  var isBinary = hasBinary(packets);\n\t\n\t  if (supportsBinary && isBinary) {\n\t    if (Blob && !dontSendBlobs) {\n\t      return exports.encodePayloadAsBlob(packets, callback);\n\t    }\n\t\n\t    return exports.encodePayloadAsArrayBuffer(packets, callback);\n\t  }\n\t\n\t  if (!packets.length) {\n\t    return callback('0:');\n\t  }\n\t\n\t  function setLengthHeader(message) {\n\t    return message.length + ':' + message;\n\t  }\n\t\n\t  function encodeOne(packet, doneCallback) {\n\t    exports.encodePacket(packet, !isBinary ? false : supportsBinary, false, function(message) {\n\t      doneCallback(null, setLengthHeader(message));\n\t    });\n\t  }\n\t\n\t  map(packets, encodeOne, function(err, results) {\n\t    return callback(results.join(''));\n\t  });\n\t};\n\t\n\t/**\n\t * Async array map using after\n\t */\n\t\n\tfunction map(ary, each, done) {\n\t  var result = new Array(ary.length);\n\t  var next = after(ary.length, done);\n\t\n\t  var eachWithIndex = function(i, el, cb) {\n\t    each(el, function(error, msg) {\n\t      result[i] = msg;\n\t      cb(error, result);\n\t    });\n\t  };\n\t\n\t  for (var i = 0; i < ary.length; i++) {\n\t    eachWithIndex(i, ary[i], next);\n\t  }\n\t}\n\t\n\t/*\n\t * Decodes data when a payload is maybe expected. Possible binary contents are\n\t * decoded from their base64 representation\n\t *\n\t * @param {String} data, callback method\n\t * @api public\n\t */\n\t\n\texports.decodePayload = function (data, binaryType, callback) {\n\t  if (typeof data !== 'string') {\n\t    return exports.decodePayloadAsBinary(data, binaryType, callback);\n\t  }\n\t\n\t  if (typeof binaryType === 'function') {\n\t    callback = binaryType;\n\t    binaryType = null;\n\t  }\n\t\n\t  var packet;\n\t  if (data === '') {\n\t    // parser error - ignoring payload\n\t    return callback(err, 0, 1);\n\t  }\n\t\n\t  var length = '', n, msg;\n\t\n\t  for (var i = 0, l = data.length; i < l; i++) {\n\t    var chr = data.charAt(i);\n\t\n\t    if (chr !== ':') {\n\t      length += chr;\n\t      continue;\n\t    }\n\t\n\t    if (length === '' || (length != (n = Number(length)))) {\n\t      // parser error - ignoring payload\n\t      return callback(err, 0, 1);\n\t    }\n\t\n\t    msg = data.substr(i + 1, n);\n\t\n\t    if (length != msg.length) {\n\t      // parser error - ignoring payload\n\t      return callback(err, 0, 1);\n\t    }\n\t\n\t    if (msg.length) {\n\t      packet = exports.decodePacket(msg, binaryType, false);\n\t\n\t      if (err.type === packet.type && err.data === packet.data) {\n\t        // parser error in individual packet - ignoring payload\n\t        return callback(err, 0, 1);\n\t      }\n\t\n\t      var ret = callback(packet, i + n, l);\n\t      if (false === ret) return;\n\t    }\n\t\n\t    // advance cursor\n\t    i += n;\n\t    length = '';\n\t  }\n\t\n\t  if (length !== '') {\n\t    // parser error - ignoring payload\n\t    return callback(err, 0, 1);\n\t  }\n\t\n\t};\n\t\n\t/**\n\t * Encodes multiple messages (payload) as binary.\n\t *\n\t * <1 = binary, 0 = string><number from 0-9><number from 0-9>[...]<number\n\t * 255><data>\n\t *\n\t * Example:\n\t * 1 3 255 1 2 3, if the binary contents are interpreted as 8 bit integers\n\t *\n\t * @param {Array} packets\n\t * @return {ArrayBuffer} encoded payload\n\t * @api private\n\t */\n\t\n\texports.encodePayloadAsArrayBuffer = function(packets, callback) {\n\t  if (!packets.length) {\n\t    return callback(new ArrayBuffer(0));\n\t  }\n\t\n\t  function encodeOne(packet, doneCallback) {\n\t    exports.encodePacket(packet, true, true, function(data) {\n\t      return doneCallback(null, data);\n\t    });\n\t  }\n\t\n\t  map(packets, encodeOne, function(err, encodedPackets) {\n\t    var totalLength = encodedPackets.reduce(function(acc, p) {\n\t      var len;\n\t      if (typeof p === 'string'){\n\t        len = p.length;\n\t      } else {\n\t        len = p.byteLength;\n\t      }\n\t      return acc + len.toString().length + len + 2; // string/binary identifier + separator = 2\n\t    }, 0);\n\t\n\t    var resultArray = new Uint8Array(totalLength);\n\t\n\t    var bufferIndex = 0;\n\t    encodedPackets.forEach(function(p) {\n\t      var isString = typeof p === 'string';\n\t      var ab = p;\n\t      if (isString) {\n\t        var view = new Uint8Array(p.length);\n\t        for (var i = 0; i < p.length; i++) {\n\t          view[i] = p.charCodeAt(i);\n\t        }\n\t        ab = view.buffer;\n\t      }\n\t\n\t      if (isString) { // not true binary\n\t        resultArray[bufferIndex++] = 0;\n\t      } else { // true binary\n\t        resultArray[bufferIndex++] = 1;\n\t      }\n\t\n\t      var lenStr = ab.byteLength.toString();\n\t      for (var i = 0; i < lenStr.length; i++) {\n\t        resultArray[bufferIndex++] = parseInt(lenStr[i]);\n\t      }\n\t      resultArray[bufferIndex++] = 255;\n\t\n\t      var view = new Uint8Array(ab);\n\t      for (var i = 0; i < view.length; i++) {\n\t        resultArray[bufferIndex++] = view[i];\n\t      }\n\t    });\n\t\n\t    return callback(resultArray.buffer);\n\t  });\n\t};\n\t\n\t/**\n\t * Encode as Blob\n\t */\n\t\n\texports.encodePayloadAsBlob = function(packets, callback) {\n\t  function encodeOne(packet, doneCallback) {\n\t    exports.encodePacket(packet, true, true, function(encoded) {\n\t      var binaryIdentifier = new Uint8Array(1);\n\t      binaryIdentifier[0] = 1;\n\t      if (typeof encoded === 'string') {\n\t        var view = new Uint8Array(encoded.length);\n\t        for (var i = 0; i < encoded.length; i++) {\n\t          view[i] = encoded.charCodeAt(i);\n\t        }\n\t        encoded = view.buffer;\n\t        binaryIdentifier[0] = 0;\n\t      }\n\t\n\t      var len = (encoded instanceof ArrayBuffer)\n\t        ? encoded.byteLength\n\t        : encoded.size;\n\t\n\t      var lenStr = len.toString();\n\t      var lengthAry = new Uint8Array(lenStr.length + 1);\n\t      for (var i = 0; i < lenStr.length; i++) {\n\t        lengthAry[i] = parseInt(lenStr[i]);\n\t      }\n\t      lengthAry[lenStr.length] = 255;\n\t\n\t      if (Blob) {\n\t        var blob = new Blob([binaryIdentifier.buffer, lengthAry.buffer, encoded]);\n\t        doneCallback(null, blob);\n\t      }\n\t    });\n\t  }\n\t\n\t  map(packets, encodeOne, function(err, results) {\n\t    return callback(new Blob(results));\n\t  });\n\t};\n\t\n\t/*\n\t * Decodes data when a payload is maybe expected. Strings are decoded by\n\t * interpreting each byte as a key code for entries marked to start with 0. See\n\t * description of encodePayloadAsBinary\n\t *\n\t * @param {ArrayBuffer} data, callback method\n\t * @api public\n\t */\n\t\n\texports.decodePayloadAsBinary = function (data, binaryType, callback) {\n\t  if (typeof binaryType === 'function') {\n\t    callback = binaryType;\n\t    binaryType = null;\n\t  }\n\t\n\t  var bufferTail = data;\n\t  var buffers = [];\n\t\n\t  while (bufferTail.byteLength > 0) {\n\t    var tailArray = new Uint8Array(bufferTail);\n\t    var isString = tailArray[0] === 0;\n\t    var msgLength = '';\n\t\n\t    for (var i = 1; ; i++) {\n\t      if (tailArray[i] === 255) break;\n\t\n\t      // 310 = char length of Number.MAX_VALUE\n\t      if (msgLength.length > 310) {\n\t        return callback(err, 0, 1);\n\t      }\n\t\n\t      msgLength += tailArray[i];\n\t    }\n\t\n\t    bufferTail = sliceBuffer(bufferTail, 2 + msgLength.length);\n\t    msgLength = parseInt(msgLength);\n\t\n\t    var msg = sliceBuffer(bufferTail, 0, msgLength);\n\t    if (isString) {\n\t      try {\n\t        msg = String.fromCharCode.apply(null, new Uint8Array(msg));\n\t      } catch (e) {\n\t        // iPhone Safari doesn't let you apply to typed arrays\n\t        var typed = new Uint8Array(msg);\n\t        msg = '';\n\t        for (var i = 0; i < typed.length; i++) {\n\t          msg += String.fromCharCode(typed[i]);\n\t        }\n\t      }\n\t    }\n\t\n\t    buffers.push(msg);\n\t    bufferTail = sliceBuffer(bufferTail, msgLength);\n\t  }\n\t\n\t  var total = buffers.length;\n\t  buffers.forEach(function(buffer, i) {\n\t    callback(exports.decodePacket(buffer, binaryType, true), i, total);\n\t  });\n\t};\n\n\n/***/ }),\n/* 20 */\n/***/ (function(module, exports) {\n\n\t\n\t/**\n\t * Gets the keys for an object.\n\t *\n\t * @return {Array} keys\n\t * @api private\n\t */\n\t\n\tmodule.exports = Object.keys || function keys (obj){\n\t  var arr = [];\n\t  var has = Object.prototype.hasOwnProperty;\n\t\n\t  for (var i in obj) {\n\t    if (has.call(obj, i)) {\n\t      arr.push(i);\n\t    }\n\t  }\n\t  return arr;\n\t};\n\n\n/***/ }),\n/* 21 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* global Blob File */\n\t\n\t/*\n\t * Module requirements.\n\t */\n\t\n\tvar isArray = __webpack_require__(7);\n\t\n\tvar toString = Object.prototype.toString;\n\tvar withNativeBlob = typeof Blob === 'function' ||\n\t                        typeof Blob !== 'undefined' && toString.call(Blob) === '[object BlobConstructor]';\n\tvar withNativeFile = typeof File === 'function' ||\n\t                        typeof File !== 'undefined' && toString.call(File) === '[object FileConstructor]';\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = hasBinary;\n\t\n\t/**\n\t * Checks for binary data.\n\t *\n\t * Supports Buffer, ArrayBuffer, Blob and File.\n\t *\n\t * @param {Object} anything\n\t * @api public\n\t */\n\t\n\tfunction hasBinary (obj) {\n\t  if (!obj || typeof obj !== 'object') {\n\t    return false;\n\t  }\n\t\n\t  if (isArray(obj)) {\n\t    for (var i = 0, l = obj.length; i < l; i++) {\n\t      if (hasBinary(obj[i])) {\n\t        return true;\n\t      }\n\t    }\n\t    return false;\n\t  }\n\t\n\t  if ((typeof Buffer === 'function' && Buffer.isBuffer && Buffer.isBuffer(obj)) ||\n\t    (typeof ArrayBuffer === 'function' && obj instanceof ArrayBuffer) ||\n\t    (withNativeBlob && obj instanceof Blob) ||\n\t    (withNativeFile && obj instanceof File)\n\t  ) {\n\t    return true;\n\t  }\n\t\n\t  // see: https://github.com/Automattic/has-binary/pull/4\n\t  if (obj.toJSON && typeof obj.toJSON === 'function' && arguments.length === 1) {\n\t    return hasBinary(obj.toJSON(), true);\n\t  }\n\t\n\t  for (var key in obj) {\n\t    if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n\t      return true;\n\t    }\n\t  }\n\t\n\t  return false;\n\t}\n\n\n/***/ }),\n/* 22 */\n/***/ (function(module, exports) {\n\n\t/**\n\t * An abstraction for slicing an arraybuffer even when\n\t * ArrayBuffer.prototype.slice is not supported\n\t *\n\t * @api public\n\t */\n\t\n\tmodule.exports = function(arraybuffer, start, end) {\n\t  var bytes = arraybuffer.byteLength;\n\t  start = start || 0;\n\t  end = end || bytes;\n\t\n\t  if (arraybuffer.slice) { return arraybuffer.slice(start, end); }\n\t\n\t  if (start < 0) { start += bytes; }\n\t  if (end < 0) { end += bytes; }\n\t  if (end > bytes) { end = bytes; }\n\t\n\t  if (start >= bytes || start >= end || bytes === 0) {\n\t    return new ArrayBuffer(0);\n\t  }\n\t\n\t  var abv = new Uint8Array(arraybuffer);\n\t  var result = new Uint8Array(end - start);\n\t  for (var i = start, ii = 0; i < end; i++, ii++) {\n\t    result[ii] = abv[i];\n\t  }\n\t  return result.buffer;\n\t};\n\n\n/***/ }),\n/* 23 */\n/***/ (function(module, exports) {\n\n\tmodule.exports = after\n\t\n\tfunction after(count, callback, err_cb) {\n\t    var bail = false\n\t    err_cb = err_cb || noop\n\t    proxy.count = count\n\t\n\t    return (count === 0) ? callback() : proxy\n\t\n\t    function proxy(err, result) {\n\t        if (proxy.count <= 0) {\n\t            throw new Error('after called too many times')\n\t        }\n\t        --proxy.count\n\t\n\t        // after first error, rest are passed to err_cb\n\t        if (err) {\n\t            bail = true\n\t            callback(err)\n\t            // future error callbacks will go to error handler\n\t            callback = err_cb\n\t        } else if (proxy.count === 0 && !bail) {\n\t            callback(null, result)\n\t        }\n\t    }\n\t}\n\t\n\tfunction noop() {}\n\n\n/***/ }),\n/* 24 */\n/***/ (function(module, exports) {\n\n\t/*! https://mths.be/utf8js v2.1.2 by @mathias */\n\t\n\tvar stringFromCharCode = String.fromCharCode;\n\t\n\t// Taken from https://mths.be/punycode\n\tfunction ucs2decode(string) {\n\t\tvar output = [];\n\t\tvar counter = 0;\n\t\tvar length = string.length;\n\t\tvar value;\n\t\tvar extra;\n\t\twhile (counter < length) {\n\t\t\tvalue = string.charCodeAt(counter++);\n\t\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t\t// high surrogate, and there is a next character\n\t\t\t\textra = string.charCodeAt(counter++);\n\t\t\t\tif ((extra & 0xFC00) == 0xDC00) { // low surrogate\n\t\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t\t} else {\n\t\t\t\t\t// unmatched surrogate; only append this code unit, in case the next\n\t\t\t\t\t// code unit is the high surrogate of a surrogate pair\n\t\t\t\t\toutput.push(value);\n\t\t\t\t\tcounter--;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\toutput.push(value);\n\t\t\t}\n\t\t}\n\t\treturn output;\n\t}\n\t\n\t// Taken from https://mths.be/punycode\n\tfunction ucs2encode(array) {\n\t\tvar length = array.length;\n\t\tvar index = -1;\n\t\tvar value;\n\t\tvar output = '';\n\t\twhile (++index < length) {\n\t\t\tvalue = array[index];\n\t\t\tif (value > 0xFFFF) {\n\t\t\t\tvalue -= 0x10000;\n\t\t\t\toutput += stringFromCharCode(value >>> 10 & 0x3FF | 0xD800);\n\t\t\t\tvalue = 0xDC00 | value & 0x3FF;\n\t\t\t}\n\t\t\toutput += stringFromCharCode(value);\n\t\t}\n\t\treturn output;\n\t}\n\t\n\tfunction checkScalarValue(codePoint, strict) {\n\t\tif (codePoint >= 0xD800 && codePoint <= 0xDFFF) {\n\t\t\tif (strict) {\n\t\t\t\tthrow Error(\n\t\t\t\t\t'Lone surrogate U+' + codePoint.toString(16).toUpperCase() +\n\t\t\t\t\t' is not a scalar value'\n\t\t\t\t);\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\t\treturn true;\n\t}\n\t/*--------------------------------------------------------------------------*/\n\t\n\tfunction createByte(codePoint, shift) {\n\t\treturn stringFromCharCode(((codePoint >> shift) & 0x3F) | 0x80);\n\t}\n\t\n\tfunction encodeCodePoint(codePoint, strict) {\n\t\tif ((codePoint & 0xFFFFFF80) == 0) { // 1-byte sequence\n\t\t\treturn stringFromCharCode(codePoint);\n\t\t}\n\t\tvar symbol = '';\n\t\tif ((codePoint & 0xFFFFF800) == 0) { // 2-byte sequence\n\t\t\tsymbol = stringFromCharCode(((codePoint >> 6) & 0x1F) | 0xC0);\n\t\t}\n\t\telse if ((codePoint & 0xFFFF0000) == 0) { // 3-byte sequence\n\t\t\tif (!checkScalarValue(codePoint, strict)) {\n\t\t\t\tcodePoint = 0xFFFD;\n\t\t\t}\n\t\t\tsymbol = stringFromCharCode(((codePoint >> 12) & 0x0F) | 0xE0);\n\t\t\tsymbol += createByte(codePoint, 6);\n\t\t}\n\t\telse if ((codePoint & 0xFFE00000) == 0) { // 4-byte sequence\n\t\t\tsymbol = stringFromCharCode(((codePoint >> 18) & 0x07) | 0xF0);\n\t\t\tsymbol += createByte(codePoint, 12);\n\t\t\tsymbol += createByte(codePoint, 6);\n\t\t}\n\t\tsymbol += stringFromCharCode((codePoint & 0x3F) | 0x80);\n\t\treturn symbol;\n\t}\n\t\n\tfunction utf8encode(string, opts) {\n\t\topts = opts || {};\n\t\tvar strict = false !== opts.strict;\n\t\n\t\tvar codePoints = ucs2decode(string);\n\t\tvar length = codePoints.length;\n\t\tvar index = -1;\n\t\tvar codePoint;\n\t\tvar byteString = '';\n\t\twhile (++index < length) {\n\t\t\tcodePoint = codePoints[index];\n\t\t\tbyteString += encodeCodePoint(codePoint, strict);\n\t\t}\n\t\treturn byteString;\n\t}\n\t\n\t/*--------------------------------------------------------------------------*/\n\t\n\tfunction readContinuationByte() {\n\t\tif (byteIndex >= byteCount) {\n\t\t\tthrow Error('Invalid byte index');\n\t\t}\n\t\n\t\tvar continuationByte = byteArray[byteIndex] & 0xFF;\n\t\tbyteIndex++;\n\t\n\t\tif ((continuationByte & 0xC0) == 0x80) {\n\t\t\treturn continuationByte & 0x3F;\n\t\t}\n\t\n\t\t// If we end up here, it’s not a continuation byte\n\t\tthrow Error('Invalid continuation byte');\n\t}\n\t\n\tfunction decodeSymbol(strict) {\n\t\tvar byte1;\n\t\tvar byte2;\n\t\tvar byte3;\n\t\tvar byte4;\n\t\tvar codePoint;\n\t\n\t\tif (byteIndex > byteCount) {\n\t\t\tthrow Error('Invalid byte index');\n\t\t}\n\t\n\t\tif (byteIndex == byteCount) {\n\t\t\treturn false;\n\t\t}\n\t\n\t\t// Read first byte\n\t\tbyte1 = byteArray[byteIndex] & 0xFF;\n\t\tbyteIndex++;\n\t\n\t\t// 1-byte sequence (no continuation bytes)\n\t\tif ((byte1 & 0x80) == 0) {\n\t\t\treturn byte1;\n\t\t}\n\t\n\t\t// 2-byte sequence\n\t\tif ((byte1 & 0xE0) == 0xC0) {\n\t\t\tbyte2 = readContinuationByte();\n\t\t\tcodePoint = ((byte1 & 0x1F) << 6) | byte2;\n\t\t\tif (codePoint >= 0x80) {\n\t\t\t\treturn codePoint;\n\t\t\t} else {\n\t\t\t\tthrow Error('Invalid continuation byte');\n\t\t\t}\n\t\t}\n\t\n\t\t// 3-byte sequence (may include unpaired surrogates)\n\t\tif ((byte1 & 0xF0) == 0xE0) {\n\t\t\tbyte2 = readContinuationByte();\n\t\t\tbyte3 = readContinuationByte();\n\t\t\tcodePoint = ((byte1 & 0x0F) << 12) | (byte2 << 6) | byte3;\n\t\t\tif (codePoint >= 0x0800) {\n\t\t\t\treturn checkScalarValue(codePoint, strict) ? codePoint : 0xFFFD;\n\t\t\t} else {\n\t\t\t\tthrow Error('Invalid continuation byte');\n\t\t\t}\n\t\t}\n\t\n\t\t// 4-byte sequence\n\t\tif ((byte1 & 0xF8) == 0xF0) {\n\t\t\tbyte2 = readContinuationByte();\n\t\t\tbyte3 = readContinuationByte();\n\t\t\tbyte4 = readContinuationByte();\n\t\t\tcodePoint = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0C) |\n\t\t\t\t(byte3 << 0x06) | byte4;\n\t\t\tif (codePoint >= 0x010000 && codePoint <= 0x10FFFF) {\n\t\t\t\treturn codePoint;\n\t\t\t}\n\t\t}\n\t\n\t\tthrow Error('Invalid UTF-8 detected');\n\t}\n\t\n\tvar byteArray;\n\tvar byteCount;\n\tvar byteIndex;\n\tfunction utf8decode(byteString, opts) {\n\t\topts = opts || {};\n\t\tvar strict = false !== opts.strict;\n\t\n\t\tbyteArray = ucs2decode(byteString);\n\t\tbyteCount = byteArray.length;\n\t\tbyteIndex = 0;\n\t\tvar codePoints = [];\n\t\tvar tmp;\n\t\twhile ((tmp = decodeSymbol(strict)) !== false) {\n\t\t\tcodePoints.push(tmp);\n\t\t}\n\t\treturn ucs2encode(codePoints);\n\t}\n\t\n\tmodule.exports = {\n\t\tversion: '2.1.2',\n\t\tencode: utf8encode,\n\t\tdecode: utf8decode\n\t};\n\n\n/***/ }),\n/* 25 */\n/***/ (function(module, exports) {\n\n\t/*\n\t * base64-arraybuffer\n\t * https://github.com/niklasvh/base64-arraybuffer\n\t *\n\t * Copyright (c) 2012 Niklas von Hertzen\n\t * Licensed under the MIT license.\n\t */\n\t(function(chars){\n\t  \"use strict\";\n\t\n\t  exports.encode = function(arraybuffer) {\n\t    var bytes = new Uint8Array(arraybuffer),\n\t    i, len = bytes.length, base64 = \"\";\n\t\n\t    for (i = 0; i < len; i+=3) {\n\t      base64 += chars[bytes[i] >> 2];\n\t      base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n\t      base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n\t      base64 += chars[bytes[i + 2] & 63];\n\t    }\n\t\n\t    if ((len % 3) === 2) {\n\t      base64 = base64.substring(0, base64.length - 1) + \"=\";\n\t    } else if (len % 3 === 1) {\n\t      base64 = base64.substring(0, base64.length - 2) + \"==\";\n\t    }\n\t\n\t    return base64;\n\t  };\n\t\n\t  exports.decode =  function(base64) {\n\t    var bufferLength = base64.length * 0.75,\n\t    len = base64.length, i, p = 0,\n\t    encoded1, encoded2, encoded3, encoded4;\n\t\n\t    if (base64[base64.length - 1] === \"=\") {\n\t      bufferLength--;\n\t      if (base64[base64.length - 2] === \"=\") {\n\t        bufferLength--;\n\t      }\n\t    }\n\t\n\t    var arraybuffer = new ArrayBuffer(bufferLength),\n\t    bytes = new Uint8Array(arraybuffer);\n\t\n\t    for (i = 0; i < len; i+=4) {\n\t      encoded1 = chars.indexOf(base64[i]);\n\t      encoded2 = chars.indexOf(base64[i+1]);\n\t      encoded3 = chars.indexOf(base64[i+2]);\n\t      encoded4 = chars.indexOf(base64[i+3]);\n\t\n\t      bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n\t      bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n\t      bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n\t    }\n\t\n\t    return arraybuffer;\n\t  };\n\t})(\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\");\n\n\n/***/ }),\n/* 26 */\n/***/ (function(module, exports) {\n\n\t/**\r\n\t * Create a blob builder even when vendor prefixes exist\r\n\t */\r\n\t\r\n\tvar BlobBuilder = typeof BlobBuilder !== 'undefined' ? BlobBuilder :\r\n\t  typeof WebKitBlobBuilder !== 'undefined' ? WebKitBlobBuilder :\r\n\t  typeof MSBlobBuilder !== 'undefined' ? MSBlobBuilder :\r\n\t  typeof MozBlobBuilder !== 'undefined' ? MozBlobBuilder : \r\n\t  false;\r\n\t\r\n\t/**\r\n\t * Check if Blob constructor is supported\r\n\t */\r\n\t\r\n\tvar blobSupported = (function() {\r\n\t  try {\r\n\t    var a = new Blob(['hi']);\r\n\t    return a.size === 2;\r\n\t  } catch(e) {\r\n\t    return false;\r\n\t  }\r\n\t})();\r\n\t\r\n\t/**\r\n\t * Check if Blob constructor supports ArrayBufferViews\r\n\t * Fails in Safari 6, so we need to map to ArrayBuffers there.\r\n\t */\r\n\t\r\n\tvar blobSupportsArrayBufferView = blobSupported && (function() {\r\n\t  try {\r\n\t    var b = new Blob([new Uint8Array([1,2])]);\r\n\t    return b.size === 2;\r\n\t  } catch(e) {\r\n\t    return false;\r\n\t  }\r\n\t})();\r\n\t\r\n\t/**\r\n\t * Check if BlobBuilder is supported\r\n\t */\r\n\t\r\n\tvar blobBuilderSupported = BlobBuilder\r\n\t  && BlobBuilder.prototype.append\r\n\t  && BlobBuilder.prototype.getBlob;\r\n\t\r\n\t/**\r\n\t * Helper function that maps ArrayBufferViews to ArrayBuffers\r\n\t * Used by BlobBuilder constructor and old browsers that didn't\r\n\t * support it in the Blob constructor.\r\n\t */\r\n\t\r\n\tfunction mapArrayBufferViews(ary) {\r\n\t  return ary.map(function(chunk) {\r\n\t    if (chunk.buffer instanceof ArrayBuffer) {\r\n\t      var buf = chunk.buffer;\r\n\t\r\n\t      // if this is a subarray, make a copy so we only\r\n\t      // include the subarray region from the underlying buffer\r\n\t      if (chunk.byteLength !== buf.byteLength) {\r\n\t        var copy = new Uint8Array(chunk.byteLength);\r\n\t        copy.set(new Uint8Array(buf, chunk.byteOffset, chunk.byteLength));\r\n\t        buf = copy.buffer;\r\n\t      }\r\n\t\r\n\t      return buf;\r\n\t    }\r\n\t\r\n\t    return chunk;\r\n\t  });\r\n\t}\r\n\t\r\n\tfunction BlobBuilderConstructor(ary, options) {\r\n\t  options = options || {};\r\n\t\r\n\t  var bb = new BlobBuilder();\r\n\t  mapArrayBufferViews(ary).forEach(function(part) {\r\n\t    bb.append(part);\r\n\t  });\r\n\t\r\n\t  return (options.type) ? bb.getBlob(options.type) : bb.getBlob();\r\n\t};\r\n\t\r\n\tfunction BlobConstructor(ary, options) {\r\n\t  return new Blob(mapArrayBufferViews(ary), options || {});\r\n\t};\r\n\t\r\n\tif (typeof Blob !== 'undefined') {\r\n\t  BlobBuilderConstructor.prototype = Blob.prototype;\r\n\t  BlobConstructor.prototype = Blob.prototype;\r\n\t}\r\n\t\r\n\tmodule.exports = (function() {\r\n\t  if (blobSupported) {\r\n\t    return blobSupportsArrayBufferView ? Blob : BlobConstructor;\r\n\t  } else if (blobBuilderSupported) {\r\n\t    return BlobBuilderConstructor;\r\n\t  } else {\r\n\t    return undefined;\r\n\t  }\r\n\t})();\r\n\n\n/***/ }),\n/* 27 */\n/***/ (function(module, exports) {\n\n\t/**\n\t * Compiles a querystring\n\t * Returns string representation of the object\n\t *\n\t * @param {Object}\n\t * @api private\n\t */\n\t\n\texports.encode = function (obj) {\n\t  var str = '';\n\t\n\t  for (var i in obj) {\n\t    if (obj.hasOwnProperty(i)) {\n\t      if (str.length) str += '&';\n\t      str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n\t    }\n\t  }\n\t\n\t  return str;\n\t};\n\t\n\t/**\n\t * Parses a simple querystring into an object\n\t *\n\t * @param {String} qs\n\t * @api private\n\t */\n\t\n\texports.decode = function(qs){\n\t  var qry = {};\n\t  var pairs = qs.split('&');\n\t  for (var i = 0, l = pairs.length; i < l; i++) {\n\t    var pair = pairs[i].split('=');\n\t    qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n\t  }\n\t  return qry;\n\t};\n\n\n/***/ }),\n/* 28 */\n/***/ (function(module, exports) {\n\n\t\n\tmodule.exports = function(a, b){\n\t  var fn = function(){};\n\t  fn.prototype = b.prototype;\n\t  a.prototype = new fn;\n\t  a.prototype.constructor = a;\n\t};\n\n/***/ }),\n/* 29 */\n/***/ (function(module, exports) {\n\n\t'use strict';\n\t\n\tvar alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split('')\n\t  , length = 64\n\t  , map = {}\n\t  , seed = 0\n\t  , i = 0\n\t  , prev;\n\t\n\t/**\n\t * Return a string representing the specified number.\n\t *\n\t * @param {Number} num The number to convert.\n\t * @returns {String} The string representation of the number.\n\t * @api public\n\t */\n\tfunction encode(num) {\n\t  var encoded = '';\n\t\n\t  do {\n\t    encoded = alphabet[num % length] + encoded;\n\t    num = Math.floor(num / length);\n\t  } while (num > 0);\n\t\n\t  return encoded;\n\t}\n\t\n\t/**\n\t * Return the integer value specified by the given string.\n\t *\n\t * @param {String} str The string to convert.\n\t * @returns {Number} The integer value represented by the string.\n\t * @api public\n\t */\n\tfunction decode(str) {\n\t  var decoded = 0;\n\t\n\t  for (i = 0; i < str.length; i++) {\n\t    decoded = decoded * length + map[str.charAt(i)];\n\t  }\n\t\n\t  return decoded;\n\t}\n\t\n\t/**\n\t * Yeast: A tiny growing id generator.\n\t *\n\t * @returns {String} A unique id.\n\t * @api public\n\t */\n\tfunction yeast() {\n\t  var now = encode(+new Date());\n\t\n\t  if (now !== prev) return seed = 0, prev = now;\n\t  return now +'.'+ encode(seed++);\n\t}\n\t\n\t//\n\t// Map each character to its index.\n\t//\n\tfor (; i < length; i++) map[alphabet[i]] = i;\n\t\n\t//\n\t// Expose the `yeast`, `encode` and `decode` functions.\n\t//\n\tyeast.encode = encode;\n\tyeast.decode = decode;\n\tmodule.exports = yeast;\n\n\n/***/ }),\n/* 30 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module requirements.\n\t */\n\t\n\tvar Polling = __webpack_require__(17);\n\tvar inherit = __webpack_require__(28);\n\tvar globalThis = __webpack_require__(15);\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = JSONPPolling;\n\t\n\t/**\n\t * Cached regular expressions.\n\t */\n\t\n\tvar rNewline = /\\n/g;\n\tvar rEscapedNewline = /\\\\n/g;\n\t\n\t/**\n\t * Global JSONP callbacks.\n\t */\n\t\n\tvar callbacks;\n\t\n\t/**\n\t * Noop.\n\t */\n\t\n\tfunction empty () { }\n\t\n\t/**\n\t * JSONP Polling constructor.\n\t *\n\t * @param {Object} opts.\n\t * @api public\n\t */\n\t\n\tfunction JSONPPolling (opts) {\n\t  Polling.call(this, opts);\n\t\n\t  this.query = this.query || {};\n\t\n\t  // define global callbacks array if not present\n\t  // we do this here (lazily) to avoid unneeded global pollution\n\t  if (!callbacks) {\n\t    // we need to consider multiple engines in the same page\n\t    callbacks = globalThis.___eio = (globalThis.___eio || []);\n\t  }\n\t\n\t  // callback identifier\n\t  this.index = callbacks.length;\n\t\n\t  // add callback to jsonp global\n\t  var self = this;\n\t  callbacks.push(function (msg) {\n\t    self.onData(msg);\n\t  });\n\t\n\t  // append to query string\n\t  this.query.j = this.index;\n\t\n\t  // prevent spurious errors from being emitted when the window is unloaded\n\t  if (typeof addEventListener === 'function') {\n\t    addEventListener('beforeunload', function () {\n\t      if (self.script) self.script.onerror = empty;\n\t    }, false);\n\t  }\n\t}\n\t\n\t/**\n\t * Inherits from Polling.\n\t */\n\t\n\tinherit(JSONPPolling, Polling);\n\t\n\t/*\n\t * JSONP only supports binary as base64 encoded strings\n\t */\n\t\n\tJSONPPolling.prototype.supportsBinary = false;\n\t\n\t/**\n\t * Closes the socket.\n\t *\n\t * @api private\n\t */\n\t\n\tJSONPPolling.prototype.doClose = function () {\n\t  if (this.script) {\n\t    this.script.parentNode.removeChild(this.script);\n\t    this.script = null;\n\t  }\n\t\n\t  if (this.form) {\n\t    this.form.parentNode.removeChild(this.form);\n\t    this.form = null;\n\t    this.iframe = null;\n\t  }\n\t\n\t  Polling.prototype.doClose.call(this);\n\t};\n\t\n\t/**\n\t * Starts a poll cycle.\n\t *\n\t * @api private\n\t */\n\t\n\tJSONPPolling.prototype.doPoll = function () {\n\t  var self = this;\n\t  var script = document.createElement('script');\n\t\n\t  if (this.script) {\n\t    this.script.parentNode.removeChild(this.script);\n\t    this.script = null;\n\t  }\n\t\n\t  script.async = true;\n\t  script.src = this.uri();\n\t  script.onerror = function (e) {\n\t    self.onError('jsonp poll error', e);\n\t  };\n\t\n\t  var insertAt = document.getElementsByTagName('script')[0];\n\t  if (insertAt) {\n\t    insertAt.parentNode.insertBefore(script, insertAt);\n\t  } else {\n\t    (document.head || document.body).appendChild(script);\n\t  }\n\t  this.script = script;\n\t\n\t  var isUAgecko = 'undefined' !== typeof navigator && /gecko/i.test(navigator.userAgent);\n\t\n\t  if (isUAgecko) {\n\t    setTimeout(function () {\n\t      var iframe = document.createElement('iframe');\n\t      document.body.appendChild(iframe);\n\t      document.body.removeChild(iframe);\n\t    }, 100);\n\t  }\n\t};\n\t\n\t/**\n\t * Writes with a hidden iframe.\n\t *\n\t * @param {String} data to send\n\t * @param {Function} called upon flush.\n\t * @api private\n\t */\n\t\n\tJSONPPolling.prototype.doWrite = function (data, fn) {\n\t  var self = this;\n\t\n\t  if (!this.form) {\n\t    var form = document.createElement('form');\n\t    var area = document.createElement('textarea');\n\t    var id = this.iframeId = 'eio_iframe_' + this.index;\n\t    var iframe;\n\t\n\t    form.className = 'socketio';\n\t    form.style.position = 'absolute';\n\t    form.style.top = '-1000px';\n\t    form.style.left = '-1000px';\n\t    form.target = id;\n\t    form.method = 'POST';\n\t    form.setAttribute('accept-charset', 'utf-8');\n\t    area.name = 'd';\n\t    form.appendChild(area);\n\t    document.body.appendChild(form);\n\t\n\t    this.form = form;\n\t    this.area = area;\n\t  }\n\t\n\t  this.form.action = this.uri();\n\t\n\t  function complete () {\n\t    initIframe();\n\t    fn();\n\t  }\n\t\n\t  function initIframe () {\n\t    if (self.iframe) {\n\t      try {\n\t        self.form.removeChild(self.iframe);\n\t      } catch (e) {\n\t        self.onError('jsonp polling iframe removal error', e);\n\t      }\n\t    }\n\t\n\t    try {\n\t      // ie6 dynamic iframes with target=\"\" support (thanks Chris Lambacher)\n\t      var html = '<iframe src=\"javascript:0\" name=\"' + self.iframeId + '\">';\n\t      iframe = document.createElement(html);\n\t    } catch (e) {\n\t      iframe = document.createElement('iframe');\n\t      iframe.name = self.iframeId;\n\t      iframe.src = 'javascript:0';\n\t    }\n\t\n\t    iframe.id = self.iframeId;\n\t\n\t    self.form.appendChild(iframe);\n\t    self.iframe = iframe;\n\t  }\n\t\n\t  initIframe();\n\t\n\t  // escape \\n to prevent it from being converted into \\r\\n by some UAs\n\t  // double escaping is required for escaped new lines because unescaping of new lines can be done safely on server-side\n\t  data = data.replace(rEscapedNewline, '\\\\\\n');\n\t  this.area.value = data.replace(rNewline, '\\\\n');\n\t\n\t  try {\n\t    this.form.submit();\n\t  } catch (e) {}\n\t\n\t  if (this.iframe.attachEvent) {\n\t    this.iframe.onreadystatechange = function () {\n\t      if (self.iframe.readyState === 'complete') {\n\t        complete();\n\t      }\n\t    };\n\t  } else {\n\t    this.iframe.onload = complete;\n\t  }\n\t};\n\n\n/***/ }),\n/* 31 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar Transport = __webpack_require__(18);\n\tvar parser = __webpack_require__(19);\n\tvar parseqs = __webpack_require__(27);\n\tvar inherit = __webpack_require__(28);\n\tvar yeast = __webpack_require__(29);\n\tvar debug = __webpack_require__(3)('engine.io-client:websocket');\n\t\n\tvar BrowserWebSocket, NodeWebSocket;\n\t\n\tif (typeof WebSocket !== 'undefined') {\n\t  BrowserWebSocket = WebSocket;\n\t} else if (typeof self !== 'undefined') {\n\t  BrowserWebSocket = self.WebSocket || self.MozWebSocket;\n\t}\n\t\n\tif (typeof window === 'undefined') {\n\t  try {\n\t    NodeWebSocket = __webpack_require__(32);\n\t  } catch (e) { }\n\t}\n\t\n\t/**\n\t * Get either the `WebSocket` or `MozWebSocket` globals\n\t * in the browser or try to resolve WebSocket-compatible\n\t * interface exposed by `ws` for Node-like environment.\n\t */\n\t\n\tvar WebSocketImpl = BrowserWebSocket || NodeWebSocket;\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = WS;\n\t\n\t/**\n\t * WebSocket transport constructor.\n\t *\n\t * @api {Object} connection options\n\t * @api public\n\t */\n\t\n\tfunction WS (opts) {\n\t  var forceBase64 = (opts && opts.forceBase64);\n\t  if (forceBase64) {\n\t    this.supportsBinary = false;\n\t  }\n\t  this.perMessageDeflate = opts.perMessageDeflate;\n\t  this.usingBrowserWebSocket = BrowserWebSocket && !opts.forceNode;\n\t  this.protocols = opts.protocols;\n\t  if (!this.usingBrowserWebSocket) {\n\t    WebSocketImpl = NodeWebSocket;\n\t  }\n\t  Transport.call(this, opts);\n\t}\n\t\n\t/**\n\t * Inherits from Transport.\n\t */\n\t\n\tinherit(WS, Transport);\n\t\n\t/**\n\t * Transport name.\n\t *\n\t * @api public\n\t */\n\t\n\tWS.prototype.name = 'websocket';\n\t\n\t/*\n\t * WebSockets support binary\n\t */\n\t\n\tWS.prototype.supportsBinary = true;\n\t\n\t/**\n\t * Opens socket.\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.doOpen = function () {\n\t  if (!this.check()) {\n\t    // let probe timeout\n\t    return;\n\t  }\n\t\n\t  var uri = this.uri();\n\t  var protocols = this.protocols;\n\t\n\t  var opts = {};\n\t\n\t  if (!this.isReactNative) {\n\t    opts.agent = this.agent;\n\t    opts.perMessageDeflate = this.perMessageDeflate;\n\t\n\t    // SSL options for Node.js client\n\t    opts.pfx = this.pfx;\n\t    opts.key = this.key;\n\t    opts.passphrase = this.passphrase;\n\t    opts.cert = this.cert;\n\t    opts.ca = this.ca;\n\t    opts.ciphers = this.ciphers;\n\t    opts.rejectUnauthorized = this.rejectUnauthorized;\n\t  }\n\t\n\t  if (this.extraHeaders) {\n\t    opts.headers = this.extraHeaders;\n\t  }\n\t  if (this.localAddress) {\n\t    opts.localAddress = this.localAddress;\n\t  }\n\t\n\t  try {\n\t    this.ws =\n\t      this.usingBrowserWebSocket && !this.isReactNative\n\t        ? protocols\n\t          ? new WebSocketImpl(uri, protocols)\n\t          : new WebSocketImpl(uri)\n\t        : new WebSocketImpl(uri, protocols, opts);\n\t  } catch (err) {\n\t    return this.emit('error', err);\n\t  }\n\t\n\t  if (this.ws.binaryType === undefined) {\n\t    this.supportsBinary = false;\n\t  }\n\t\n\t  if (this.ws.supports && this.ws.supports.binary) {\n\t    this.supportsBinary = true;\n\t    this.ws.binaryType = 'nodebuffer';\n\t  } else {\n\t    this.ws.binaryType = 'arraybuffer';\n\t  }\n\t\n\t  this.addEventListeners();\n\t};\n\t\n\t/**\n\t * Adds event listeners to the socket\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.addEventListeners = function () {\n\t  var self = this;\n\t\n\t  this.ws.onopen = function () {\n\t    self.onOpen();\n\t  };\n\t  this.ws.onclose = function () {\n\t    self.onClose();\n\t  };\n\t  this.ws.onmessage = function (ev) {\n\t    self.onData(ev.data);\n\t  };\n\t  this.ws.onerror = function (e) {\n\t    self.onError('websocket error', e);\n\t  };\n\t};\n\t\n\t/**\n\t * Writes data to socket.\n\t *\n\t * @param {Array} array of packets.\n\t * @api private\n\t */\n\t\n\tWS.prototype.write = function (packets) {\n\t  var self = this;\n\t  this.writable = false;\n\t\n\t  // encodePacket efficient as it uses WS framing\n\t  // no need for encodePayload\n\t  var total = packets.length;\n\t  for (var i = 0, l = total; i < l; i++) {\n\t    (function (packet) {\n\t      parser.encodePacket(packet, self.supportsBinary, function (data) {\n\t        if (!self.usingBrowserWebSocket) {\n\t          // always create a new object (GH-437)\n\t          var opts = {};\n\t          if (packet.options) {\n\t            opts.compress = packet.options.compress;\n\t          }\n\t\n\t          if (self.perMessageDeflate) {\n\t            var len = 'string' === typeof data ? Buffer.byteLength(data) : data.length;\n\t            if (len < self.perMessageDeflate.threshold) {\n\t              opts.compress = false;\n\t            }\n\t          }\n\t        }\n\t\n\t        // Sometimes the websocket has already been closed but the browser didn't\n\t        // have a chance of informing us about it yet, in that case send will\n\t        // throw an error\n\t        try {\n\t          if (self.usingBrowserWebSocket) {\n\t            // TypeError is thrown when passing the second argument on Safari\n\t            self.ws.send(data);\n\t          } else {\n\t            self.ws.send(data, opts);\n\t          }\n\t        } catch (e) {\n\t\n\t        }\n\t\n\t        --total || done();\n\t      });\n\t    })(packets[i]);\n\t  }\n\t\n\t  function done () {\n\t    self.emit('flush');\n\t\n\t    // fake drain\n\t    // defer to next tick to allow Socket to clear writeBuffer\n\t    setTimeout(function () {\n\t      self.writable = true;\n\t      self.emit('drain');\n\t    }, 0);\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon close\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.onClose = function () {\n\t  Transport.prototype.onClose.call(this);\n\t};\n\t\n\t/**\n\t * Closes socket.\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.doClose = function () {\n\t  if (typeof this.ws !== 'undefined') {\n\t    this.ws.close();\n\t  }\n\t};\n\t\n\t/**\n\t * Generates uri for connection.\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.uri = function () {\n\t  var query = this.query || {};\n\t  var schema = this.secure ? 'wss' : 'ws';\n\t  var port = '';\n\t\n\t  // avoid port if default for schema\n\t  if (this.port && (('wss' === schema && Number(this.port) !== 443) ||\n\t    ('ws' === schema && Number(this.port) !== 80))) {\n\t    port = ':' + this.port;\n\t  }\n\t\n\t  // append timestamp to URI\n\t  if (this.timestampRequests) {\n\t    query[this.timestampParam] = yeast();\n\t  }\n\t\n\t  // communicate binary support capabilities\n\t  if (!this.supportsBinary) {\n\t    query.b64 = 1;\n\t  }\n\t\n\t  query = parseqs.encode(query);\n\t\n\t  // prepend ? to query\n\t  if (query.length) {\n\t    query = '?' + query;\n\t  }\n\t\n\t  var ipv6 = this.hostname.indexOf(':') !== -1;\n\t  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n\t};\n\t\n\t/**\n\t * Feature detection for WebSocket.\n\t *\n\t * @return {Boolean} whether this transport is available.\n\t * @api public\n\t */\n\t\n\tWS.prototype.check = function () {\n\t  return !!WebSocketImpl && !('__initialize' in WebSocketImpl && this.name === WS.prototype.name);\n\t};\n\n\n/***/ }),\n/* 32 */\n/***/ (function(module, exports) {\n\n\t/* (ignored) */\n\n/***/ }),\n/* 33 */\n/***/ (function(module, exports) {\n\n\t\n\tvar indexOf = [].indexOf;\n\t\n\tmodule.exports = function(arr, obj){\n\t  if (indexOf) return arr.indexOf(obj);\n\t  for (var i = 0; i < arr.length; ++i) {\n\t    if (arr[i] === obj) return i;\n\t  }\n\t  return -1;\n\t};\n\n/***/ }),\n/* 34 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t'use strict';\n\t\n\tvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar parser = __webpack_require__(4);\n\tvar Emitter = __webpack_require__(5);\n\tvar toArray = __webpack_require__(35);\n\tvar on = __webpack_require__(36);\n\tvar bind = __webpack_require__(37);\n\tvar debug = __webpack_require__(3)('socket.io-client:socket');\n\tvar parseqs = __webpack_require__(27);\n\tvar hasBin = __webpack_require__(21);\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = exports = Socket;\n\t\n\t/**\n\t * Internal events (blacklisted).\n\t * These events can't be emitted by the user.\n\t *\n\t * @api private\n\t */\n\t\n\tvar events = {\n\t  connect: 1,\n\t  connect_error: 1,\n\t  connect_timeout: 1,\n\t  connecting: 1,\n\t  disconnect: 1,\n\t  error: 1,\n\t  reconnect: 1,\n\t  reconnect_attempt: 1,\n\t  reconnect_failed: 1,\n\t  reconnect_error: 1,\n\t  reconnecting: 1,\n\t  ping: 1,\n\t  pong: 1\n\t};\n\t\n\t/**\n\t * Shortcut to `Emitter#emit`.\n\t */\n\t\n\tvar emit = Emitter.prototype.emit;\n\t\n\t/**\n\t * `Socket` constructor.\n\t *\n\t * @api public\n\t */\n\t\n\tfunction Socket(io, nsp, opts) {\n\t  this.io = io;\n\t  this.nsp = nsp;\n\t  this.json = this; // compat\n\t  this.ids = 0;\n\t  this.acks = {};\n\t  this.receiveBuffer = [];\n\t  this.sendBuffer = [];\n\t  this.connected = false;\n\t  this.disconnected = true;\n\t  this.flags = {};\n\t  if (opts && opts.query) {\n\t    this.query = opts.query;\n\t  }\n\t  if (this.io.autoConnect) this.open();\n\t}\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Socket.prototype);\n\t\n\t/**\n\t * Subscribe to open, close and packet events\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.subEvents = function () {\n\t  if (this.subs) return;\n\t\n\t  var io = this.io;\n\t  this.subs = [on(io, 'open', bind(this, 'onopen')), on(io, 'packet', bind(this, 'onpacket')), on(io, 'close', bind(this, 'onclose'))];\n\t};\n\t\n\t/**\n\t * \"Opens\" the socket.\n\t *\n\t * @api public\n\t */\n\t\n\tSocket.prototype.open = Socket.prototype.connect = function () {\n\t  if (this.connected) return this;\n\t\n\t  this.subEvents();\n\t  if (!this.io.reconnecting) this.io.open(); // ensure open\n\t  if ('open' === this.io.readyState) this.onopen();\n\t  this.emit('connecting');\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sends a `message` event.\n\t *\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.send = function () {\n\t  var args = toArray(arguments);\n\t  args.unshift('message');\n\t  this.emit.apply(this, args);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Override `emit`.\n\t * If the event is in `events`, it's emitted normally.\n\t *\n\t * @param {String} event name\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.emit = function (ev) {\n\t  if (events.hasOwnProperty(ev)) {\n\t    emit.apply(this, arguments);\n\t    return this;\n\t  }\n\t\n\t  var args = toArray(arguments);\n\t  var packet = {\n\t    type: (this.flags.binary !== undefined ? this.flags.binary : hasBin(args)) ? parser.BINARY_EVENT : parser.EVENT,\n\t    data: args\n\t  };\n\t\n\t  packet.options = {};\n\t  packet.options.compress = !this.flags || false !== this.flags.compress;\n\t\n\t  // event ack callback\n\t  if ('function' === typeof args[args.length - 1]) {\n\t\n\t    this.acks[this.ids] = args.pop();\n\t    packet.id = this.ids++;\n\t  }\n\t\n\t  if (this.connected) {\n\t    this.packet(packet);\n\t  } else {\n\t    this.sendBuffer.push(packet);\n\t  }\n\t\n\t  this.flags = {};\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sends a packet.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tSocket.prototype.packet = function (packet) {\n\t  packet.nsp = this.nsp;\n\t  this.io.packet(packet);\n\t};\n\t\n\t/**\n\t * Called upon engine `open`.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onopen = function () {\n\t\n\t  // write connect packet if necessary\n\t  if ('/' !== this.nsp) {\n\t    if (this.query) {\n\t      var query = _typeof(this.query) === 'object' ? parseqs.encode(this.query) : this.query;\n\t\n\t      this.packet({ type: parser.CONNECT, query: query });\n\t    } else {\n\t      this.packet({ type: parser.CONNECT });\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon engine `close`.\n\t *\n\t * @param {String} reason\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onclose = function (reason) {\n\t\n\t  this.connected = false;\n\t  this.disconnected = true;\n\t  delete this.id;\n\t  this.emit('disconnect', reason);\n\t};\n\t\n\t/**\n\t * Called with socket packet.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onpacket = function (packet) {\n\t  var sameNamespace = packet.nsp === this.nsp;\n\t  var rootNamespaceError = packet.type === parser.ERROR && packet.nsp === '/';\n\t\n\t  if (!sameNamespace && !rootNamespaceError) return;\n\t\n\t  switch (packet.type) {\n\t    case parser.CONNECT:\n\t      this.onconnect();\n\t      break;\n\t\n\t    case parser.EVENT:\n\t      this.onevent(packet);\n\t      break;\n\t\n\t    case parser.BINARY_EVENT:\n\t      this.onevent(packet);\n\t      break;\n\t\n\t    case parser.ACK:\n\t      this.onack(packet);\n\t      break;\n\t\n\t    case parser.BINARY_ACK:\n\t      this.onack(packet);\n\t      break;\n\t\n\t    case parser.DISCONNECT:\n\t      this.ondisconnect();\n\t      break;\n\t\n\t    case parser.ERROR:\n\t      this.emit('error', packet.data);\n\t      break;\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon a server event.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onevent = function (packet) {\n\t  var args = packet.data || [];\n\t\n\t  if (null != packet.id) {\n\t\n\t    args.push(this.ack(packet.id));\n\t  }\n\t\n\t  if (this.connected) {\n\t    emit.apply(this, args);\n\t  } else {\n\t    this.receiveBuffer.push(args);\n\t  }\n\t};\n\t\n\t/**\n\t * Produces an ack callback to emit with an event.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.ack = function (id) {\n\t  var self = this;\n\t  var sent = false;\n\t  return function () {\n\t    // prevent double callbacks\n\t    if (sent) return;\n\t    sent = true;\n\t    var args = toArray(arguments);\n\t\n\t    self.packet({\n\t      type: hasBin(args) ? parser.BINARY_ACK : parser.ACK,\n\t      id: id,\n\t      data: args\n\t    });\n\t  };\n\t};\n\t\n\t/**\n\t * Called upon a server acknowlegement.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onack = function (packet) {\n\t  var ack = this.acks[packet.id];\n\t  if ('function' === typeof ack) {\n\t\n\t    ack.apply(this, packet.data);\n\t    delete this.acks[packet.id];\n\t  } else {}\n\t};\n\t\n\t/**\n\t * Called upon server connect.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onconnect = function () {\n\t  this.connected = true;\n\t  this.disconnected = false;\n\t  this.emitBuffered();\n\t  this.emit('connect');\n\t};\n\t\n\t/**\n\t * Emit buffered events (received and emitted).\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.emitBuffered = function () {\n\t  var i;\n\t  for (i = 0; i < this.receiveBuffer.length; i++) {\n\t    emit.apply(this, this.receiveBuffer[i]);\n\t  }\n\t  this.receiveBuffer = [];\n\t\n\t  for (i = 0; i < this.sendBuffer.length; i++) {\n\t    this.packet(this.sendBuffer[i]);\n\t  }\n\t  this.sendBuffer = [];\n\t};\n\t\n\t/**\n\t * Called upon server disconnect.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.ondisconnect = function () {\n\t\n\t  this.destroy();\n\t  this.onclose('io server disconnect');\n\t};\n\t\n\t/**\n\t * Called upon forced client/server side disconnections,\n\t * this method ensures the manager stops tracking us and\n\t * that reconnections don't get triggered for this.\n\t *\n\t * @api private.\n\t */\n\t\n\tSocket.prototype.destroy = function () {\n\t  if (this.subs) {\n\t    // clean subscriptions to avoid reconnections\n\t    for (var i = 0; i < this.subs.length; i++) {\n\t      this.subs[i].destroy();\n\t    }\n\t    this.subs = null;\n\t  }\n\t\n\t  this.io.destroy(this);\n\t};\n\t\n\t/**\n\t * Disconnects the socket manually.\n\t *\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.close = Socket.prototype.disconnect = function () {\n\t  if (this.connected) {\n\t\n\t    this.packet({ type: parser.DISCONNECT });\n\t  }\n\t\n\t  // remove socket from pool\n\t  this.destroy();\n\t\n\t  if (this.connected) {\n\t    // fire events\n\t    this.onclose('io client disconnect');\n\t  }\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the compress flag.\n\t *\n\t * @param {Boolean} if `true`, compresses the sending data\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.compress = function (compress) {\n\t  this.flags.compress = compress;\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the binary flag\n\t *\n\t * @param {Boolean} whether the emitted data contains binary\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.binary = function (binary) {\n\t  this.flags.binary = binary;\n\t  return this;\n\t};\n\n/***/ }),\n/* 35 */\n/***/ (function(module, exports) {\n\n\tmodule.exports = toArray\n\t\n\tfunction toArray(list, index) {\n\t    var array = []\n\t\n\t    index = index || 0\n\t\n\t    for (var i = index || 0; i < list.length; i++) {\n\t        array[i - index] = list[i]\n\t    }\n\t\n\t    return array\n\t}\n\n\n/***/ }),\n/* 36 */\n/***/ (function(module, exports) {\n\n\t\"use strict\";\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = on;\n\t\n\t/**\n\t * Helper for subscriptions.\n\t *\n\t * @param {Object|EventEmitter} obj with `Emitter` mixin or `EventEmitter`\n\t * @param {String} event name\n\t * @param {Function} callback\n\t * @api public\n\t */\n\t\n\tfunction on(obj, ev, fn) {\n\t  obj.on(ev, fn);\n\t  return {\n\t    destroy: function destroy() {\n\t      obj.removeListener(ev, fn);\n\t    }\n\t  };\n\t}\n\n/***/ }),\n/* 37 */\n/***/ (function(module, exports) {\n\n\t/**\n\t * Slice reference.\n\t */\n\t\n\tvar slice = [].slice;\n\t\n\t/**\n\t * Bind `obj` to `fn`.\n\t *\n\t * @param {Object} obj\n\t * @param {Function|String} fn or string\n\t * @return {Function}\n\t * @api public\n\t */\n\t\n\tmodule.exports = function(obj, fn){\n\t  if ('string' == typeof fn) fn = obj[fn];\n\t  if ('function' != typeof fn) throw new Error('bind() requires a function');\n\t  var args = slice.call(arguments, 2);\n\t  return function(){\n\t    return fn.apply(obj, args.concat(slice.call(arguments)));\n\t  }\n\t};\n\n\n/***/ }),\n/* 38 */\n/***/ (function(module, exports) {\n\n\t\n\t/**\n\t * Expose `Backoff`.\n\t */\n\t\n\tmodule.exports = Backoff;\n\t\n\t/**\n\t * Initialize backoff timer with `opts`.\n\t *\n\t * - `min` initial timeout in milliseconds [100]\n\t * - `max` max timeout [10000]\n\t * - `jitter` [0]\n\t * - `factor` [2]\n\t *\n\t * @param {Object} opts\n\t * @api public\n\t */\n\t\n\tfunction Backoff(opts) {\n\t  opts = opts || {};\n\t  this.ms = opts.min || 100;\n\t  this.max = opts.max || 10000;\n\t  this.factor = opts.factor || 2;\n\t  this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n\t  this.attempts = 0;\n\t}\n\t\n\t/**\n\t * Return the backoff duration.\n\t *\n\t * @return {Number}\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.duration = function(){\n\t  var ms = this.ms * Math.pow(this.factor, this.attempts++);\n\t  if (this.jitter) {\n\t    var rand =  Math.random();\n\t    var deviation = Math.floor(rand * this.jitter * ms);\n\t    ms = (Math.floor(rand * 10) & 1) == 0  ? ms - deviation : ms + deviation;\n\t  }\n\t  return Math.min(ms, this.max) | 0;\n\t};\n\t\n\t/**\n\t * Reset the number of attempts.\n\t *\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.reset = function(){\n\t  this.attempts = 0;\n\t};\n\t\n\t/**\n\t * Set the minimum duration\n\t *\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.setMin = function(min){\n\t  this.ms = min;\n\t};\n\t\n\t/**\n\t * Set the maximum duration\n\t *\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.setMax = function(max){\n\t  this.max = max;\n\t};\n\t\n\t/**\n\t * Set the jitter\n\t *\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.setJitter = function(jitter){\n\t  this.jitter = jitter;\n\t};\n\t\n\n\n/***/ })\n/******/ ])\n});\n;\n\n\n// WEBPACK FOOTER //\n// socket.io.slim.js", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId])\n \t\t\treturn installedModules[moduleId].exports;\n\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\texports: {},\n \t\t\tid: moduleId,\n \t\t\tloaded: false\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.loaded = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(0);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap 7863bcd206341bc7952a", "\n/**\n * Module dependencies.\n */\n\nvar url = require('./url');\nvar parser = require('socket.io-parser');\nvar Manager = require('./manager');\nvar debug = require('debug')('socket.io-client');\n\n/**\n * Module exports.\n */\n\nmodule.exports = exports = lookup;\n\n/**\n * Managers cache.\n */\n\nvar cache = exports.managers = {};\n\n/**\n * Looks up an existing `Manager` for multiplexing.\n * If the user summons:\n *\n *   `io('http://localhost/a');`\n *   `io('http://localhost/b');`\n *\n * We reuse the existing instance based on same scheme/port/host,\n * and we initialize sockets for each namespace.\n *\n * @api public\n */\n\nfunction lookup (uri, opts) {\n  if (typeof uri === 'object') {\n    opts = uri;\n    uri = undefined;\n  }\n\n  opts = opts || {};\n\n  var parsed = url(uri);\n  var source = parsed.source;\n  var id = parsed.id;\n  var path = parsed.path;\n  var sameNamespace = cache[id] && path in cache[id].nsps;\n  var newConnection = opts.forceNew || opts['force new connection'] ||\n                      false === opts.multiplex || sameNamespace;\n\n  var io;\n\n  if (newConnection) {\n\n    io = Manager(source, opts);\n  } else {\n    if (!cache[id]) {\n\n      cache[id] = Manager(source, opts);\n    }\n    io = cache[id];\n  }\n  if (parsed.query && !opts.query) {\n    opts.query = parsed.query;\n  }\n  return io.socket(parsed.path, opts);\n}\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nexports.protocol = parser.protocol;\n\n/**\n * `connect`.\n *\n * @param {String} uri\n * @api public\n */\n\nexports.connect = lookup;\n\n/**\n * Expose constructors for standalone build.\n *\n * @api public\n */\n\nexports.Manager = require('./manager');\nexports.Socket = require('./socket');\n\n\n\n// WEBPACK FOOTER //\n// ./lib/index.js", "\n/**\n * Module dependencies.\n */\n\nvar parseuri = require('parseuri');\nvar debug = require('debug')('socket.io-client:url');\n\n/**\n * Module exports.\n */\n\nmodule.exports = url;\n\n/**\n * URL parser.\n *\n * @param {String} url\n * @param {Object} An object meant to mimic window.location.\n *                 Defaults to window.location.\n * @api public\n */\n\nfunction url (uri, loc) {\n  var obj = uri;\n\n  // default to window.location\n  loc = loc || (typeof location !== 'undefined' && location);\n  if (null == uri) uri = loc.protocol + '//' + loc.host;\n\n  // relative path support\n  if ('string' === typeof uri) {\n    if ('/' === uri.charAt(0)) {\n      if ('/' === uri.charAt(1)) {\n        uri = loc.protocol + uri;\n      } else {\n        uri = loc.host + uri;\n      }\n    }\n\n    if (!/^(https?|wss?):\\/\\//.test(uri)) {\n\n      if ('undefined' !== typeof loc) {\n        uri = loc.protocol + '//' + uri;\n      } else {\n        uri = 'https://' + uri;\n      }\n    }\n\n    // parse\n\n    obj = parseuri(uri);\n  }\n\n  // make sure we treat `localhost:80` and `localhost` equally\n  if (!obj.port) {\n    if (/^(http|ws)$/.test(obj.protocol)) {\n      obj.port = '80';\n    } else if (/^(http|ws)s$/.test(obj.protocol)) {\n      obj.port = '443';\n    }\n  }\n\n  obj.path = obj.path || '/';\n\n  var ipv6 = obj.host.indexOf(':') !== -1;\n  var host = ipv6 ? '[' + obj.host + ']' : obj.host;\n\n  // define unique id\n  obj.id = obj.protocol + '://' + host + ':' + obj.port;\n  // define href\n  obj.href = obj.protocol + '://' + host + (loc && loc.port === obj.port ? '' : (':' + obj.port));\n\n  return obj;\n}\n\n\n\n// WEBPACK FOOTER //\n// ./lib/url.js", "/**\n * Parses an URI\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\n\nvar re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\n\nvar parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\n\nmodule.exports = function parseuri(str) {\n    var src = str,\n        b = str.indexOf('['),\n        e = str.indexOf(']');\n\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n\n    var m = re.exec(str || ''),\n        uri = {},\n        i = 14;\n\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n\n    return uri;\n};\n\nfunction pathNames(obj, path) {\n    var regx = /\\/{2,9}/g,\n        names = path.replace(regx, \"/\").split(\"/\");\n\n    if (path.substr(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.substr(path.length - 1, 1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n\n    return names;\n}\n\nfunction queryKey(uri, query) {\n    var data = {};\n\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n\n    return data;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/parseuri/index.js\n// module id = 2\n// module chunks = 0", "\nmodule.exports = function () { return function () {}; };\n\n\n\n// WEBPACK FOOTER //\n// ./support/noop.js", "\n/**\n * Module dependencies.\n */\n\nvar debug = require('debug')('socket.io-parser');\nvar Emitter = require('component-emitter');\nvar binary = require('./binary');\nvar isArray = require('isarray');\nvar isBuf = require('./is-buffer');\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nexports.protocol = 4;\n\n/**\n * Packet types.\n *\n * @api public\n */\n\nexports.types = [\n  'CONNECT',\n  'DISCONNECT',\n  'EVENT',\n  'ACK',\n  'ERROR',\n  'BINARY_EVENT',\n  'BINARY_ACK'\n];\n\n/**\n * Packet type `connect`.\n *\n * @api public\n */\n\nexports.CONNECT = 0;\n\n/**\n * Packet type `disconnect`.\n *\n * @api public\n */\n\nexports.DISCONNECT = 1;\n\n/**\n * Packet type `event`.\n *\n * @api public\n */\n\nexports.EVENT = 2;\n\n/**\n * Packet type `ack`.\n *\n * @api public\n */\n\nexports.ACK = 3;\n\n/**\n * Packet type `error`.\n *\n * @api public\n */\n\nexports.ERROR = 4;\n\n/**\n * Packet type 'binary event'\n *\n * @api public\n */\n\nexports.BINARY_EVENT = 5;\n\n/**\n * Packet type `binary ack`. For acks with binary arguments.\n *\n * @api public\n */\n\nexports.BINARY_ACK = 6;\n\n/**\n * Encoder constructor.\n *\n * @api public\n */\n\nexports.Encoder = Encoder;\n\n/**\n * Decoder constructor.\n *\n * @api public\n */\n\nexports.Decoder = Decoder;\n\n/**\n * A socket.io Encoder instance\n *\n * @api public\n */\n\nfunction Encoder() {}\n\nvar ERROR_PACKET = exports.ERROR + '\"encode error\"';\n\n/**\n * Encode a packet as a single string if non-binary, or as a\n * buffer sequence, depending on packet type.\n *\n * @param {Object} obj - packet object\n * @param {Function} callback - function to handle encodings (likely engine.write)\n * @return Calls callback with Array of encodings\n * @api public\n */\n\nEncoder.prototype.encode = function(obj, callback){\n\n\n  if (exports.BINARY_EVENT === obj.type || exports.BINARY_ACK === obj.type) {\n    encodeAsBinary(obj, callback);\n  } else {\n    var encoding = encodeAsString(obj);\n    callback([encoding]);\n  }\n};\n\n/**\n * Encode packet as string.\n *\n * @param {Object} packet\n * @return {String} encoded\n * @api private\n */\n\nfunction encodeAsString(obj) {\n\n  // first is type\n  var str = '' + obj.type;\n\n  // attachments if we have them\n  if (exports.BINARY_EVENT === obj.type || exports.BINARY_ACK === obj.type) {\n    str += obj.attachments + '-';\n  }\n\n  // if we have a namespace other than `/`\n  // we append it followed by a comma `,`\n  if (obj.nsp && '/' !== obj.nsp) {\n    str += obj.nsp + ',';\n  }\n\n  // immediately followed by the id\n  if (null != obj.id) {\n    str += obj.id;\n  }\n\n  // json data\n  if (null != obj.data) {\n    var payload = tryStringify(obj.data);\n    if (payload !== false) {\n      str += payload;\n    } else {\n      return ERROR_PACKET;\n    }\n  }\n\n\n  return str;\n}\n\nfunction tryStringify(str) {\n  try {\n    return JSON.stringify(str);\n  } catch(e){\n    return false;\n  }\n}\n\n/**\n * Encode packet as 'buffer sequence' by removing blobs, and\n * deconstructing packet into object with placeholders and\n * a list of buffers.\n *\n * @param {Object} packet\n * @return {Buffer} encoded\n * @api private\n */\n\nfunction encodeAsBinary(obj, callback) {\n\n  function writeEncoding(bloblessData) {\n    var deconstruction = binary.deconstructPacket(bloblessData);\n    var pack = encodeAsString(deconstruction.packet);\n    var buffers = deconstruction.buffers;\n\n    buffers.unshift(pack); // add packet info to beginning of data list\n    callback(buffers); // write all the buffers\n  }\n\n  binary.removeBlobs(obj, writeEncoding);\n}\n\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n * @api public\n */\n\nfunction Decoder() {\n  this.reconstructor = null;\n}\n\n/**\n * Mix in `Emitter` with Decoder.\n */\n\nEmitter(Decoder.prototype);\n\n/**\n * Decodes an encoded packet string into packet JSON.\n *\n * @param {String} obj - encoded packet\n * @return {Object} packet\n * @api public\n */\n\nDecoder.prototype.add = function(obj) {\n  var packet;\n  if (typeof obj === 'string') {\n    packet = decodeString(obj);\n    if (exports.BINARY_EVENT === packet.type || exports.BINARY_ACK === packet.type) { // binary packet's json\n      this.reconstructor = new BinaryReconstructor(packet);\n\n      // no attachments, labeled binary but no binary data to follow\n      if (this.reconstructor.reconPack.attachments === 0) {\n        this.emit('decoded', packet);\n      }\n    } else { // non-binary full packet\n      this.emit('decoded', packet);\n    }\n  } else if (isBuf(obj) || obj.base64) { // raw binary data\n    if (!this.reconstructor) {\n      throw new Error('got binary data when not reconstructing a packet');\n    } else {\n      packet = this.reconstructor.takeBinaryData(obj);\n      if (packet) { // received final buffer\n        this.reconstructor = null;\n        this.emit('decoded', packet);\n      }\n    }\n  } else {\n    throw new Error('Unknown type: ' + obj);\n  }\n};\n\n/**\n * Decode a packet String (JSON data)\n *\n * @param {String} str\n * @return {Object} packet\n * @api private\n */\n\nfunction decodeString(str) {\n  var i = 0;\n  // look up type\n  var p = {\n    type: Number(str.charAt(0))\n  };\n\n  if (null == exports.types[p.type]) {\n    return error('unknown packet type ' + p.type);\n  }\n\n  // look up attachments if type binary\n  if (exports.BINARY_EVENT === p.type || exports.BINARY_ACK === p.type) {\n    var buf = '';\n    while (str.charAt(++i) !== '-') {\n      buf += str.charAt(i);\n      if (i == str.length) break;\n    }\n    if (buf != Number(buf) || str.charAt(i) !== '-') {\n      throw new Error('Illegal attachments');\n    }\n    p.attachments = Number(buf);\n  }\n\n  // look up namespace (if any)\n  if ('/' === str.charAt(i + 1)) {\n    p.nsp = '';\n    while (++i) {\n      var c = str.charAt(i);\n      if (',' === c) break;\n      p.nsp += c;\n      if (i === str.length) break;\n    }\n  } else {\n    p.nsp = '/';\n  }\n\n  // look up id\n  var next = str.charAt(i + 1);\n  if ('' !== next && Number(next) == next) {\n    p.id = '';\n    while (++i) {\n      var c = str.charAt(i);\n      if (null == c || Number(c) != c) {\n        --i;\n        break;\n      }\n      p.id += str.charAt(i);\n      if (i === str.length) break;\n    }\n    p.id = Number(p.id);\n  }\n\n  // look up json data\n  if (str.charAt(++i)) {\n    var payload = tryParse(str.substr(i));\n    var isPayloadValid = payload !== false && (p.type === exports.ERROR || isArray(payload));\n    if (isPayloadValid) {\n      p.data = payload;\n    } else {\n      return error('invalid payload');\n    }\n  }\n\n\n  return p;\n}\n\nfunction tryParse(str) {\n  try {\n    return JSON.parse(str);\n  } catch(e){\n    return false;\n  }\n}\n\n/**\n * Deallocates a parser's resources\n *\n * @api public\n */\n\nDecoder.prototype.destroy = function() {\n  if (this.reconstructor) {\n    this.reconstructor.finishedReconstruction();\n  }\n};\n\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n * @api private\n */\n\nfunction BinaryReconstructor(packet) {\n  this.reconPack = packet;\n  this.buffers = [];\n}\n\n/**\n * Method to be called when binary data received from connection\n * after a BINARY_EVENT packet.\n *\n * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n * @return {null | Object} returns null if more binary data is expected or\n *   a reconstructed packet object if all buffers have been received.\n * @api private\n */\n\nBinaryReconstructor.prototype.takeBinaryData = function(binData) {\n  this.buffers.push(binData);\n  if (this.buffers.length === this.reconPack.attachments) { // done with buffer list\n    var packet = binary.reconstructPacket(this.reconPack, this.buffers);\n    this.finishedReconstruction();\n    return packet;\n  }\n  return null;\n};\n\n/**\n * Cleans up binary packet reconstruction variables.\n *\n * @api private\n */\n\nBinaryReconstructor.prototype.finishedReconstruction = function() {\n  this.reconPack = null;\n  this.buffers = [];\n};\n\nfunction error(msg) {\n  return {\n    type: exports.ERROR,\n    data: 'parser error: ' + msg\n  };\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/socket.io-parser/index.js\n// module id = 4\n// module chunks = 0", "\r\n/**\r\n * Expose `Emitter`.\r\n */\r\n\r\nif (typeof module !== 'undefined') {\r\n  module.exports = Emitter;\r\n}\r\n\r\n/**\r\n * Initialize a new `Emitter`.\r\n *\r\n * @api public\r\n */\r\n\r\nfunction Emitter(obj) {\r\n  if (obj) return mixin(obj);\r\n};\r\n\r\n/**\r\n * Mixin the emitter properties.\r\n *\r\n * @param {Object} obj\r\n * @return {Object}\r\n * @api private\r\n */\r\n\r\nfunction mixin(obj) {\r\n  for (var key in Emitter.prototype) {\r\n    obj[key] = Emitter.prototype[key];\r\n  }\r\n  return obj;\r\n}\r\n\r\n/**\r\n * Listen on the given `event` with `fn`.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.on =\r\nEmitter.prototype.addEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\r\n    .push(fn);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Adds an `event` listener that will be invoked a single\r\n * time then automatically removed.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.once = function(event, fn){\r\n  function on() {\r\n    this.off(event, on);\r\n    fn.apply(this, arguments);\r\n  }\r\n\r\n  on.fn = fn;\r\n  this.on(event, on);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Remove the given callback for `event` or all\r\n * registered callbacks.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.off =\r\nEmitter.prototype.removeListener =\r\nEmitter.prototype.removeAllListeners =\r\nEmitter.prototype.removeEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  // all\r\n  if (0 == arguments.length) {\r\n    this._callbacks = {};\r\n    return this;\r\n  }\r\n\r\n  // specific event\r\n  var callbacks = this._callbacks['$' + event];\r\n  if (!callbacks) return this;\r\n\r\n  // remove all handlers\r\n  if (1 == arguments.length) {\r\n    delete this._callbacks['$' + event];\r\n    return this;\r\n  }\r\n\r\n  // remove specific handler\r\n  var cb;\r\n  for (var i = 0; i < callbacks.length; i++) {\r\n    cb = callbacks[i];\r\n    if (cb === fn || cb.fn === fn) {\r\n      callbacks.splice(i, 1);\r\n      break;\r\n    }\r\n  }\r\n\r\n  // Remove event specific arrays for event types that no\r\n  // one is subscribed for to avoid memory leak.\r\n  if (callbacks.length === 0) {\r\n    delete this._callbacks['$' + event];\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Emit `event` with the given args.\r\n *\r\n * @param {String} event\r\n * @param {Mixed} ...\r\n * @return {Emitter}\r\n */\r\n\r\nEmitter.prototype.emit = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  var args = new Array(arguments.length - 1)\r\n    , callbacks = this._callbacks['$' + event];\r\n\r\n  for (var i = 1; i < arguments.length; i++) {\r\n    args[i - 1] = arguments[i];\r\n  }\r\n\r\n  if (callbacks) {\r\n    callbacks = callbacks.slice(0);\r\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\r\n      callbacks[i].apply(this, args);\r\n    }\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Return array of callbacks for `event`.\r\n *\r\n * @param {String} event\r\n * @return {Array}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.listeners = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n  return this._callbacks['$' + event] || [];\r\n};\r\n\r\n/**\r\n * Check if this emitter has `event` handlers.\r\n *\r\n * @param {String} event\r\n * @return {Boolean}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.hasListeners = function(event){\r\n  return !! this.listeners(event).length;\r\n};\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/component-emitter/index.js\n// module id = 5\n// module chunks = 0", "/*global Blob,File*/\n\n/**\n * Module requirements\n */\n\nvar isArray = require('isarray');\nvar isBuf = require('./is-buffer');\nvar toString = Object.prototype.toString;\nvar withNativeBlob = typeof Blob === 'function' || (typeof Blob !== 'undefined' && toString.call(Blob) === '[object BlobConstructor]');\nvar withNativeFile = typeof File === 'function' || (typeof File !== 'undefined' && toString.call(File) === '[object FileConstructor]');\n\n/**\n * Replaces every Buffer | ArrayBuffer in packet with a numbered placeholder.\n * Anything with blobs or files should be fed through removeBlobs before coming\n * here.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @api public\n */\n\nexports.deconstructPacket = function(packet) {\n  var buffers = [];\n  var packetData = packet.data;\n  var pack = packet;\n  pack.data = _deconstructPacket(packetData, buffers);\n  pack.attachments = buffers.length; // number of binary 'attachments'\n  return {packet: pack, buffers: buffers};\n};\n\nfunction _deconstructPacket(data, buffers) {\n  if (!data) return data;\n\n  if (isBuf(data)) {\n    var placeholder = { _placeholder: true, num: buffers.length };\n    buffers.push(data);\n    return placeholder;\n  } else if (isArray(data)) {\n    var newData = new Array(data.length);\n    for (var i = 0; i < data.length; i++) {\n      newData[i] = _deconstructPacket(data[i], buffers);\n    }\n    return newData;\n  } else if (typeof data === 'object' && !(data instanceof Date)) {\n    var newData = {};\n    for (var key in data) {\n      newData[key] = _deconstructPacket(data[key], buffers);\n    }\n    return newData;\n  }\n  return data;\n}\n\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @api public\n */\n\nexports.reconstructPacket = function(packet, buffers) {\n  packet.data = _reconstructPacket(packet.data, buffers);\n  packet.attachments = undefined; // no longer useful\n  return packet;\n};\n\nfunction _reconstructPacket(data, buffers) {\n  if (!data) return data;\n\n  if (data && data._placeholder) {\n    return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n  } else if (isArray(data)) {\n    for (var i = 0; i < data.length; i++) {\n      data[i] = _reconstructPacket(data[i], buffers);\n    }\n  } else if (typeof data === 'object') {\n    for (var key in data) {\n      data[key] = _reconstructPacket(data[key], buffers);\n    }\n  }\n\n  return data;\n}\n\n/**\n * Asynchronously removes Blobs or Files from data via\n * FileReader's readAsArrayBuffer method. Used before encoding\n * data as msgpack. Calls callback with the blobless data.\n *\n * @param {Object} data\n * @param {Function} callback\n * @api private\n */\n\nexports.removeBlobs = function(data, callback) {\n  function _removeBlobs(obj, curKey, containingObject) {\n    if (!obj) return obj;\n\n    // convert any blob\n    if ((withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File)) {\n      pendingBlobs++;\n\n      // async filereader\n      var fileReader = new FileReader();\n      fileReader.onload = function() { // this.result == arraybuffer\n        if (containingObject) {\n          containingObject[curKey] = this.result;\n        }\n        else {\n          bloblessData = this.result;\n        }\n\n        // if nothing pending its callback time\n        if(! --pendingBlobs) {\n          callback(bloblessData);\n        }\n      };\n\n      fileReader.readAsArrayBuffer(obj); // blob -> arraybuffer\n    } else if (isArray(obj)) { // handle array\n      for (var i = 0; i < obj.length; i++) {\n        _removeBlobs(obj[i], i, obj);\n      }\n    } else if (typeof obj === 'object' && !isBuf(obj)) { // and object\n      for (var key in obj) {\n        _removeBlobs(obj[key], key, obj);\n      }\n    }\n  }\n\n  var pendingBlobs = 0;\n  var bloblessData = data;\n  _removeBlobs(bloblessData);\n  if (!pendingBlobs) {\n    callback(bloblessData);\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/socket.io-parser/binary.js\n// module id = 6\n// module chunks = 0", "var toString = {}.toString;\n\nmodule.exports = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/isarray/index.js\n// module id = 7\n// module chunks = 0", "\nmodule.exports = isBuf;\n\nvar withNativeBuffer = typeof Buffer === 'function' && typeof Buffer.isBuffer === 'function';\nvar withNativeArrayBuffer = typeof ArrayBuffer === 'function';\n\nvar isView = function (obj) {\n  return typeof ArrayBuffer.isView === 'function' ? ArrayBuffer.isView(obj) : (obj.buffer instanceof ArrayBuffer);\n};\n\n/**\n * Returns true if obj is a buffer or an arraybuffer.\n *\n * @api private\n */\n\nfunction isBuf(obj) {\n  return (withNativeBuffer && Buffer.isBuffer(obj)) ||\n          (withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj)));\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/socket.io-parser/is-buffer.js\n// module id = 8\n// module chunks = 0", "\n/**\n * Module dependencies.\n */\n\nvar eio = require('engine.io-client');\nvar Socket = require('./socket');\nvar Emitter = require('component-emitter');\nvar parser = require('socket.io-parser');\nvar on = require('./on');\nvar bind = require('component-bind');\nvar debug = require('debug')('socket.io-client:manager');\nvar indexOf = require('indexof');\nvar Backoff = require('backo2');\n\n/**\n * IE6+ hasOwnProperty\n */\n\nvar has = Object.prototype.hasOwnProperty;\n\n/**\n * Module exports\n */\n\nmodule.exports = Manager;\n\n/**\n * `Manager` constructor.\n *\n * @param {String} engine instance or engine uri/opts\n * @param {Object} options\n * @api public\n */\n\nfunction Manager (uri, opts) {\n  if (!(this instanceof Manager)) return new Manager(uri, opts);\n  if (uri && ('object' === typeof uri)) {\n    opts = uri;\n    uri = undefined;\n  }\n  opts = opts || {};\n\n  opts.path = opts.path || '/socket.io';\n  this.nsps = {};\n  this.subs = [];\n  this.opts = opts;\n  this.reconnection(opts.reconnection !== false);\n  this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n  this.reconnectionDelay(opts.reconnectionDelay || 1000);\n  this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n  this.randomizationFactor(opts.randomizationFactor || 0.5);\n  this.backoff = new Backoff({\n    min: this.reconnectionDelay(),\n    max: this.reconnectionDelayMax(),\n    jitter: this.randomizationFactor()\n  });\n  this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n  this.readyState = 'closed';\n  this.uri = uri;\n  this.connecting = [];\n  this.lastPing = null;\n  this.encoding = false;\n  this.packetBuffer = [];\n  var _parser = opts.parser || parser;\n  this.encoder = new _parser.Encoder();\n  this.decoder = new _parser.Decoder();\n  this.autoConnect = opts.autoConnect !== false;\n  if (this.autoConnect) this.open();\n}\n\n/**\n * Propagate given event to sockets and emit on `this`\n *\n * @api private\n */\n\nManager.prototype.emitAll = function () {\n  this.emit.apply(this, arguments);\n  for (var nsp in this.nsps) {\n    if (has.call(this.nsps, nsp)) {\n      this.nsps[nsp].emit.apply(this.nsps[nsp], arguments);\n    }\n  }\n};\n\n/**\n * Update `socket.id` of all sockets\n *\n * @api private\n */\n\nManager.prototype.updateSocketIds = function () {\n  for (var nsp in this.nsps) {\n    if (has.call(this.nsps, nsp)) {\n      this.nsps[nsp].id = this.generateId(nsp);\n    }\n  }\n};\n\n/**\n * generate `socket.id` for the given `nsp`\n *\n * @param {String} nsp\n * @return {String}\n * @api private\n */\n\nManager.prototype.generateId = function (nsp) {\n  return (nsp === '/' ? '' : (nsp + '#')) + this.engine.id;\n};\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Manager.prototype);\n\n/**\n * Sets the `reconnection` config.\n *\n * @param {Boolean} true/false if it should automatically reconnect\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnection = function (v) {\n  if (!arguments.length) return this._reconnection;\n  this._reconnection = !!v;\n  return this;\n};\n\n/**\n * Sets the reconnection attempts config.\n *\n * @param {Number} max reconnection attempts before giving up\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnectionAttempts = function (v) {\n  if (!arguments.length) return this._reconnectionAttempts;\n  this._reconnectionAttempts = v;\n  return this;\n};\n\n/**\n * Sets the delay between reconnections.\n *\n * @param {Number} delay\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnectionDelay = function (v) {\n  if (!arguments.length) return this._reconnectionDelay;\n  this._reconnectionDelay = v;\n  this.backoff && this.backoff.setMin(v);\n  return this;\n};\n\nManager.prototype.randomizationFactor = function (v) {\n  if (!arguments.length) return this._randomizationFactor;\n  this._randomizationFactor = v;\n  this.backoff && this.backoff.setJitter(v);\n  return this;\n};\n\n/**\n * Sets the maximum delay between reconnections.\n *\n * @param {Number} delay\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnectionDelayMax = function (v) {\n  if (!arguments.length) return this._reconnectionDelayMax;\n  this._reconnectionDelayMax = v;\n  this.backoff && this.backoff.setMax(v);\n  return this;\n};\n\n/**\n * Sets the connection timeout. `false` to disable\n *\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.timeout = function (v) {\n  if (!arguments.length) return this._timeout;\n  this._timeout = v;\n  return this;\n};\n\n/**\n * Starts trying to reconnect if reconnection is enabled and we have not\n * started reconnecting yet\n *\n * @api private\n */\n\nManager.prototype.maybeReconnectOnOpen = function () {\n  // Only try to reconnect if it's the first time we're connecting\n  if (!this.reconnecting && this._reconnection && this.backoff.attempts === 0) {\n    // keeps reconnection from firing twice for the same reconnection loop\n    this.reconnect();\n  }\n};\n\n/**\n * Sets the current transport `socket`.\n *\n * @param {Function} optional, callback\n * @return {Manager} self\n * @api public\n */\n\nManager.prototype.open =\nManager.prototype.connect = function (fn, opts) {\n\n  if (~this.readyState.indexOf('open')) return this;\n\n\n  this.engine = eio(this.uri, this.opts);\n  var socket = this.engine;\n  var self = this;\n  this.readyState = 'opening';\n  this.skipReconnect = false;\n\n  // emit `open`\n  var openSub = on(socket, 'open', function () {\n    self.onopen();\n    fn && fn();\n  });\n\n  // emit `connect_error`\n  var errorSub = on(socket, 'error', function (data) {\n\n    self.cleanup();\n    self.readyState = 'closed';\n    self.emitAll('connect_error', data);\n    if (fn) {\n      var err = new Error('Connection error');\n      err.data = data;\n      fn(err);\n    } else {\n      // Only do this if there is no fn to handle the error\n      self.maybeReconnectOnOpen();\n    }\n  });\n\n  // emit `connect_timeout`\n  if (false !== this._timeout) {\n    var timeout = this._timeout;\n\n\n    if (timeout === 0) {\n      openSub.destroy(); // prevents a race condition with the 'open' event\n    }\n\n    // set timer\n    var timer = setTimeout(function () {\n\n      openSub.destroy();\n      socket.close();\n      socket.emit('error', 'timeout');\n      self.emitAll('connect_timeout', timeout);\n    }, timeout);\n\n    this.subs.push({\n      destroy: function () {\n        clearTimeout(timer);\n      }\n    });\n  }\n\n  this.subs.push(openSub);\n  this.subs.push(errorSub);\n\n  return this;\n};\n\n/**\n * Called upon transport open.\n *\n * @api private\n */\n\nManager.prototype.onopen = function () {\n\n\n  // clear old subs\n  this.cleanup();\n\n  // mark as open\n  this.readyState = 'open';\n  this.emit('open');\n\n  // add new subs\n  var socket = this.engine;\n  this.subs.push(on(socket, 'data', bind(this, 'ondata')));\n  this.subs.push(on(socket, 'ping', bind(this, 'onping')));\n  this.subs.push(on(socket, 'pong', bind(this, 'onpong')));\n  this.subs.push(on(socket, 'error', bind(this, 'onerror')));\n  this.subs.push(on(socket, 'close', bind(this, 'onclose')));\n  this.subs.push(on(this.decoder, 'decoded', bind(this, 'ondecoded')));\n};\n\n/**\n * Called upon a ping.\n *\n * @api private\n */\n\nManager.prototype.onping = function () {\n  this.lastPing = new Date();\n  this.emitAll('ping');\n};\n\n/**\n * Called upon a packet.\n *\n * @api private\n */\n\nManager.prototype.onpong = function () {\n  this.emitAll('pong', new Date() - this.lastPing);\n};\n\n/**\n * Called with data.\n *\n * @api private\n */\n\nManager.prototype.ondata = function (data) {\n  this.decoder.add(data);\n};\n\n/**\n * Called when parser fully decodes a packet.\n *\n * @api private\n */\n\nManager.prototype.ondecoded = function (packet) {\n  this.emit('packet', packet);\n};\n\n/**\n * Called upon socket error.\n *\n * @api private\n */\n\nManager.prototype.onerror = function (err) {\n\n  this.emitAll('error', err);\n};\n\n/**\n * Creates a new socket for the given `nsp`.\n *\n * @return {Socket}\n * @api public\n */\n\nManager.prototype.socket = function (nsp, opts) {\n  var socket = this.nsps[nsp];\n  if (!socket) {\n    socket = new Socket(this, nsp, opts);\n    this.nsps[nsp] = socket;\n    var self = this;\n    socket.on('connecting', onConnecting);\n    socket.on('connect', function () {\n      socket.id = self.generateId(nsp);\n    });\n\n    if (this.autoConnect) {\n      // manually call here since connecting event is fired before listening\n      onConnecting();\n    }\n  }\n\n  function onConnecting () {\n    if (!~indexOf(self.connecting, socket)) {\n      self.connecting.push(socket);\n    }\n  }\n\n  return socket;\n};\n\n/**\n * Called upon a socket close.\n *\n * @param {Socket} socket\n */\n\nManager.prototype.destroy = function (socket) {\n  var index = indexOf(this.connecting, socket);\n  if (~index) this.connecting.splice(index, 1);\n  if (this.connecting.length) return;\n\n  this.close();\n};\n\n/**\n * Writes a packet.\n *\n * @param {Object} packet\n * @api private\n */\n\nManager.prototype.packet = function (packet) {\n\n  var self = this;\n  if (packet.query && packet.type === 0) packet.nsp += '?' + packet.query;\n\n  if (!self.encoding) {\n    // encode, then write to engine with result\n    self.encoding = true;\n    this.encoder.encode(packet, function (encodedPackets) {\n      for (var i = 0; i < encodedPackets.length; i++) {\n        self.engine.write(encodedPackets[i], packet.options);\n      }\n      self.encoding = false;\n      self.processPacketQueue();\n    });\n  } else { // add packet to the queue\n    self.packetBuffer.push(packet);\n  }\n};\n\n/**\n * If packet buffer is non-empty, begins encoding the\n * next packet in line.\n *\n * @api private\n */\n\nManager.prototype.processPacketQueue = function () {\n  if (this.packetBuffer.length > 0 && !this.encoding) {\n    var pack = this.packetBuffer.shift();\n    this.packet(pack);\n  }\n};\n\n/**\n * Clean up transport subscriptions and packet buffer.\n *\n * @api private\n */\n\nManager.prototype.cleanup = function () {\n\n\n  var subsLength = this.subs.length;\n  for (var i = 0; i < subsLength; i++) {\n    var sub = this.subs.shift();\n    sub.destroy();\n  }\n\n  this.packetBuffer = [];\n  this.encoding = false;\n  this.lastPing = null;\n\n  this.decoder.destroy();\n};\n\n/**\n * Close the current socket.\n *\n * @api private\n */\n\nManager.prototype.close =\nManager.prototype.disconnect = function () {\n\n  this.skipReconnect = true;\n  this.reconnecting = false;\n  if ('opening' === this.readyState) {\n    // `onclose` will not fire because\n    // an open event never happened\n    this.cleanup();\n  }\n  this.backoff.reset();\n  this.readyState = 'closed';\n  if (this.engine) this.engine.close();\n};\n\n/**\n * Called upon engine close.\n *\n * @api private\n */\n\nManager.prototype.onclose = function (reason) {\n\n\n  this.cleanup();\n  this.backoff.reset();\n  this.readyState = 'closed';\n  this.emit('close', reason);\n\n  if (this._reconnection && !this.skipReconnect) {\n    this.reconnect();\n  }\n};\n\n/**\n * Attempt a reconnection.\n *\n * @api private\n */\n\nManager.prototype.reconnect = function () {\n  if (this.reconnecting || this.skipReconnect) return this;\n\n  var self = this;\n\n  if (this.backoff.attempts >= this._reconnectionAttempts) {\n\n    this.backoff.reset();\n    this.emitAll('reconnect_failed');\n    this.reconnecting = false;\n  } else {\n    var delay = this.backoff.duration();\n\n\n    this.reconnecting = true;\n    var timer = setTimeout(function () {\n      if (self.skipReconnect) return;\n\n\n      self.emitAll('reconnect_attempt', self.backoff.attempts);\n      self.emitAll('reconnecting', self.backoff.attempts);\n\n      // check again for the case socket closed in above events\n      if (self.skipReconnect) return;\n\n      self.open(function (err) {\n        if (err) {\n\n          self.reconnecting = false;\n          self.reconnect();\n          self.emitAll('reconnect_error', err.data);\n        } else {\n\n          self.onreconnect();\n        }\n      });\n    }, delay);\n\n    this.subs.push({\n      destroy: function () {\n        clearTimeout(timer);\n      }\n    });\n  }\n};\n\n/**\n * Called upon successful reconnect.\n *\n * @api private\n */\n\nManager.prototype.onreconnect = function () {\n  var attempt = this.backoff.attempts;\n  this.reconnecting = false;\n  this.backoff.reset();\n  this.updateSocketIds();\n  this.emitAll('reconnect', attempt);\n};\n\n\n\n// WEBPACK FOOTER //\n// ./lib/manager.js", "\nmodule.exports = require('./socket');\n\n/**\n * Exports parser\n *\n * @api public\n *\n */\nmodule.exports.parser = require('engine.io-parser');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/index.js\n// module id = 10\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar transports = require('./transports/index');\nvar Emitter = require('component-emitter');\nvar debug = require('debug')('engine.io-client:socket');\nvar index = require('indexof');\nvar parser = require('engine.io-parser');\nvar parseuri = require('parseuri');\nvar parseqs = require('parseqs');\n\n/**\n * Module exports.\n */\n\nmodule.exports = Socket;\n\n/**\n * Socket constructor.\n *\n * @param {String|Object} uri or options\n * @param {Object} options\n * @api public\n */\n\nfunction Socket (uri, opts) {\n  if (!(this instanceof Socket)) return new Socket(uri, opts);\n\n  opts = opts || {};\n\n  if (uri && 'object' === typeof uri) {\n    opts = uri;\n    uri = null;\n  }\n\n  if (uri) {\n    uri = parseuri(uri);\n    opts.hostname = uri.host;\n    opts.secure = uri.protocol === 'https' || uri.protocol === 'wss';\n    opts.port = uri.port;\n    if (uri.query) opts.query = uri.query;\n  } else if (opts.host) {\n    opts.hostname = parseuri(opts.host).host;\n  }\n\n  this.secure = null != opts.secure ? opts.secure\n    : (typeof location !== 'undefined' && 'https:' === location.protocol);\n\n  if (opts.hostname && !opts.port) {\n    // if no port is specified manually, use the protocol default\n    opts.port = this.secure ? '443' : '80';\n  }\n\n  this.agent = opts.agent || false;\n  this.hostname = opts.hostname ||\n    (typeof location !== 'undefined' ? location.hostname : 'localhost');\n  this.port = opts.port || (typeof location !== 'undefined' && location.port\n      ? location.port\n      : (this.secure ? 443 : 80));\n  this.query = opts.query || {};\n  if ('string' === typeof this.query) this.query = parseqs.decode(this.query);\n  this.upgrade = false !== opts.upgrade;\n  this.path = (opts.path || '/engine.io').replace(/\\/$/, '') + '/';\n  this.forceJSONP = !!opts.forceJSONP;\n  this.jsonp = false !== opts.jsonp;\n  this.forceBase64 = !!opts.forceBase64;\n  this.enablesXDR = !!opts.enablesXDR;\n  this.withCredentials = false !== opts.withCredentials;\n  this.timestampParam = opts.timestampParam || 't';\n  this.timestampRequests = opts.timestampRequests;\n  this.transports = opts.transports || ['polling', 'websocket'];\n  this.transportOptions = opts.transportOptions || {};\n  this.readyState = '';\n  this.writeBuffer = [];\n  this.prevBufferLen = 0;\n  this.policyPort = opts.policyPort || 843;\n  this.rememberUpgrade = opts.rememberUpgrade || false;\n  this.binaryType = null;\n  this.onlyBinaryUpgrades = opts.onlyBinaryUpgrades;\n  this.perMessageDeflate = false !== opts.perMessageDeflate ? (opts.perMessageDeflate || {}) : false;\n\n  if (true === this.perMessageDeflate) this.perMessageDeflate = {};\n  if (this.perMessageDeflate && null == this.perMessageDeflate.threshold) {\n    this.perMessageDeflate.threshold = 1024;\n  }\n\n  // SSL options for Node.js client\n  this.pfx = opts.pfx || null;\n  this.key = opts.key || null;\n  this.passphrase = opts.passphrase || null;\n  this.cert = opts.cert || null;\n  this.ca = opts.ca || null;\n  this.ciphers = opts.ciphers || null;\n  this.rejectUnauthorized = opts.rejectUnauthorized === undefined ? true : opts.rejectUnauthorized;\n  this.forceNode = !!opts.forceNode;\n\n  // detect ReactNative environment\n  this.isReactNative = (typeof navigator !== 'undefined' && typeof navigator.product === 'string' && navigator.product.toLowerCase() === 'reactnative');\n\n  // other options for Node.js or ReactNative client\n  if (typeof self === 'undefined' || this.isReactNative) {\n    if (opts.extraHeaders && Object.keys(opts.extraHeaders).length > 0) {\n      this.extraHeaders = opts.extraHeaders;\n    }\n\n    if (opts.localAddress) {\n      this.localAddress = opts.localAddress;\n    }\n  }\n\n  // set on handshake\n  this.id = null;\n  this.upgrades = null;\n  this.pingInterval = null;\n  this.pingTimeout = null;\n\n  // set on heartbeat\n  this.pingIntervalTimer = null;\n  this.pingTimeoutTimer = null;\n\n  this.open();\n}\n\nSocket.priorWebsocketSuccess = false;\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Socket.prototype);\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nSocket.protocol = parser.protocol; // this is an int\n\n/**\n * Expose deps for legacy compatibility\n * and standalone browser access.\n */\n\nSocket.Socket = Socket;\nSocket.Transport = require('./transport');\nSocket.transports = require('./transports/index');\nSocket.parser = require('engine.io-parser');\n\n/**\n * Creates transport of the given type.\n *\n * @param {String} transport name\n * @return {Transport}\n * @api private\n */\n\nSocket.prototype.createTransport = function (name) {\n\n  var query = clone(this.query);\n\n  // append engine.io protocol identifier\n  query.EIO = parser.protocol;\n\n  // transport name\n  query.transport = name;\n\n  // per-transport options\n  var options = this.transportOptions[name] || {};\n\n  // session id if we already have one\n  if (this.id) query.sid = this.id;\n\n  var transport = new transports[name]({\n    query: query,\n    socket: this,\n    agent: options.agent || this.agent,\n    hostname: options.hostname || this.hostname,\n    port: options.port || this.port,\n    secure: options.secure || this.secure,\n    path: options.path || this.path,\n    forceJSONP: options.forceJSONP || this.forceJSONP,\n    jsonp: options.jsonp || this.jsonp,\n    forceBase64: options.forceBase64 || this.forceBase64,\n    enablesXDR: options.enablesXDR || this.enablesXDR,\n    withCredentials: options.withCredentials || this.withCredentials,\n    timestampRequests: options.timestampRequests || this.timestampRequests,\n    timestampParam: options.timestampParam || this.timestampParam,\n    policyPort: options.policyPort || this.policyPort,\n    pfx: options.pfx || this.pfx,\n    key: options.key || this.key,\n    passphrase: options.passphrase || this.passphrase,\n    cert: options.cert || this.cert,\n    ca: options.ca || this.ca,\n    ciphers: options.ciphers || this.ciphers,\n    rejectUnauthorized: options.rejectUnauthorized || this.rejectUnauthorized,\n    perMessageDeflate: options.perMessageDeflate || this.perMessageDeflate,\n    extraHeaders: options.extraHeaders || this.extraHeaders,\n    forceNode: options.forceNode || this.forceNode,\n    localAddress: options.localAddress || this.localAddress,\n    requestTimeout: options.requestTimeout || this.requestTimeout,\n    protocols: options.protocols || void (0),\n    isReactNative: this.isReactNative\n  });\n\n  return transport;\n};\n\nfunction clone (obj) {\n  var o = {};\n  for (var i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      o[i] = obj[i];\n    }\n  }\n  return o;\n}\n\n/**\n * Initializes transport to use and starts probe.\n *\n * @api private\n */\nSocket.prototype.open = function () {\n  var transport;\n  if (this.rememberUpgrade && Socket.priorWebsocketSuccess && this.transports.indexOf('websocket') !== -1) {\n    transport = 'websocket';\n  } else if (0 === this.transports.length) {\n    // Emit error on next tick so it can be listened to\n    var self = this;\n    setTimeout(function () {\n      self.emit('error', 'No transports available');\n    }, 0);\n    return;\n  } else {\n    transport = this.transports[0];\n  }\n  this.readyState = 'opening';\n\n  // Retry with the next transport if the transport is disabled (jsonp: false)\n  try {\n    transport = this.createTransport(transport);\n  } catch (e) {\n    this.transports.shift();\n    this.open();\n    return;\n  }\n\n  transport.open();\n  this.setTransport(transport);\n};\n\n/**\n * Sets the current transport. Disables the existing one (if any).\n *\n * @api private\n */\n\nSocket.prototype.setTransport = function (transport) {\n\n  var self = this;\n\n  if (this.transport) {\n\n    this.transport.removeAllListeners();\n  }\n\n  // set up transport\n  this.transport = transport;\n\n  // set up transport listeners\n  transport\n  .on('drain', function () {\n    self.onDrain();\n  })\n  .on('packet', function (packet) {\n    self.onPacket(packet);\n  })\n  .on('error', function (e) {\n    self.onError(e);\n  })\n  .on('close', function () {\n    self.onClose('transport close');\n  });\n};\n\n/**\n * Probes a transport.\n *\n * @param {String} transport name\n * @api private\n */\n\nSocket.prototype.probe = function (name) {\n\n  var transport = this.createTransport(name, { probe: 1 });\n  var failed = false;\n  var self = this;\n\n  Socket.priorWebsocketSuccess = false;\n\n  function onTransportOpen () {\n    if (self.onlyBinaryUpgrades) {\n      var upgradeLosesBinary = !this.supportsBinary && self.transport.supportsBinary;\n      failed = failed || upgradeLosesBinary;\n    }\n    if (failed) return;\n\n\n    transport.send([{ type: 'ping', data: 'probe' }]);\n    transport.once('packet', function (msg) {\n      if (failed) return;\n      if ('pong' === msg.type && 'probe' === msg.data) {\n\n        self.upgrading = true;\n        self.emit('upgrading', transport);\n        if (!transport) return;\n        Socket.priorWebsocketSuccess = 'websocket' === transport.name;\n\n\n        self.transport.pause(function () {\n          if (failed) return;\n          if ('closed' === self.readyState) return;\n\n\n          cleanup();\n\n          self.setTransport(transport);\n          transport.send([{ type: 'upgrade' }]);\n          self.emit('upgrade', transport);\n          transport = null;\n          self.upgrading = false;\n          self.flush();\n        });\n      } else {\n\n        var err = new Error('probe error');\n        err.transport = transport.name;\n        self.emit('upgradeError', err);\n      }\n    });\n  }\n\n  function freezeTransport () {\n    if (failed) return;\n\n    // Any callback called by transport should be ignored since now\n    failed = true;\n\n    cleanup();\n\n    transport.close();\n    transport = null;\n  }\n\n  // Handle any error that happens while probing\n  function onerror (err) {\n    var error = new Error('probe error: ' + err);\n    error.transport = transport.name;\n\n    freezeTransport();\n\n\n\n    self.emit('upgradeError', error);\n  }\n\n  function onTransportClose () {\n    onerror('transport closed');\n  }\n\n  // When the socket is closed while we're probing\n  function onclose () {\n    onerror('socket closed');\n  }\n\n  // When the socket is upgraded while we're probing\n  function onupgrade (to) {\n    if (transport && to.name !== transport.name) {\n\n      freezeTransport();\n    }\n  }\n\n  // Remove all listeners on the transport and on self\n  function cleanup () {\n    transport.removeListener('open', onTransportOpen);\n    transport.removeListener('error', onerror);\n    transport.removeListener('close', onTransportClose);\n    self.removeListener('close', onclose);\n    self.removeListener('upgrading', onupgrade);\n  }\n\n  transport.once('open', onTransportOpen);\n  transport.once('error', onerror);\n  transport.once('close', onTransportClose);\n\n  this.once('close', onclose);\n  this.once('upgrading', onupgrade);\n\n  transport.open();\n};\n\n/**\n * Called when connection is deemed open.\n *\n * @api public\n */\n\nSocket.prototype.onOpen = function () {\n\n  this.readyState = 'open';\n  Socket.priorWebsocketSuccess = 'websocket' === this.transport.name;\n  this.emit('open');\n  this.flush();\n\n  // we check for `readyState` in case an `open`\n  // listener already closed the socket\n  if ('open' === this.readyState && this.upgrade && this.transport.pause) {\n\n    for (var i = 0, l = this.upgrades.length; i < l; i++) {\n      this.probe(this.upgrades[i]);\n    }\n  }\n};\n\n/**\n * Handles a packet.\n *\n * @api private\n */\n\nSocket.prototype.onPacket = function (packet) {\n  if ('opening' === this.readyState || 'open' === this.readyState ||\n      'closing' === this.readyState) {\n\n\n    this.emit('packet', packet);\n\n    // Socket is live - any packet counts\n    this.emit('heartbeat');\n\n    switch (packet.type) {\n      case 'open':\n        this.onHandshake(JSON.parse(packet.data));\n        break;\n\n      case 'pong':\n        this.setPing();\n        this.emit('pong');\n        break;\n\n      case 'error':\n        var err = new Error('server error');\n        err.code = packet.data;\n        this.onError(err);\n        break;\n\n      case 'message':\n        this.emit('data', packet.data);\n        this.emit('message', packet.data);\n        break;\n    }\n  } else {\n\n  }\n};\n\n/**\n * Called upon handshake completion.\n *\n * @param {Object} handshake obj\n * @api private\n */\n\nSocket.prototype.onHandshake = function (data) {\n  this.emit('handshake', data);\n  this.id = data.sid;\n  this.transport.query.sid = data.sid;\n  this.upgrades = this.filterUpgrades(data.upgrades);\n  this.pingInterval = data.pingInterval;\n  this.pingTimeout = data.pingTimeout;\n  this.onOpen();\n  // In case open handler closes socket\n  if ('closed' === this.readyState) return;\n  this.setPing();\n\n  // Prolong liveness of socket on heartbeat\n  this.removeListener('heartbeat', this.onHeartbeat);\n  this.on('heartbeat', this.onHeartbeat);\n};\n\n/**\n * Resets ping timeout.\n *\n * @api private\n */\n\nSocket.prototype.onHeartbeat = function (timeout) {\n  clearTimeout(this.pingTimeoutTimer);\n  var self = this;\n  self.pingTimeoutTimer = setTimeout(function () {\n    if ('closed' === self.readyState) return;\n    self.onClose('ping timeout');\n  }, timeout || (self.pingInterval + self.pingTimeout));\n};\n\n/**\n * Pings server every `this.pingInterval` and expects response\n * within `this.pingTimeout` or closes connection.\n *\n * @api private\n */\n\nSocket.prototype.setPing = function () {\n  var self = this;\n  clearTimeout(self.pingIntervalTimer);\n  self.pingIntervalTimer = setTimeout(function () {\n\n    self.ping();\n    self.onHeartbeat(self.pingTimeout);\n  }, self.pingInterval);\n};\n\n/**\n* Sends a ping packet.\n*\n* @api private\n*/\n\nSocket.prototype.ping = function () {\n  var self = this;\n  this.sendPacket('ping', function () {\n    self.emit('ping');\n  });\n};\n\n/**\n * Called on `drain` event\n *\n * @api private\n */\n\nSocket.prototype.onDrain = function () {\n  this.writeBuffer.splice(0, this.prevBufferLen);\n\n  // setting prevBufferLen = 0 is very important\n  // for example, when upgrading, upgrade packet is sent over,\n  // and a nonzero prevBufferLen could cause problems on `drain`\n  this.prevBufferLen = 0;\n\n  if (0 === this.writeBuffer.length) {\n    this.emit('drain');\n  } else {\n    this.flush();\n  }\n};\n\n/**\n * Flush write buffers.\n *\n * @api private\n */\n\nSocket.prototype.flush = function () {\n  if ('closed' !== this.readyState && this.transport.writable &&\n    !this.upgrading && this.writeBuffer.length) {\n\n    this.transport.send(this.writeBuffer);\n    // keep track of current length of writeBuffer\n    // splice writeBuffer and callbackBuffer on `drain`\n    this.prevBufferLen = this.writeBuffer.length;\n    this.emit('flush');\n  }\n};\n\n/**\n * Sends a message.\n *\n * @param {String} message.\n * @param {Function} callback function.\n * @param {Object} options.\n * @return {Socket} for chaining.\n * @api public\n */\n\nSocket.prototype.write =\nSocket.prototype.send = function (msg, options, fn) {\n  this.sendPacket('message', msg, options, fn);\n  return this;\n};\n\n/**\n * Sends a packet.\n *\n * @param {String} packet type.\n * @param {String} data.\n * @param {Object} options.\n * @param {Function} callback function.\n * @api private\n */\n\nSocket.prototype.sendPacket = function (type, data, options, fn) {\n  if ('function' === typeof data) {\n    fn = data;\n    data = undefined;\n  }\n\n  if ('function' === typeof options) {\n    fn = options;\n    options = null;\n  }\n\n  if ('closing' === this.readyState || 'closed' === this.readyState) {\n    return;\n  }\n\n  options = options || {};\n  options.compress = false !== options.compress;\n\n  var packet = {\n    type: type,\n    data: data,\n    options: options\n  };\n  this.emit('packetCreate', packet);\n  this.writeBuffer.push(packet);\n  if (fn) this.once('flush', fn);\n  this.flush();\n};\n\n/**\n * Closes the connection.\n *\n * @api private\n */\n\nSocket.prototype.close = function () {\n  if ('opening' === this.readyState || 'open' === this.readyState) {\n    this.readyState = 'closing';\n\n    var self = this;\n\n    if (this.writeBuffer.length) {\n      this.once('drain', function () {\n        if (this.upgrading) {\n          waitForUpgrade();\n        } else {\n          close();\n        }\n      });\n    } else if (this.upgrading) {\n      waitForUpgrade();\n    } else {\n      close();\n    }\n  }\n\n  function close () {\n    self.onClose('forced close');\n\n    self.transport.close();\n  }\n\n  function cleanupAndClose () {\n    self.removeListener('upgrade', cleanupAndClose);\n    self.removeListener('upgradeError', cleanupAndClose);\n    close();\n  }\n\n  function waitForUpgrade () {\n    // wait for upgrade to finish since we can't send packets while pausing a transport\n    self.once('upgrade', cleanupAndClose);\n    self.once('upgradeError', cleanupAndClose);\n  }\n\n  return this;\n};\n\n/**\n * Called upon transport error\n *\n * @api private\n */\n\nSocket.prototype.onError = function (err) {\n\n  Socket.priorWebsocketSuccess = false;\n  this.emit('error', err);\n  this.onClose('transport error', err);\n};\n\n/**\n * Called upon transport close.\n *\n * @api private\n */\n\nSocket.prototype.onClose = function (reason, desc) {\n  if ('opening' === this.readyState || 'open' === this.readyState || 'closing' === this.readyState) {\n\n    var self = this;\n\n    // clear timers\n    clearTimeout(this.pingIntervalTimer);\n    clearTimeout(this.pingTimeoutTimer);\n\n    // stop event from firing again for transport\n    this.transport.removeAllListeners('close');\n\n    // ensure transport won't stay open\n    this.transport.close();\n\n    // ignore further transport communication\n    this.transport.removeAllListeners();\n\n    // set ready state\n    this.readyState = 'closed';\n\n    // clear session id\n    this.id = null;\n\n    // emit close event\n    this.emit('close', reason, desc);\n\n    // clean buffers after, so users can still\n    // grab the buffers on `close` event\n    self.writeBuffer = [];\n    self.prevBufferLen = 0;\n  }\n};\n\n/**\n * Filters upgrades, returning only those matching client transports.\n *\n * @param {Array} server upgrades\n * @api private\n *\n */\n\nSocket.prototype.filterUpgrades = function (upgrades) {\n  var filteredUpgrades = [];\n  for (var i = 0, j = upgrades.length; i < j; i++) {\n    if (~index(this.transports, upgrades[i])) filteredUpgrades.push(upgrades[i]);\n  }\n  return filteredUpgrades;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/socket.js\n// module id = 11\n// module chunks = 0", "/**\n * Module dependencies\n */\n\nvar XMLHttpRequest = require('xmlhttprequest-ssl');\nvar XHR = require('./polling-xhr');\nvar JSONP = require('./polling-jsonp');\nvar websocket = require('./websocket');\n\n/**\n * Export transports.\n */\n\nexports.polling = polling;\nexports.websocket = websocket;\n\n/**\n * Polling transport polymorphic constructor.\n * Decides on xhr vs jsonp based on feature detection.\n *\n * @api private\n */\n\nfunction polling (opts) {\n  var xhr;\n  var xd = false;\n  var xs = false;\n  var jsonp = false !== opts.jsonp;\n\n  if (typeof location !== 'undefined') {\n    var isSSL = 'https:' === location.protocol;\n    var port = location.port;\n\n    // some user agents have empty `location.port`\n    if (!port) {\n      port = isSSL ? 443 : 80;\n    }\n\n    xd = opts.hostname !== location.hostname || port !== opts.port;\n    xs = opts.secure !== isSSL;\n  }\n\n  opts.xdomain = xd;\n  opts.xscheme = xs;\n  xhr = new XMLHttpRequest(opts);\n\n  if ('open' in xhr && !opts.forceJSONP) {\n    return new XHR(opts);\n  } else {\n    if (!jsonp) throw new Error('JSONP disabled');\n    return new JSONP(opts);\n  }\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/index.js\n// module id = 12\n// module chunks = 0", "// browser shim for xmlhttprequest module\n\nvar hasCORS = require('has-cors');\nvar globalThis = require('./globalThis');\n\nmodule.exports = function (opts) {\n  var xdomain = opts.xdomain;\n\n  // scheme must be same when usign XDomainRequest\n  // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n  var xscheme = opts.xscheme;\n\n  // XDomainRequest has a flow of not sending cookie, therefore it should be disabled as a default.\n  // https://github.com/Automattic/engine.io-client/pull/217\n  var enablesXDR = opts.enablesXDR;\n\n  // XMLHttpRequest can be disabled on IE\n  try {\n    if ('undefined' !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n      return new XMLHttpRequest();\n    }\n  } catch (e) { }\n\n  // Use XDomainRequest for IE8 if enablesXDR is true\n  // because loading bar keeps flashing when using jsonp-polling\n  // https://github.com/yujiosaka/socke.io-ie8-loading-example\n  try {\n    if ('undefined' !== typeof XDomainRequest && !xscheme && enablesXDR) {\n      return new XDomainRequest();\n    }\n  } catch (e) { }\n\n  if (!xdomain) {\n    try {\n      return new globalThis[['Active'].concat('Object').join('X')]('Microsoft.XMLHTTP');\n    } catch (e) { }\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/xmlhttprequest.js\n// module id = 13\n// module chunks = 0", "\n/**\n * Module exports.\n *\n * Logic borrowed from Modernizr:\n *\n *   - https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cors.js\n */\n\ntry {\n  module.exports = typeof XMLHttpRequest !== 'undefined' &&\n    'withCredentials' in new XMLHttpRequest();\n} catch (err) {\n  // if XMLHttp support is disabled in IE then it will throw\n  // when trying to create\n  module.exports = false;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/has-cors/index.js\n// module id = 14\n// module chunks = 0", "module.exports = (function () {\n  if (typeof self !== 'undefined') {\n    return self;\n  } else if (typeof window !== 'undefined') {\n    return window;\n  } else {\n    return Function('return this')(); // eslint-disable-line no-new-func\n  }\n})();\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/globalThis.browser.js\n// module id = 15\n// module chunks = 0", "/* global attachEvent */\n\n/**\n * Module requirements.\n */\n\nvar XMLHttpRequest = require('xmlhttprequest-ssl');\nvar Polling = require('./polling');\nvar Emitter = require('component-emitter');\nvar inherit = require('component-inherit');\nvar debug = require('debug')('engine.io-client:polling-xhr');\nvar globalThis = require('../globalThis');\n\n/**\n * Module exports.\n */\n\nmodule.exports = XHR;\nmodule.exports.Request = Request;\n\n/**\n * Empty function\n */\n\nfunction empty () {}\n\n/**\n * XHR Polling constructor.\n *\n * @param {Object} opts\n * @api public\n */\n\nfunction XHR (opts) {\n  Polling.call(this, opts);\n  this.requestTimeout = opts.requestTimeout;\n  this.extraHeaders = opts.extraHeaders;\n\n  if (typeof location !== 'undefined') {\n    var isSSL = 'https:' === location.protocol;\n    var port = location.port;\n\n    // some user agents have empty `location.port`\n    if (!port) {\n      port = isSSL ? 443 : 80;\n    }\n\n    this.xd = (typeof location !== 'undefined' && opts.hostname !== location.hostname) ||\n      port !== opts.port;\n    this.xs = opts.secure !== isSSL;\n  }\n}\n\n/**\n * Inherits from Polling.\n */\n\ninherit(XHR, Polling);\n\n/**\n * XHR supports binary\n */\n\nXHR.prototype.supportsBinary = true;\n\n/**\n * Creates a request.\n *\n * @param {String} method\n * @api private\n */\n\nXHR.prototype.request = function (opts) {\n  opts = opts || {};\n  opts.uri = this.uri();\n  opts.xd = this.xd;\n  opts.xs = this.xs;\n  opts.agent = this.agent || false;\n  opts.supportsBinary = this.supportsBinary;\n  opts.enablesXDR = this.enablesXDR;\n  opts.withCredentials = this.withCredentials;\n\n  // SSL options for Node.js client\n  opts.pfx = this.pfx;\n  opts.key = this.key;\n  opts.passphrase = this.passphrase;\n  opts.cert = this.cert;\n  opts.ca = this.ca;\n  opts.ciphers = this.ciphers;\n  opts.rejectUnauthorized = this.rejectUnauthorized;\n  opts.requestTimeout = this.requestTimeout;\n\n  // other options for Node.js client\n  opts.extraHeaders = this.extraHeaders;\n\n  return new Request(opts);\n};\n\n/**\n * Sends data.\n *\n * @param {String} data to send.\n * @param {Function} called upon flush.\n * @api private\n */\n\nXHR.prototype.doWrite = function (data, fn) {\n  var isBinary = typeof data !== 'string' && data !== undefined;\n  var req = this.request({ method: 'POST', data: data, isBinary: isBinary });\n  var self = this;\n  req.on('success', fn);\n  req.on('error', function (err) {\n    self.onError('xhr post error', err);\n  });\n  this.sendXhr = req;\n};\n\n/**\n * Starts a poll cycle.\n *\n * @api private\n */\n\nXHR.prototype.doPoll = function () {\n\n  var req = this.request();\n  var self = this;\n  req.on('data', function (data) {\n    self.onData(data);\n  });\n  req.on('error', function (err) {\n    self.onError('xhr poll error', err);\n  });\n  this.pollXhr = req;\n};\n\n/**\n * Request constructor\n *\n * @param {Object} options\n * @api public\n */\n\nfunction Request (opts) {\n  this.method = opts.method || 'GET';\n  this.uri = opts.uri;\n  this.xd = !!opts.xd;\n  this.xs = !!opts.xs;\n  this.async = false !== opts.async;\n  this.data = undefined !== opts.data ? opts.data : null;\n  this.agent = opts.agent;\n  this.isBinary = opts.isBinary;\n  this.supportsBinary = opts.supportsBinary;\n  this.enablesXDR = opts.enablesXDR;\n  this.withCredentials = opts.withCredentials;\n  this.requestTimeout = opts.requestTimeout;\n\n  // SSL options for Node.js client\n  this.pfx = opts.pfx;\n  this.key = opts.key;\n  this.passphrase = opts.passphrase;\n  this.cert = opts.cert;\n  this.ca = opts.ca;\n  this.ciphers = opts.ciphers;\n  this.rejectUnauthorized = opts.rejectUnauthorized;\n\n  // other options for Node.js client\n  this.extraHeaders = opts.extraHeaders;\n\n  this.create();\n}\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Request.prototype);\n\n/**\n * Creates the XHR object and sends the request.\n *\n * @api private\n */\n\nRequest.prototype.create = function () {\n  var opts = { agent: this.agent, xdomain: this.xd, xscheme: this.xs, enablesXDR: this.enablesXDR };\n\n  // SSL options for Node.js client\n  opts.pfx = this.pfx;\n  opts.key = this.key;\n  opts.passphrase = this.passphrase;\n  opts.cert = this.cert;\n  opts.ca = this.ca;\n  opts.ciphers = this.ciphers;\n  opts.rejectUnauthorized = this.rejectUnauthorized;\n\n  var xhr = this.xhr = new XMLHttpRequest(opts);\n  var self = this;\n\n  try {\n\n    xhr.open(this.method, this.uri, this.async);\n    try {\n      if (this.extraHeaders) {\n        xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n        for (var i in this.extraHeaders) {\n          if (this.extraHeaders.hasOwnProperty(i)) {\n            xhr.setRequestHeader(i, this.extraHeaders[i]);\n          }\n        }\n      }\n    } catch (e) {}\n\n    if ('POST' === this.method) {\n      try {\n        if (this.isBinary) {\n          xhr.setRequestHeader('Content-type', 'application/octet-stream');\n        } else {\n          xhr.setRequestHeader('Content-type', 'text/plain;charset=UTF-8');\n        }\n      } catch (e) {}\n    }\n\n    try {\n      xhr.setRequestHeader('Accept', '*/*');\n    } catch (e) {}\n\n    // ie6 check\n    if ('withCredentials' in xhr) {\n      xhr.withCredentials = this.withCredentials;\n    }\n\n    if (this.requestTimeout) {\n      xhr.timeout = this.requestTimeout;\n    }\n\n    if (this.hasXDR()) {\n      xhr.onload = function () {\n        self.onLoad();\n      };\n      xhr.onerror = function () {\n        self.onError(xhr.responseText);\n      };\n    } else {\n      xhr.onreadystatechange = function () {\n        if (xhr.readyState === 2) {\n          try {\n            var contentType = xhr.getResponseHeader('Content-Type');\n            if (self.supportsBinary && contentType === 'application/octet-stream' || contentType === 'application/octet-stream; charset=UTF-8') {\n              xhr.responseType = 'arraybuffer';\n            }\n          } catch (e) {}\n        }\n        if (4 !== xhr.readyState) return;\n        if (200 === xhr.status || 1223 === xhr.status) {\n          self.onLoad();\n        } else {\n          // make sure the `error` event handler that's user-set\n          // does not throw in the same tick and gets caught here\n          setTimeout(function () {\n            self.onError(typeof xhr.status === 'number' ? xhr.status : 0);\n          }, 0);\n        }\n      };\n    }\n\n\n    xhr.send(this.data);\n  } catch (e) {\n    // Need to defer since .create() is called directly fhrom the constructor\n    // and thus the 'error' event can only be only bound *after* this exception\n    // occurs.  Therefore, also, we cannot throw here at all.\n    setTimeout(function () {\n      self.onError(e);\n    }, 0);\n    return;\n  }\n\n  if (typeof document !== 'undefined') {\n    this.index = Request.requestsCount++;\n    Request.requests[this.index] = this;\n  }\n};\n\n/**\n * Called upon successful response.\n *\n * @api private\n */\n\nRequest.prototype.onSuccess = function () {\n  this.emit('success');\n  this.cleanup();\n};\n\n/**\n * Called if we have data.\n *\n * @api private\n */\n\nRequest.prototype.onData = function (data) {\n  this.emit('data', data);\n  this.onSuccess();\n};\n\n/**\n * Called upon error.\n *\n * @api private\n */\n\nRequest.prototype.onError = function (err) {\n  this.emit('error', err);\n  this.cleanup(true);\n};\n\n/**\n * Cleans up house.\n *\n * @api private\n */\n\nRequest.prototype.cleanup = function (fromError) {\n  if ('undefined' === typeof this.xhr || null === this.xhr) {\n    return;\n  }\n  // xmlhttprequest\n  if (this.hasXDR()) {\n    this.xhr.onload = this.xhr.onerror = empty;\n  } else {\n    this.xhr.onreadystatechange = empty;\n  }\n\n  if (fromError) {\n    try {\n      this.xhr.abort();\n    } catch (e) {}\n  }\n\n  if (typeof document !== 'undefined') {\n    delete Request.requests[this.index];\n  }\n\n  this.xhr = null;\n};\n\n/**\n * Called upon load.\n *\n * @api private\n */\n\nRequest.prototype.onLoad = function () {\n  var data;\n  try {\n    var contentType;\n    try {\n      contentType = this.xhr.getResponseHeader('Content-Type');\n    } catch (e) {}\n    if (contentType === 'application/octet-stream' || contentType === 'application/octet-stream; charset=UTF-8') {\n      data = this.xhr.response || this.xhr.responseText;\n    } else {\n      data = this.xhr.responseText;\n    }\n  } catch (e) {\n    this.onError(e);\n  }\n  if (null != data) {\n    this.onData(data);\n  }\n};\n\n/**\n * Check if it has XDomainRequest.\n *\n * @api private\n */\n\nRequest.prototype.hasXDR = function () {\n  return typeof XDomainRequest !== 'undefined' && !this.xs && this.enablesXDR;\n};\n\n/**\n * Aborts the request.\n *\n * @api public\n */\n\nRequest.prototype.abort = function () {\n  this.cleanup();\n};\n\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\n\nRequest.requestsCount = 0;\nRequest.requests = {};\n\nif (typeof document !== 'undefined') {\n  if (typeof attachEvent === 'function') {\n    attachEvent('onunload', unloadHandler);\n  } else if (typeof addEventListener === 'function') {\n    var terminationEvent = 'onpagehide' in globalThis ? 'pagehide' : 'unload';\n    addEventListener(terminationEvent, unloadHandler, false);\n  }\n}\n\nfunction unloadHandler () {\n  for (var i in Request.requests) {\n    if (Request.requests.hasOwnProperty(i)) {\n      Request.requests[i].abort();\n    }\n  }\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/polling-xhr.js\n// module id = 16\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar Transport = require('../transport');\nvar parseqs = require('parseqs');\nvar parser = require('engine.io-parser');\nvar inherit = require('component-inherit');\nvar yeast = require('yeast');\nvar debug = require('debug')('engine.io-client:polling');\n\n/**\n * Module exports.\n */\n\nmodule.exports = Polling;\n\n/**\n * Is XHR2 supported?\n */\n\nvar hasXHR2 = (function () {\n  var XMLHttpRequest = require('xmlhttprequest-ssl');\n  var xhr = new XMLHttpRequest({ xdomain: false });\n  return null != xhr.responseType;\n})();\n\n/**\n * Polling interface.\n *\n * @param {Object} opts\n * @api private\n */\n\nfunction Polling (opts) {\n  var forceBase64 = (opts && opts.forceBase64);\n  if (!hasXHR2 || forceBase64) {\n    this.supportsBinary = false;\n  }\n  Transport.call(this, opts);\n}\n\n/**\n * Inherits from Transport.\n */\n\ninherit(Polling, Transport);\n\n/**\n * Transport name.\n */\n\nPolling.prototype.name = 'polling';\n\n/**\n * Opens the socket (triggers polling). We write a PING message to determine\n * when the transport is open.\n *\n * @api private\n */\n\nPolling.prototype.doOpen = function () {\n  this.poll();\n};\n\n/**\n * Pauses polling.\n *\n * @param {Function} callback upon buffers are flushed and transport is paused\n * @api private\n */\n\nPolling.prototype.pause = function (onPause) {\n  var self = this;\n\n  this.readyState = 'pausing';\n\n  function pause () {\n\n    self.readyState = 'paused';\n    onPause();\n  }\n\n  if (this.polling || !this.writable) {\n    var total = 0;\n\n    if (this.polling) {\n\n      total++;\n      this.once('pollComplete', function () {\n\n        --total || pause();\n      });\n    }\n\n    if (!this.writable) {\n\n      total++;\n      this.once('drain', function () {\n\n        --total || pause();\n      });\n    }\n  } else {\n    pause();\n  }\n};\n\n/**\n * Starts polling cycle.\n *\n * @api public\n */\n\nPolling.prototype.poll = function () {\n\n  this.polling = true;\n  this.doPoll();\n  this.emit('poll');\n};\n\n/**\n * Overloads onData to detect payloads.\n *\n * @api private\n */\n\nPolling.prototype.onData = function (data) {\n  var self = this;\n\n  var callback = function (packet, index, total) {\n    // if its the first message we consider the transport open\n    if ('opening' === self.readyState && packet.type === 'open') {\n      self.onOpen();\n    }\n\n    // if its a close packet, we close the ongoing requests\n    if ('close' === packet.type) {\n      self.onClose();\n      return false;\n    }\n\n    // otherwise bypass onData and handle the message\n    self.onPacket(packet);\n  };\n\n  // decode payload\n  parser.decodePayload(data, this.socket.binaryType, callback);\n\n  // if an event did not trigger closing\n  if ('closed' !== this.readyState) {\n    // if we got data we're not polling\n    this.polling = false;\n    this.emit('pollComplete');\n\n    if ('open' === this.readyState) {\n      this.poll();\n    } else {\n\n    }\n  }\n};\n\n/**\n * For polling, send a close packet.\n *\n * @api private\n */\n\nPolling.prototype.doClose = function () {\n  var self = this;\n\n  function close () {\n\n    self.write([{ type: 'close' }]);\n  }\n\n  if ('open' === this.readyState) {\n\n    close();\n  } else {\n    // in case we're trying to close while\n    // handshaking is in progress (GH-164)\n\n    this.once('open', close);\n  }\n};\n\n/**\n * Writes a packets payload.\n *\n * @param {Array} data packets\n * @param {Function} drain callback\n * @api private\n */\n\nPolling.prototype.write = function (packets) {\n  var self = this;\n  this.writable = false;\n  var callbackfn = function () {\n    self.writable = true;\n    self.emit('drain');\n  };\n\n  parser.encodePayload(packets, this.supportsBinary, function (data) {\n    self.doWrite(data, callbackfn);\n  });\n};\n\n/**\n * Generates uri for connection.\n *\n * @api private\n */\n\nPolling.prototype.uri = function () {\n  var query = this.query || {};\n  var schema = this.secure ? 'https' : 'http';\n  var port = '';\n\n  // cache busting is forced\n  if (false !== this.timestampRequests) {\n    query[this.timestampParam] = yeast();\n  }\n\n  if (!this.supportsBinary && !query.sid) {\n    query.b64 = 1;\n  }\n\n  query = parseqs.encode(query);\n\n  // avoid port if default for schema\n  if (this.port && (('https' === schema && Number(this.port) !== 443) ||\n     ('http' === schema && Number(this.port) !== 80))) {\n    port = ':' + this.port;\n  }\n\n  // prepend ? to query\n  if (query.length) {\n    query = '?' + query;\n  }\n\n  var ipv6 = this.hostname.indexOf(':') !== -1;\n  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/polling.js\n// module id = 17\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar parser = require('engine.io-parser');\nvar Emitter = require('component-emitter');\n\n/**\n * Module exports.\n */\n\nmodule.exports = Transport;\n\n/**\n * Transport abstract constructor.\n *\n * @param {Object} options.\n * @api private\n */\n\nfunction Transport (opts) {\n  this.path = opts.path;\n  this.hostname = opts.hostname;\n  this.port = opts.port;\n  this.secure = opts.secure;\n  this.query = opts.query;\n  this.timestampParam = opts.timestampParam;\n  this.timestampRequests = opts.timestampRequests;\n  this.readyState = '';\n  this.agent = opts.agent || false;\n  this.socket = opts.socket;\n  this.enablesXDR = opts.enablesXDR;\n  this.withCredentials = opts.withCredentials;\n\n  // SSL options for Node.js client\n  this.pfx = opts.pfx;\n  this.key = opts.key;\n  this.passphrase = opts.passphrase;\n  this.cert = opts.cert;\n  this.ca = opts.ca;\n  this.ciphers = opts.ciphers;\n  this.rejectUnauthorized = opts.rejectUnauthorized;\n  this.forceNode = opts.forceNode;\n\n  // results of ReactNative environment detection\n  this.isReactNative = opts.isReactNative;\n\n  // other options for Node.js client\n  this.extraHeaders = opts.extraHeaders;\n  this.localAddress = opts.localAddress;\n}\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Transport.prototype);\n\n/**\n * Emits an error.\n *\n * @param {String} str\n * @return {Transport} for chaining\n * @api public\n */\n\nTransport.prototype.onError = function (msg, desc) {\n  var err = new Error(msg);\n  err.type = 'TransportError';\n  err.description = desc;\n  this.emit('error', err);\n  return this;\n};\n\n/**\n * Opens the transport.\n *\n * @api public\n */\n\nTransport.prototype.open = function () {\n  if ('closed' === this.readyState || '' === this.readyState) {\n    this.readyState = 'opening';\n    this.doOpen();\n  }\n\n  return this;\n};\n\n/**\n * Closes the transport.\n *\n * @api private\n */\n\nTransport.prototype.close = function () {\n  if ('opening' === this.readyState || 'open' === this.readyState) {\n    this.doClose();\n    this.onClose();\n  }\n\n  return this;\n};\n\n/**\n * Sends multiple packets.\n *\n * @param {Array} packets\n * @api private\n */\n\nTransport.prototype.send = function (packets) {\n  if ('open' === this.readyState) {\n    this.write(packets);\n  } else {\n    throw new Error('Transport not open');\n  }\n};\n\n/**\n * Called upon open\n *\n * @api private\n */\n\nTransport.prototype.onOpen = function () {\n  this.readyState = 'open';\n  this.writable = true;\n  this.emit('open');\n};\n\n/**\n * Called with data.\n *\n * @param {String} data\n * @api private\n */\n\nTransport.prototype.onData = function (data) {\n  var packet = parser.decodePacket(data, this.socket.binaryType);\n  this.onPacket(packet);\n};\n\n/**\n * Called with a decoded packet.\n */\n\nTransport.prototype.onPacket = function (packet) {\n  this.emit('packet', packet);\n};\n\n/**\n * Called upon close.\n *\n * @api private\n */\n\nTransport.prototype.onClose = function () {\n  this.readyState = 'closed';\n  this.emit('close');\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transport.js\n// module id = 18\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar keys = require('./keys');\nvar hasBinary = require('has-binary2');\nvar sliceBuffer = require('arraybuffer.slice');\nvar after = require('after');\nvar utf8 = require('./utf8');\n\nvar base64encoder;\nif (typeof ArrayBuffer !== 'undefined') {\n  base64encoder = require('base64-arraybuffer');\n}\n\n/**\n * Check if we are running an android browser. That requires us to use\n * ArrayBuffer with polling transports...\n *\n * http://ghinda.net/jpeg-blob-ajax-android/\n */\n\nvar isAndroid = typeof navigator !== 'undefined' && /Android/i.test(navigator.userAgent);\n\n/**\n * Check if we are running in PhantomJS.\n * Uploading a Blob with PhantomJS does not work correctly, as reported here:\n * https://github.com/ariya/phantomjs/issues/11395\n * @type boolean\n */\nvar isPhantomJS = typeof navigator !== 'undefined' && /PhantomJS/i.test(navigator.userAgent);\n\n/**\n * When true, avoids using Blobs to encode payloads.\n * @type boolean\n */\nvar dontSendBlobs = isAndroid || isPhantomJS;\n\n/**\n * Current protocol version.\n */\n\nexports.protocol = 3;\n\n/**\n * Packet types.\n */\n\nvar packets = exports.packets = {\n    open:     0    // non-ws\n  , close:    1    // non-ws\n  , ping:     2\n  , pong:     3\n  , message:  4\n  , upgrade:  5\n  , noop:     6\n};\n\nvar packetslist = keys(packets);\n\n/**\n * Premade error packet.\n */\n\nvar err = { type: 'error', data: 'parser error' };\n\n/**\n * Create a blob api even for blob builder when vendor prefixes exist\n */\n\nvar Blob = require('blob');\n\n/**\n * Encodes a packet.\n *\n *     <packet type id> [ <data> ]\n *\n * Example:\n *\n *     5hello world\n *     3\n *     4\n *\n * Binary is encoded in an identical principle\n *\n * @api private\n */\n\nexports.encodePacket = function (packet, supportsBinary, utf8encode, callback) {\n  if (typeof supportsBinary === 'function') {\n    callback = supportsBinary;\n    supportsBinary = false;\n  }\n\n  if (typeof utf8encode === 'function') {\n    callback = utf8encode;\n    utf8encode = null;\n  }\n\n  var data = (packet.data === undefined)\n    ? undefined\n    : packet.data.buffer || packet.data;\n\n  if (typeof ArrayBuffer !== 'undefined' && data instanceof ArrayBuffer) {\n    return encodeArrayBuffer(packet, supportsBinary, callback);\n  } else if (typeof Blob !== 'undefined' && data instanceof Blob) {\n    return encodeBlob(packet, supportsBinary, callback);\n  }\n\n  // might be an object with { base64: true, data: dataAsBase64String }\n  if (data && data.base64) {\n    return encodeBase64Object(packet, callback);\n  }\n\n  // Sending data as a utf-8 string\n  var encoded = packets[packet.type];\n\n  // data fragment is optional\n  if (undefined !== packet.data) {\n    encoded += utf8encode ? utf8.encode(String(packet.data), { strict: false }) : String(packet.data);\n  }\n\n  return callback('' + encoded);\n\n};\n\nfunction encodeBase64Object(packet, callback) {\n  // packet data is an object { base64: true, data: dataAsBase64String }\n  var message = 'b' + exports.packets[packet.type] + packet.data.data;\n  return callback(message);\n}\n\n/**\n * Encode packet helpers for binary types\n */\n\nfunction encodeArrayBuffer(packet, supportsBinary, callback) {\n  if (!supportsBinary) {\n    return exports.encodeBase64Packet(packet, callback);\n  }\n\n  var data = packet.data;\n  var contentArray = new Uint8Array(data);\n  var resultBuffer = new Uint8Array(1 + data.byteLength);\n\n  resultBuffer[0] = packets[packet.type];\n  for (var i = 0; i < contentArray.length; i++) {\n    resultBuffer[i+1] = contentArray[i];\n  }\n\n  return callback(resultBuffer.buffer);\n}\n\nfunction encodeBlobAsArrayBuffer(packet, supportsBinary, callback) {\n  if (!supportsBinary) {\n    return exports.encodeBase64Packet(packet, callback);\n  }\n\n  var fr = new FileReader();\n  fr.onload = function() {\n    exports.encodePacket({ type: packet.type, data: fr.result }, supportsBinary, true, callback);\n  };\n  return fr.readAsArrayBuffer(packet.data);\n}\n\nfunction encodeBlob(packet, supportsBinary, callback) {\n  if (!supportsBinary) {\n    return exports.encodeBase64Packet(packet, callback);\n  }\n\n  if (dontSendBlobs) {\n    return encodeBlobAsArrayBuffer(packet, supportsBinary, callback);\n  }\n\n  var length = new Uint8Array(1);\n  length[0] = packets[packet.type];\n  var blob = new Blob([length.buffer, packet.data]);\n\n  return callback(blob);\n}\n\n/**\n * Encodes a packet with binary data in a base64 string\n *\n * @param {Object} packet, has `type` and `data`\n * @return {String} base64 encoded message\n */\n\nexports.encodeBase64Packet = function(packet, callback) {\n  var message = 'b' + exports.packets[packet.type];\n  if (typeof Blob !== 'undefined' && packet.data instanceof Blob) {\n    var fr = new FileReader();\n    fr.onload = function() {\n      var b64 = fr.result.split(',')[1];\n      callback(message + b64);\n    };\n    return fr.readAsDataURL(packet.data);\n  }\n\n  var b64data;\n  try {\n    b64data = String.fromCharCode.apply(null, new Uint8Array(packet.data));\n  } catch (e) {\n    // iPhone Safari doesn't let you apply with typed arrays\n    var typed = new Uint8Array(packet.data);\n    var basic = new Array(typed.length);\n    for (var i = 0; i < typed.length; i++) {\n      basic[i] = typed[i];\n    }\n    b64data = String.fromCharCode.apply(null, basic);\n  }\n  message += btoa(b64data);\n  return callback(message);\n};\n\n/**\n * Decodes a packet. Changes format to Blob if requested.\n *\n * @return {Object} with `type` and `data` (if any)\n * @api private\n */\n\nexports.decodePacket = function (data, binaryType, utf8decode) {\n  if (data === undefined) {\n    return err;\n  }\n  // String data\n  if (typeof data === 'string') {\n    if (data.charAt(0) === 'b') {\n      return exports.decodeBase64Packet(data.substr(1), binaryType);\n    }\n\n    if (utf8decode) {\n      data = tryDecode(data);\n      if (data === false) {\n        return err;\n      }\n    }\n    var type = data.charAt(0);\n\n    if (Number(type) != type || !packetslist[type]) {\n      return err;\n    }\n\n    if (data.length > 1) {\n      return { type: packetslist[type], data: data.substring(1) };\n    } else {\n      return { type: packetslist[type] };\n    }\n  }\n\n  var asArray = new Uint8Array(data);\n  var type = asArray[0];\n  var rest = sliceBuffer(data, 1);\n  if (Blob && binaryType === 'blob') {\n    rest = new Blob([rest]);\n  }\n  return { type: packetslist[type], data: rest };\n};\n\nfunction tryDecode(data) {\n  try {\n    data = utf8.decode(data, { strict: false });\n  } catch (e) {\n    return false;\n  }\n  return data;\n}\n\n/**\n * Decodes a packet encoded in a base64 string\n *\n * @param {String} base64 encoded message\n * @return {Object} with `type` and `data` (if any)\n */\n\nexports.decodeBase64Packet = function(msg, binaryType) {\n  var type = packetslist[msg.charAt(0)];\n  if (!base64encoder) {\n    return { type: type, data: { base64: true, data: msg.substr(1) } };\n  }\n\n  var data = base64encoder.decode(msg.substr(1));\n\n  if (binaryType === 'blob' && Blob) {\n    data = new Blob([data]);\n  }\n\n  return { type: type, data: data };\n};\n\n/**\n * Encodes multiple messages (payload).\n *\n *     <length>:data\n *\n * Example:\n *\n *     11:hello world2:hi\n *\n * If any contents are binary, they will be encoded as base64 strings. Base64\n * encoded strings are marked with a b before the length specifier\n *\n * @param {Array} packets\n * @api private\n */\n\nexports.encodePayload = function (packets, supportsBinary, callback) {\n  if (typeof supportsBinary === 'function') {\n    callback = supportsBinary;\n    supportsBinary = null;\n  }\n\n  var isBinary = hasBinary(packets);\n\n  if (supportsBinary && isBinary) {\n    if (Blob && !dontSendBlobs) {\n      return exports.encodePayloadAsBlob(packets, callback);\n    }\n\n    return exports.encodePayloadAsArrayBuffer(packets, callback);\n  }\n\n  if (!packets.length) {\n    return callback('0:');\n  }\n\n  function setLengthHeader(message) {\n    return message.length + ':' + message;\n  }\n\n  function encodeOne(packet, doneCallback) {\n    exports.encodePacket(packet, !isBinary ? false : supportsBinary, false, function(message) {\n      doneCallback(null, setLengthHeader(message));\n    });\n  }\n\n  map(packets, encodeOne, function(err, results) {\n    return callback(results.join(''));\n  });\n};\n\n/**\n * Async array map using after\n */\n\nfunction map(ary, each, done) {\n  var result = new Array(ary.length);\n  var next = after(ary.length, done);\n\n  var eachWithIndex = function(i, el, cb) {\n    each(el, function(error, msg) {\n      result[i] = msg;\n      cb(error, result);\n    });\n  };\n\n  for (var i = 0; i < ary.length; i++) {\n    eachWithIndex(i, ary[i], next);\n  }\n}\n\n/*\n * Decodes data when a payload is maybe expected. Possible binary contents are\n * decoded from their base64 representation\n *\n * @param {String} data, callback method\n * @api public\n */\n\nexports.decodePayload = function (data, binaryType, callback) {\n  if (typeof data !== 'string') {\n    return exports.decodePayloadAsBinary(data, binaryType, callback);\n  }\n\n  if (typeof binaryType === 'function') {\n    callback = binaryType;\n    binaryType = null;\n  }\n\n  var packet;\n  if (data === '') {\n    // parser error - ignoring payload\n    return callback(err, 0, 1);\n  }\n\n  var length = '', n, msg;\n\n  for (var i = 0, l = data.length; i < l; i++) {\n    var chr = data.charAt(i);\n\n    if (chr !== ':') {\n      length += chr;\n      continue;\n    }\n\n    if (length === '' || (length != (n = Number(length)))) {\n      // parser error - ignoring payload\n      return callback(err, 0, 1);\n    }\n\n    msg = data.substr(i + 1, n);\n\n    if (length != msg.length) {\n      // parser error - ignoring payload\n      return callback(err, 0, 1);\n    }\n\n    if (msg.length) {\n      packet = exports.decodePacket(msg, binaryType, false);\n\n      if (err.type === packet.type && err.data === packet.data) {\n        // parser error in individual packet - ignoring payload\n        return callback(err, 0, 1);\n      }\n\n      var ret = callback(packet, i + n, l);\n      if (false === ret) return;\n    }\n\n    // advance cursor\n    i += n;\n    length = '';\n  }\n\n  if (length !== '') {\n    // parser error - ignoring payload\n    return callback(err, 0, 1);\n  }\n\n};\n\n/**\n * Encodes multiple messages (payload) as binary.\n *\n * <1 = binary, 0 = string><number from 0-9><number from 0-9>[...]<number\n * 255><data>\n *\n * Example:\n * 1 3 255 1 2 3, if the binary contents are interpreted as 8 bit integers\n *\n * @param {Array} packets\n * @return {ArrayBuffer} encoded payload\n * @api private\n */\n\nexports.encodePayloadAsArrayBuffer = function(packets, callback) {\n  if (!packets.length) {\n    return callback(new ArrayBuffer(0));\n  }\n\n  function encodeOne(packet, doneCallback) {\n    exports.encodePacket(packet, true, true, function(data) {\n      return doneCallback(null, data);\n    });\n  }\n\n  map(packets, encodeOne, function(err, encodedPackets) {\n    var totalLength = encodedPackets.reduce(function(acc, p) {\n      var len;\n      if (typeof p === 'string'){\n        len = p.length;\n      } else {\n        len = p.byteLength;\n      }\n      return acc + len.toString().length + len + 2; // string/binary identifier + separator = 2\n    }, 0);\n\n    var resultArray = new Uint8Array(totalLength);\n\n    var bufferIndex = 0;\n    encodedPackets.forEach(function(p) {\n      var isString = typeof p === 'string';\n      var ab = p;\n      if (isString) {\n        var view = new Uint8Array(p.length);\n        for (var i = 0; i < p.length; i++) {\n          view[i] = p.charCodeAt(i);\n        }\n        ab = view.buffer;\n      }\n\n      if (isString) { // not true binary\n        resultArray[bufferIndex++] = 0;\n      } else { // true binary\n        resultArray[bufferIndex++] = 1;\n      }\n\n      var lenStr = ab.byteLength.toString();\n      for (var i = 0; i < lenStr.length; i++) {\n        resultArray[bufferIndex++] = parseInt(lenStr[i]);\n      }\n      resultArray[bufferIndex++] = 255;\n\n      var view = new Uint8Array(ab);\n      for (var i = 0; i < view.length; i++) {\n        resultArray[bufferIndex++] = view[i];\n      }\n    });\n\n    return callback(resultArray.buffer);\n  });\n};\n\n/**\n * Encode as Blob\n */\n\nexports.encodePayloadAsBlob = function(packets, callback) {\n  function encodeOne(packet, doneCallback) {\n    exports.encodePacket(packet, true, true, function(encoded) {\n      var binaryIdentifier = new Uint8Array(1);\n      binaryIdentifier[0] = 1;\n      if (typeof encoded === 'string') {\n        var view = new Uint8Array(encoded.length);\n        for (var i = 0; i < encoded.length; i++) {\n          view[i] = encoded.charCodeAt(i);\n        }\n        encoded = view.buffer;\n        binaryIdentifier[0] = 0;\n      }\n\n      var len = (encoded instanceof ArrayBuffer)\n        ? encoded.byteLength\n        : encoded.size;\n\n      var lenStr = len.toString();\n      var lengthAry = new Uint8Array(lenStr.length + 1);\n      for (var i = 0; i < lenStr.length; i++) {\n        lengthAry[i] = parseInt(lenStr[i]);\n      }\n      lengthAry[lenStr.length] = 255;\n\n      if (Blob) {\n        var blob = new Blob([binaryIdentifier.buffer, lengthAry.buffer, encoded]);\n        doneCallback(null, blob);\n      }\n    });\n  }\n\n  map(packets, encodeOne, function(err, results) {\n    return callback(new Blob(results));\n  });\n};\n\n/*\n * Decodes data when a payload is maybe expected. Strings are decoded by\n * interpreting each byte as a key code for entries marked to start with 0. See\n * description of encodePayloadAsBinary\n *\n * @param {ArrayBuffer} data, callback method\n * @api public\n */\n\nexports.decodePayloadAsBinary = function (data, binaryType, callback) {\n  if (typeof binaryType === 'function') {\n    callback = binaryType;\n    binaryType = null;\n  }\n\n  var bufferTail = data;\n  var buffers = [];\n\n  while (bufferTail.byteLength > 0) {\n    var tailArray = new Uint8Array(bufferTail);\n    var isString = tailArray[0] === 0;\n    var msgLength = '';\n\n    for (var i = 1; ; i++) {\n      if (tailArray[i] === 255) break;\n\n      // 310 = char length of Number.MAX_VALUE\n      if (msgLength.length > 310) {\n        return callback(err, 0, 1);\n      }\n\n      msgLength += tailArray[i];\n    }\n\n    bufferTail = sliceBuffer(bufferTail, 2 + msgLength.length);\n    msgLength = parseInt(msgLength);\n\n    var msg = sliceBuffer(bufferTail, 0, msgLength);\n    if (isString) {\n      try {\n        msg = String.fromCharCode.apply(null, new Uint8Array(msg));\n      } catch (e) {\n        // iPhone Safari doesn't let you apply to typed arrays\n        var typed = new Uint8Array(msg);\n        msg = '';\n        for (var i = 0; i < typed.length; i++) {\n          msg += String.fromCharCode(typed[i]);\n        }\n      }\n    }\n\n    buffers.push(msg);\n    bufferTail = sliceBuffer(bufferTail, msgLength);\n  }\n\n  var total = buffers.length;\n  buffers.forEach(function(buffer, i) {\n    callback(exports.decodePacket(buffer, binaryType, true), i, total);\n  });\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-parser/lib/browser.js\n// module id = 19\n// module chunks = 0", "\n/**\n * Gets the keys for an object.\n *\n * @return {Array} keys\n * @api private\n */\n\nmodule.exports = Object.keys || function keys (obj){\n  var arr = [];\n  var has = Object.prototype.hasOwnProperty;\n\n  for (var i in obj) {\n    if (has.call(obj, i)) {\n      arr.push(i);\n    }\n  }\n  return arr;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-parser/lib/keys.js\n// module id = 20\n// module chunks = 0", "/* global Blob File */\n\n/*\n * Module requirements.\n */\n\nvar isArray = require('isarray');\n\nvar toString = Object.prototype.toString;\nvar withNativeBlob = typeof Blob === 'function' ||\n                        typeof Blob !== 'undefined' && toString.call(Blob) === '[object BlobConstructor]';\nvar withNativeFile = typeof File === 'function' ||\n                        typeof File !== 'undefined' && toString.call(File) === '[object FileConstructor]';\n\n/**\n * Module exports.\n */\n\nmodule.exports = hasBinary;\n\n/**\n * Checks for binary data.\n *\n * Supports Buffer, ArrayBuffer, Blob and File.\n *\n * @param {Object} anything\n * @api public\n */\n\nfunction hasBinary (obj) {\n  if (!obj || typeof obj !== 'object') {\n    return false;\n  }\n\n  if (isArray(obj)) {\n    for (var i = 0, l = obj.length; i < l; i++) {\n      if (hasBinary(obj[i])) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  if ((typeof Buffer === 'function' && Buffer.isBuffer && Buffer.isBuffer(obj)) ||\n    (typeof ArrayBuffer === 'function' && obj instanceof ArrayBuffer) ||\n    (withNativeBlob && obj instanceof Blob) ||\n    (withNativeFile && obj instanceof File)\n  ) {\n    return true;\n  }\n\n  // see: https://github.com/Automattic/has-binary/pull/4\n  if (obj.toJSON && typeof obj.toJSON === 'function' && arguments.length === 1) {\n    return hasBinary(obj.toJSON(), true);\n  }\n\n  for (var key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/has-binary2/index.js\n// module id = 21\n// module chunks = 0", "/**\n * An abstraction for slicing an arraybuffer even when\n * ArrayBuffer.prototype.slice is not supported\n *\n * @api public\n */\n\nmodule.exports = function(arraybuffer, start, end) {\n  var bytes = arraybuffer.byteLength;\n  start = start || 0;\n  end = end || bytes;\n\n  if (arraybuffer.slice) { return arraybuffer.slice(start, end); }\n\n  if (start < 0) { start += bytes; }\n  if (end < 0) { end += bytes; }\n  if (end > bytes) { end = bytes; }\n\n  if (start >= bytes || start >= end || bytes === 0) {\n    return new ArrayBuffer(0);\n  }\n\n  var abv = new Uint8Array(arraybuffer);\n  var result = new Uint8Array(end - start);\n  for (var i = start, ii = 0; i < end; i++, ii++) {\n    result[ii] = abv[i];\n  }\n  return result.buffer;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/arraybuffer.slice/index.js\n// module id = 22\n// module chunks = 0", "module.exports = after\n\nfunction after(count, callback, err_cb) {\n    var bail = false\n    err_cb = err_cb || noop\n    proxy.count = count\n\n    return (count === 0) ? callback() : proxy\n\n    function proxy(err, result) {\n        if (proxy.count <= 0) {\n            throw new Error('after called too many times')\n        }\n        --proxy.count\n\n        // after first error, rest are passed to err_cb\n        if (err) {\n            bail = true\n            callback(err)\n            // future error callbacks will go to error handler\n            callback = err_cb\n        } else if (proxy.count === 0 && !bail) {\n            callback(null, result)\n        }\n    }\n}\n\nfunction noop() {}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/after/index.js\n// module id = 23\n// module chunks = 0", "/*! https://mths.be/utf8js v2.1.2 by @mathias */\n\nvar stringFromCharCode = String.fromCharCode;\n\n// Taken from https://mths.be/punycode\nfunction ucs2decode(string) {\n\tvar output = [];\n\tvar counter = 0;\n\tvar length = string.length;\n\tvar value;\n\tvar extra;\n\twhile (counter < length) {\n\t\tvalue = string.charCodeAt(counter++);\n\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t// high surrogate, and there is a next character\n\t\t\textra = string.charCodeAt(counter++);\n\t\t\tif ((extra & 0xFC00) == 0xDC00) { // low surrogate\n\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t} else {\n\t\t\t\t// unmatched surrogate; only append this code unit, in case the next\n\t\t\t\t// code unit is the high surrogate of a surrogate pair\n\t\t\t\toutput.push(value);\n\t\t\t\tcounter--;\n\t\t\t}\n\t\t} else {\n\t\t\toutput.push(value);\n\t\t}\n\t}\n\treturn output;\n}\n\n// Taken from https://mths.be/punycode\nfunction ucs2encode(array) {\n\tvar length = array.length;\n\tvar index = -1;\n\tvar value;\n\tvar output = '';\n\twhile (++index < length) {\n\t\tvalue = array[index];\n\t\tif (value > 0xFFFF) {\n\t\t\tvalue -= 0x10000;\n\t\t\toutput += stringFromCharCode(value >>> 10 & 0x3FF | 0xD800);\n\t\t\tvalue = 0xDC00 | value & 0x3FF;\n\t\t}\n\t\toutput += stringFromCharCode(value);\n\t}\n\treturn output;\n}\n\nfunction checkScalarValue(codePoint, strict) {\n\tif (codePoint >= 0xD800 && codePoint <= 0xDFFF) {\n\t\tif (strict) {\n\t\t\tthrow Error(\n\t\t\t\t'Lone surrogate U+' + codePoint.toString(16).toUpperCase() +\n\t\t\t\t' is not a scalar value'\n\t\t\t);\n\t\t}\n\t\treturn false;\n\t}\n\treturn true;\n}\n/*--------------------------------------------------------------------------*/\n\nfunction createByte(codePoint, shift) {\n\treturn stringFromCharCode(((codePoint >> shift) & 0x3F) | 0x80);\n}\n\nfunction encodeCodePoint(codePoint, strict) {\n\tif ((codePoint & 0xFFFFFF80) == 0) { // 1-byte sequence\n\t\treturn stringFromCharCode(codePoint);\n\t}\n\tvar symbol = '';\n\tif ((codePoint & 0xFFFFF800) == 0) { // 2-byte sequence\n\t\tsymbol = stringFromCharCode(((codePoint >> 6) & 0x1F) | 0xC0);\n\t}\n\telse if ((codePoint & 0xFFFF0000) == 0) { // 3-byte sequence\n\t\tif (!checkScalarValue(codePoint, strict)) {\n\t\t\tcodePoint = 0xFFFD;\n\t\t}\n\t\tsymbol = stringFromCharCode(((codePoint >> 12) & 0x0F) | 0xE0);\n\t\tsymbol += createByte(codePoint, 6);\n\t}\n\telse if ((codePoint & 0xFFE00000) == 0) { // 4-byte sequence\n\t\tsymbol = stringFromCharCode(((codePoint >> 18) & 0x07) | 0xF0);\n\t\tsymbol += createByte(codePoint, 12);\n\t\tsymbol += createByte(codePoint, 6);\n\t}\n\tsymbol += stringFromCharCode((codePoint & 0x3F) | 0x80);\n\treturn symbol;\n}\n\nfunction utf8encode(string, opts) {\n\topts = opts || {};\n\tvar strict = false !== opts.strict;\n\n\tvar codePoints = ucs2decode(string);\n\tvar length = codePoints.length;\n\tvar index = -1;\n\tvar codePoint;\n\tvar byteString = '';\n\twhile (++index < length) {\n\t\tcodePoint = codePoints[index];\n\t\tbyteString += encodeCodePoint(codePoint, strict);\n\t}\n\treturn byteString;\n}\n\n/*--------------------------------------------------------------------------*/\n\nfunction readContinuationByte() {\n\tif (byteIndex >= byteCount) {\n\t\tthrow Error('Invalid byte index');\n\t}\n\n\tvar continuationByte = byteArray[byteIndex] & 0xFF;\n\tbyteIndex++;\n\n\tif ((continuationByte & 0xC0) == 0x80) {\n\t\treturn continuationByte & 0x3F;\n\t}\n\n\t// If we end up here, it’s not a continuation byte\n\tthrow Error('Invalid continuation byte');\n}\n\nfunction decodeSymbol(strict) {\n\tvar byte1;\n\tvar byte2;\n\tvar byte3;\n\tvar byte4;\n\tvar codePoint;\n\n\tif (byteIndex > byteCount) {\n\t\tthrow Error('Invalid byte index');\n\t}\n\n\tif (byteIndex == byteCount) {\n\t\treturn false;\n\t}\n\n\t// Read first byte\n\tbyte1 = byteArray[byteIndex] & 0xFF;\n\tbyteIndex++;\n\n\t// 1-byte sequence (no continuation bytes)\n\tif ((byte1 & 0x80) == 0) {\n\t\treturn byte1;\n\t}\n\n\t// 2-byte sequence\n\tif ((byte1 & 0xE0) == 0xC0) {\n\t\tbyte2 = readContinuationByte();\n\t\tcodePoint = ((byte1 & 0x1F) << 6) | byte2;\n\t\tif (codePoint >= 0x80) {\n\t\t\treturn codePoint;\n\t\t} else {\n\t\t\tthrow Error('Invalid continuation byte');\n\t\t}\n\t}\n\n\t// 3-byte sequence (may include unpaired surrogates)\n\tif ((byte1 & 0xF0) == 0xE0) {\n\t\tbyte2 = readContinuationByte();\n\t\tbyte3 = readContinuationByte();\n\t\tcodePoint = ((byte1 & 0x0F) << 12) | (byte2 << 6) | byte3;\n\t\tif (codePoint >= 0x0800) {\n\t\t\treturn checkScalarValue(codePoint, strict) ? codePoint : 0xFFFD;\n\t\t} else {\n\t\t\tthrow Error('Invalid continuation byte');\n\t\t}\n\t}\n\n\t// 4-byte sequence\n\tif ((byte1 & 0xF8) == 0xF0) {\n\t\tbyte2 = readContinuationByte();\n\t\tbyte3 = readContinuationByte();\n\t\tbyte4 = readContinuationByte();\n\t\tcodePoint = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0C) |\n\t\t\t(byte3 << 0x06) | byte4;\n\t\tif (codePoint >= 0x010000 && codePoint <= 0x10FFFF) {\n\t\t\treturn codePoint;\n\t\t}\n\t}\n\n\tthrow Error('Invalid UTF-8 detected');\n}\n\nvar byteArray;\nvar byteCount;\nvar byteIndex;\nfunction utf8decode(byteString, opts) {\n\topts = opts || {};\n\tvar strict = false !== opts.strict;\n\n\tbyteArray = ucs2decode(byteString);\n\tbyteCount = byteArray.length;\n\tbyteIndex = 0;\n\tvar codePoints = [];\n\tvar tmp;\n\twhile ((tmp = decodeSymbol(strict)) !== false) {\n\t\tcodePoints.push(tmp);\n\t}\n\treturn ucs2encode(codePoints);\n}\n\nmodule.exports = {\n\tversion: '2.1.2',\n\tencode: utf8encode,\n\tdecode: utf8decode\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-parser/lib/utf8.js\n// module id = 24\n// module chunks = 0", "/*\n * base64-arraybuffer\n * https://github.com/niklasvh/base64-arraybuffer\n *\n * Copyright (c) 2012 <PERSON><PERSON>\n * Licensed under the MIT license.\n */\n(function(chars){\n  \"use strict\";\n\n  exports.encode = function(arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer),\n    i, len = bytes.length, base64 = \"\";\n\n    for (i = 0; i < len; i+=3) {\n      base64 += chars[bytes[i] >> 2];\n      base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n      base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n      base64 += chars[bytes[i + 2] & 63];\n    }\n\n    if ((len % 3) === 2) {\n      base64 = base64.substring(0, base64.length - 1) + \"=\";\n    } else if (len % 3 === 1) {\n      base64 = base64.substring(0, base64.length - 2) + \"==\";\n    }\n\n    return base64;\n  };\n\n  exports.decode =  function(base64) {\n    var bufferLength = base64.length * 0.75,\n    len = base64.length, i, p = 0,\n    encoded1, encoded2, encoded3, encoded4;\n\n    if (base64[base64.length - 1] === \"=\") {\n      bufferLength--;\n      if (base64[base64.length - 2] === \"=\") {\n        bufferLength--;\n      }\n    }\n\n    var arraybuffer = new ArrayBuffer(bufferLength),\n    bytes = new Uint8Array(arraybuffer);\n\n    for (i = 0; i < len; i+=4) {\n      encoded1 = chars.indexOf(base64[i]);\n      encoded2 = chars.indexOf(base64[i+1]);\n      encoded3 = chars.indexOf(base64[i+2]);\n      encoded4 = chars.indexOf(base64[i+3]);\n\n      bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n      bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n      bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n\n    return arraybuffer;\n  };\n})(\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\");\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-parser/~/base64-arraybuffer/lib/base64-arraybuffer.js\n// module id = 25\n// module chunks = 0", "/**\r\n * Create a blob builder even when vendor prefixes exist\r\n */\r\n\r\nvar BlobBuilder = typeof BlobBuilder !== 'undefined' ? BlobBuilder :\r\n  typeof WebKitBlobBuilder !== 'undefined' ? WebKitBlobBuilder :\r\n  typeof MSBlobBuilder !== 'undefined' ? MSBlobBuilder :\r\n  typeof MozBlobBuilder !== 'undefined' ? MozBlobBuilder : \r\n  false;\r\n\r\n/**\r\n * Check if Blob constructor is supported\r\n */\r\n\r\nvar blobSupported = (function() {\r\n  try {\r\n    var a = new Blob(['hi']);\r\n    return a.size === 2;\r\n  } catch(e) {\r\n    return false;\r\n  }\r\n})();\r\n\r\n/**\r\n * Check if Blob constructor supports ArrayBufferViews\r\n * Fails in Safari 6, so we need to map to ArrayBuffers there.\r\n */\r\n\r\nvar blobSupportsArrayBufferView = blobSupported && (function() {\r\n  try {\r\n    var b = new Blob([new Uint8Array([1,2])]);\r\n    return b.size === 2;\r\n  } catch(e) {\r\n    return false;\r\n  }\r\n})();\r\n\r\n/**\r\n * Check if BlobBuilder is supported\r\n */\r\n\r\nvar blobBuilderSupported = BlobBuilder\r\n  && BlobBuilder.prototype.append\r\n  && BlobBuilder.prototype.getBlob;\r\n\r\n/**\r\n * Helper function that maps ArrayBufferViews to ArrayBuffers\r\n * Used by BlobBuilder constructor and old browsers that didn't\r\n * support it in the Blob constructor.\r\n */\r\n\r\nfunction mapArrayBufferViews(ary) {\r\n  return ary.map(function(chunk) {\r\n    if (chunk.buffer instanceof ArrayBuffer) {\r\n      var buf = chunk.buffer;\r\n\r\n      // if this is a subarray, make a copy so we only\r\n      // include the subarray region from the underlying buffer\r\n      if (chunk.byteLength !== buf.byteLength) {\r\n        var copy = new Uint8Array(chunk.byteLength);\r\n        copy.set(new Uint8Array(buf, chunk.byteOffset, chunk.byteLength));\r\n        buf = copy.buffer;\r\n      }\r\n\r\n      return buf;\r\n    }\r\n\r\n    return chunk;\r\n  });\r\n}\r\n\r\nfunction BlobBuilderConstructor(ary, options) {\r\n  options = options || {};\r\n\r\n  var bb = new BlobBuilder();\r\n  mapArrayBufferViews(ary).forEach(function(part) {\r\n    bb.append(part);\r\n  });\r\n\r\n  return (options.type) ? bb.getBlob(options.type) : bb.getBlob();\r\n};\r\n\r\nfunction BlobConstructor(ary, options) {\r\n  return new Blob(mapArrayBufferViews(ary), options || {});\r\n};\r\n\r\nif (typeof Blob !== 'undefined') {\r\n  BlobBuilderConstructor.prototype = Blob.prototype;\r\n  BlobConstructor.prototype = Blob.prototype;\r\n}\r\n\r\nmodule.exports = (function() {\r\n  if (blobSupported) {\r\n    return blobSupportsArrayBufferView ? Blob : BlobConstructor;\r\n  } else if (blobBuilderSupported) {\r\n    return BlobBuilderConstructor;\r\n  } else {\r\n    return undefined;\r\n  }\r\n})();\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/blob/index.js\n// module id = 26\n// module chunks = 0", "/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\n\nexports.encode = function (obj) {\n  var str = '';\n\n  for (var i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      if (str.length) str += '&';\n      str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n    }\n  }\n\n  return str;\n};\n\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\n\nexports.decode = function(qs){\n  var qry = {};\n  var pairs = qs.split('&');\n  for (var i = 0, l = pairs.length; i < l; i++) {\n    var pair = pairs[i].split('=');\n    qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n  }\n  return qry;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/parseqs/index.js\n// module id = 27\n// module chunks = 0", "\nmodule.exports = function(a, b){\n  var fn = function(){};\n  fn.prototype = b.prototype;\n  a.prototype = new fn;\n  a.prototype.constructor = a;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/component-inherit/index.js\n// module id = 28\n// module chunks = 0", "'use strict';\n\nvar alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split('')\n  , length = 64\n  , map = {}\n  , seed = 0\n  , i = 0\n  , prev;\n\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nfunction encode(num) {\n  var encoded = '';\n\n  do {\n    encoded = alphabet[num % length] + encoded;\n    num = Math.floor(num / length);\n  } while (num > 0);\n\n  return encoded;\n}\n\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nfunction decode(str) {\n  var decoded = 0;\n\n  for (i = 0; i < str.length; i++) {\n    decoded = decoded * length + map[str.charAt(i)];\n  }\n\n  return decoded;\n}\n\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nfunction yeast() {\n  var now = encode(+new Date());\n\n  if (now !== prev) return seed = 0, prev = now;\n  return now +'.'+ encode(seed++);\n}\n\n//\n// Map each character to its index.\n//\nfor (; i < length; i++) map[alphabet[i]] = i;\n\n//\n// Expose the `yeast`, `encode` and `decode` functions.\n//\nyeast.encode = encode;\nyeast.decode = decode;\nmodule.exports = yeast;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/yeast/index.js\n// module id = 29\n// module chunks = 0", "/**\n * Module requirements.\n */\n\nvar Polling = require('./polling');\nvar inherit = require('component-inherit');\nvar globalThis = require('../globalThis');\n\n/**\n * Module exports.\n */\n\nmodule.exports = JSONPPolling;\n\n/**\n * Cached regular expressions.\n */\n\nvar rNewline = /\\n/g;\nvar rEscapedNewline = /\\\\n/g;\n\n/**\n * Global JSONP callbacks.\n */\n\nvar callbacks;\n\n/**\n * Noop.\n */\n\nfunction empty () { }\n\n/**\n * JSONP Polling constructor.\n *\n * @param {Object} opts.\n * @api public\n */\n\nfunction JSONPPolling (opts) {\n  Polling.call(this, opts);\n\n  this.query = this.query || {};\n\n  // define global callbacks array if not present\n  // we do this here (lazily) to avoid unneeded global pollution\n  if (!callbacks) {\n    // we need to consider multiple engines in the same page\n    callbacks = globalThis.___eio = (globalThis.___eio || []);\n  }\n\n  // callback identifier\n  this.index = callbacks.length;\n\n  // add callback to jsonp global\n  var self = this;\n  callbacks.push(function (msg) {\n    self.onData(msg);\n  });\n\n  // append to query string\n  this.query.j = this.index;\n\n  // prevent spurious errors from being emitted when the window is unloaded\n  if (typeof addEventListener === 'function') {\n    addEventListener('beforeunload', function () {\n      if (self.script) self.script.onerror = empty;\n    }, false);\n  }\n}\n\n/**\n * Inherits from Polling.\n */\n\ninherit(JSONPPolling, Polling);\n\n/*\n * JSONP only supports binary as base64 encoded strings\n */\n\nJSONPPolling.prototype.supportsBinary = false;\n\n/**\n * Closes the socket.\n *\n * @api private\n */\n\nJSONPPolling.prototype.doClose = function () {\n  if (this.script) {\n    this.script.parentNode.removeChild(this.script);\n    this.script = null;\n  }\n\n  if (this.form) {\n    this.form.parentNode.removeChild(this.form);\n    this.form = null;\n    this.iframe = null;\n  }\n\n  Polling.prototype.doClose.call(this);\n};\n\n/**\n * Starts a poll cycle.\n *\n * @api private\n */\n\nJSONPPolling.prototype.doPoll = function () {\n  var self = this;\n  var script = document.createElement('script');\n\n  if (this.script) {\n    this.script.parentNode.removeChild(this.script);\n    this.script = null;\n  }\n\n  script.async = true;\n  script.src = this.uri();\n  script.onerror = function (e) {\n    self.onError('jsonp poll error', e);\n  };\n\n  var insertAt = document.getElementsByTagName('script')[0];\n  if (insertAt) {\n    insertAt.parentNode.insertBefore(script, insertAt);\n  } else {\n    (document.head || document.body).appendChild(script);\n  }\n  this.script = script;\n\n  var isUAgecko = 'undefined' !== typeof navigator && /gecko/i.test(navigator.userAgent);\n\n  if (isUAgecko) {\n    setTimeout(function () {\n      var iframe = document.createElement('iframe');\n      document.body.appendChild(iframe);\n      document.body.removeChild(iframe);\n    }, 100);\n  }\n};\n\n/**\n * Writes with a hidden iframe.\n *\n * @param {String} data to send\n * @param {Function} called upon flush.\n * @api private\n */\n\nJSONPPolling.prototype.doWrite = function (data, fn) {\n  var self = this;\n\n  if (!this.form) {\n    var form = document.createElement('form');\n    var area = document.createElement('textarea');\n    var id = this.iframeId = 'eio_iframe_' + this.index;\n    var iframe;\n\n    form.className = 'socketio';\n    form.style.position = 'absolute';\n    form.style.top = '-1000px';\n    form.style.left = '-1000px';\n    form.target = id;\n    form.method = 'POST';\n    form.setAttribute('accept-charset', 'utf-8');\n    area.name = 'd';\n    form.appendChild(area);\n    document.body.appendChild(form);\n\n    this.form = form;\n    this.area = area;\n  }\n\n  this.form.action = this.uri();\n\n  function complete () {\n    initIframe();\n    fn();\n  }\n\n  function initIframe () {\n    if (self.iframe) {\n      try {\n        self.form.removeChild(self.iframe);\n      } catch (e) {\n        self.onError('jsonp polling iframe removal error', e);\n      }\n    }\n\n    try {\n      // ie6 dynamic iframes with target=\"\" support (thanks Chris Lambacher)\n      var html = '<iframe src=\"javascript:0\" name=\"' + self.iframeId + '\">';\n      iframe = document.createElement(html);\n    } catch (e) {\n      iframe = document.createElement('iframe');\n      iframe.name = self.iframeId;\n      iframe.src = 'javascript:0';\n    }\n\n    iframe.id = self.iframeId;\n\n    self.form.appendChild(iframe);\n    self.iframe = iframe;\n  }\n\n  initIframe();\n\n  // escape \\n to prevent it from being converted into \\r\\n by some UAs\n  // double escaping is required for escaped new lines because unescaping of new lines can be done safely on server-side\n  data = data.replace(rEscapedNewline, '\\\\\\n');\n  this.area.value = data.replace(rNewline, '\\\\n');\n\n  try {\n    this.form.submit();\n  } catch (e) {}\n\n  if (this.iframe.attachEvent) {\n    this.iframe.onreadystatechange = function () {\n      if (self.iframe.readyState === 'complete') {\n        complete();\n      }\n    };\n  } else {\n    this.iframe.onload = complete;\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/polling-jsonp.js\n// module id = 30\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar Transport = require('../transport');\nvar parser = require('engine.io-parser');\nvar parseqs = require('parseqs');\nvar inherit = require('component-inherit');\nvar yeast = require('yeast');\nvar debug = require('debug')('engine.io-client:websocket');\n\nvar BrowserWebSocket, NodeWebSocket;\n\nif (typeof WebSocket !== 'undefined') {\n  BrowserWebSocket = WebSocket;\n} else if (typeof self !== 'undefined') {\n  BrowserWebSocket = self.WebSocket || self.MozWebSocket;\n}\n\nif (typeof window === 'undefined') {\n  try {\n    NodeWebSocket = require('ws');\n  } catch (e) { }\n}\n\n/**\n * Get either the `WebSocket` or `MozWebSocket` globals\n * in the browser or try to resolve WebSocket-compatible\n * interface exposed by `ws` for Node-like environment.\n */\n\nvar WebSocketImpl = BrowserWebSocket || NodeWebSocket;\n\n/**\n * Module exports.\n */\n\nmodule.exports = WS;\n\n/**\n * WebSocket transport constructor.\n *\n * @api {Object} connection options\n * @api public\n */\n\nfunction WS (opts) {\n  var forceBase64 = (opts && opts.forceBase64);\n  if (forceBase64) {\n    this.supportsBinary = false;\n  }\n  this.perMessageDeflate = opts.perMessageDeflate;\n  this.usingBrowserWebSocket = BrowserWebSocket && !opts.forceNode;\n  this.protocols = opts.protocols;\n  if (!this.usingBrowserWebSocket) {\n    WebSocketImpl = NodeWebSocket;\n  }\n  Transport.call(this, opts);\n}\n\n/**\n * Inherits from Transport.\n */\n\ninherit(WS, Transport);\n\n/**\n * Transport name.\n *\n * @api public\n */\n\nWS.prototype.name = 'websocket';\n\n/*\n * WebSockets support binary\n */\n\nWS.prototype.supportsBinary = true;\n\n/**\n * Opens socket.\n *\n * @api private\n */\n\nWS.prototype.doOpen = function () {\n  if (!this.check()) {\n    // let probe timeout\n    return;\n  }\n\n  var uri = this.uri();\n  var protocols = this.protocols;\n\n  var opts = {};\n\n  if (!this.isReactNative) {\n    opts.agent = this.agent;\n    opts.perMessageDeflate = this.perMessageDeflate;\n\n    // SSL options for Node.js client\n    opts.pfx = this.pfx;\n    opts.key = this.key;\n    opts.passphrase = this.passphrase;\n    opts.cert = this.cert;\n    opts.ca = this.ca;\n    opts.ciphers = this.ciphers;\n    opts.rejectUnauthorized = this.rejectUnauthorized;\n  }\n\n  if (this.extraHeaders) {\n    opts.headers = this.extraHeaders;\n  }\n  if (this.localAddress) {\n    opts.localAddress = this.localAddress;\n  }\n\n  try {\n    this.ws =\n      this.usingBrowserWebSocket && !this.isReactNative\n        ? protocols\n          ? new WebSocketImpl(uri, protocols)\n          : new WebSocketImpl(uri)\n        : new WebSocketImpl(uri, protocols, opts);\n  } catch (err) {\n    return this.emit('error', err);\n  }\n\n  if (this.ws.binaryType === undefined) {\n    this.supportsBinary = false;\n  }\n\n  if (this.ws.supports && this.ws.supports.binary) {\n    this.supportsBinary = true;\n    this.ws.binaryType = 'nodebuffer';\n  } else {\n    this.ws.binaryType = 'arraybuffer';\n  }\n\n  this.addEventListeners();\n};\n\n/**\n * Adds event listeners to the socket\n *\n * @api private\n */\n\nWS.prototype.addEventListeners = function () {\n  var self = this;\n\n  this.ws.onopen = function () {\n    self.onOpen();\n  };\n  this.ws.onclose = function () {\n    self.onClose();\n  };\n  this.ws.onmessage = function (ev) {\n    self.onData(ev.data);\n  };\n  this.ws.onerror = function (e) {\n    self.onError('websocket error', e);\n  };\n};\n\n/**\n * Writes data to socket.\n *\n * @param {Array} array of packets.\n * @api private\n */\n\nWS.prototype.write = function (packets) {\n  var self = this;\n  this.writable = false;\n\n  // encodePacket efficient as it uses WS framing\n  // no need for encodePayload\n  var total = packets.length;\n  for (var i = 0, l = total; i < l; i++) {\n    (function (packet) {\n      parser.encodePacket(packet, self.supportsBinary, function (data) {\n        if (!self.usingBrowserWebSocket) {\n          // always create a new object (GH-437)\n          var opts = {};\n          if (packet.options) {\n            opts.compress = packet.options.compress;\n          }\n\n          if (self.perMessageDeflate) {\n            var len = 'string' === typeof data ? Buffer.byteLength(data) : data.length;\n            if (len < self.perMessageDeflate.threshold) {\n              opts.compress = false;\n            }\n          }\n        }\n\n        // Sometimes the websocket has already been closed but the browser didn't\n        // have a chance of informing us about it yet, in that case send will\n        // throw an error\n        try {\n          if (self.usingBrowserWebSocket) {\n            // TypeError is thrown when passing the second argument on Safari\n            self.ws.send(data);\n          } else {\n            self.ws.send(data, opts);\n          }\n        } catch (e) {\n\n        }\n\n        --total || done();\n      });\n    })(packets[i]);\n  }\n\n  function done () {\n    self.emit('flush');\n\n    // fake drain\n    // defer to next tick to allow Socket to clear writeBuffer\n    setTimeout(function () {\n      self.writable = true;\n      self.emit('drain');\n    }, 0);\n  }\n};\n\n/**\n * Called upon close\n *\n * @api private\n */\n\nWS.prototype.onClose = function () {\n  Transport.prototype.onClose.call(this);\n};\n\n/**\n * Closes socket.\n *\n * @api private\n */\n\nWS.prototype.doClose = function () {\n  if (typeof this.ws !== 'undefined') {\n    this.ws.close();\n  }\n};\n\n/**\n * Generates uri for connection.\n *\n * @api private\n */\n\nWS.prototype.uri = function () {\n  var query = this.query || {};\n  var schema = this.secure ? 'wss' : 'ws';\n  var port = '';\n\n  // avoid port if default for schema\n  if (this.port && (('wss' === schema && Number(this.port) !== 443) ||\n    ('ws' === schema && Number(this.port) !== 80))) {\n    port = ':' + this.port;\n  }\n\n  // append timestamp to URI\n  if (this.timestampRequests) {\n    query[this.timestampParam] = yeast();\n  }\n\n  // communicate binary support capabilities\n  if (!this.supportsBinary) {\n    query.b64 = 1;\n  }\n\n  query = parseqs.encode(query);\n\n  // prepend ? to query\n  if (query.length) {\n    query = '?' + query;\n  }\n\n  var ipv6 = this.hostname.indexOf(':') !== -1;\n  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n};\n\n/**\n * Feature detection for WebSocket.\n *\n * @return {Boolean} whether this transport is available.\n * @api public\n */\n\nWS.prototype.check = function () {\n  return !!WebSocketImpl && !('__initialize' in WebSocketImpl && this.name === WS.prototype.name);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/websocket.js\n// module id = 31\n// module chunks = 0", "\nvar indexOf = [].indexOf;\n\nmodule.exports = function(arr, obj){\n  if (indexOf) return arr.indexOf(obj);\n  for (var i = 0; i < arr.length; ++i) {\n    if (arr[i] === obj) return i;\n  }\n  return -1;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/indexof/index.js\n// module id = 33\n// module chunks = 0", "\n/**\n * Module dependencies.\n */\n\nvar parser = require('socket.io-parser');\nvar Emitter = require('component-emitter');\nvar toArray = require('to-array');\nvar on = require('./on');\nvar bind = require('component-bind');\nvar debug = require('debug')('socket.io-client:socket');\nvar parseqs = require('parseqs');\nvar hasBin = require('has-binary2');\n\n/**\n * Module exports.\n */\n\nmodule.exports = exports = Socket;\n\n/**\n * Internal events (blacklisted).\n * These events can't be emitted by the user.\n *\n * @api private\n */\n\nvar events = {\n  connect: 1,\n  connect_error: 1,\n  connect_timeout: 1,\n  connecting: 1,\n  disconnect: 1,\n  error: 1,\n  reconnect: 1,\n  reconnect_attempt: 1,\n  reconnect_failed: 1,\n  reconnect_error: 1,\n  reconnecting: 1,\n  ping: 1,\n  pong: 1\n};\n\n/**\n * Shortcut to `Emitter#emit`.\n */\n\nvar emit = Emitter.prototype.emit;\n\n/**\n * `Socket` constructor.\n *\n * @api public\n */\n\nfunction Socket (io, nsp, opts) {\n  this.io = io;\n  this.nsp = nsp;\n  this.json = this; // compat\n  this.ids = 0;\n  this.acks = {};\n  this.receiveBuffer = [];\n  this.sendBuffer = [];\n  this.connected = false;\n  this.disconnected = true;\n  this.flags = {};\n  if (opts && opts.query) {\n    this.query = opts.query;\n  }\n  if (this.io.autoConnect) this.open();\n}\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Socket.prototype);\n\n/**\n * Subscribe to open, close and packet events\n *\n * @api private\n */\n\nSocket.prototype.subEvents = function () {\n  if (this.subs) return;\n\n  var io = this.io;\n  this.subs = [\n    on(io, 'open', bind(this, 'onopen')),\n    on(io, 'packet', bind(this, 'onpacket')),\n    on(io, 'close', bind(this, 'onclose'))\n  ];\n};\n\n/**\n * \"Opens\" the socket.\n *\n * @api public\n */\n\nSocket.prototype.open =\nSocket.prototype.connect = function () {\n  if (this.connected) return this;\n\n  this.subEvents();\n  if (!this.io.reconnecting) this.io.open(); // ensure open\n  if ('open' === this.io.readyState) this.onopen();\n  this.emit('connecting');\n  return this;\n};\n\n/**\n * Sends a `message` event.\n *\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.send = function () {\n  var args = toArray(arguments);\n  args.unshift('message');\n  this.emit.apply(this, args);\n  return this;\n};\n\n/**\n * Override `emit`.\n * If the event is in `events`, it's emitted normally.\n *\n * @param {String} event name\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.emit = function (ev) {\n  if (events.hasOwnProperty(ev)) {\n    emit.apply(this, arguments);\n    return this;\n  }\n\n  var args = toArray(arguments);\n  var packet = {\n    type: (this.flags.binary !== undefined ? this.flags.binary : hasBin(args)) ? parser.BINARY_EVENT : parser.EVENT,\n    data: args\n  };\n\n  packet.options = {};\n  packet.options.compress = !this.flags || false !== this.flags.compress;\n\n  // event ack callback\n  if ('function' === typeof args[args.length - 1]) {\n\n    this.acks[this.ids] = args.pop();\n    packet.id = this.ids++;\n  }\n\n  if (this.connected) {\n    this.packet(packet);\n  } else {\n    this.sendBuffer.push(packet);\n  }\n\n  this.flags = {};\n\n  return this;\n};\n\n/**\n * Sends a packet.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.packet = function (packet) {\n  packet.nsp = this.nsp;\n  this.io.packet(packet);\n};\n\n/**\n * Called upon engine `open`.\n *\n * @api private\n */\n\nSocket.prototype.onopen = function () {\n\n\n  // write connect packet if necessary\n  if ('/' !== this.nsp) {\n    if (this.query) {\n      var query = typeof this.query === 'object' ? parseqs.encode(this.query) : this.query;\n\n      this.packet({type: parser.CONNECT, query: query});\n    } else {\n      this.packet({type: parser.CONNECT});\n    }\n  }\n};\n\n/**\n * Called upon engine `close`.\n *\n * @param {String} reason\n * @api private\n */\n\nSocket.prototype.onclose = function (reason) {\n\n  this.connected = false;\n  this.disconnected = true;\n  delete this.id;\n  this.emit('disconnect', reason);\n};\n\n/**\n * Called with socket packet.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.onpacket = function (packet) {\n  var sameNamespace = packet.nsp === this.nsp;\n  var rootNamespaceError = packet.type === parser.ERROR && packet.nsp === '/';\n\n  if (!sameNamespace && !rootNamespaceError) return;\n\n  switch (packet.type) {\n    case parser.CONNECT:\n      this.onconnect();\n      break;\n\n    case parser.EVENT:\n      this.onevent(packet);\n      break;\n\n    case parser.BINARY_EVENT:\n      this.onevent(packet);\n      break;\n\n    case parser.ACK:\n      this.onack(packet);\n      break;\n\n    case parser.BINARY_ACK:\n      this.onack(packet);\n      break;\n\n    case parser.DISCONNECT:\n      this.ondisconnect();\n      break;\n\n    case parser.ERROR:\n      this.emit('error', packet.data);\n      break;\n  }\n};\n\n/**\n * Called upon a server event.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.onevent = function (packet) {\n  var args = packet.data || [];\n\n\n  if (null != packet.id) {\n\n    args.push(this.ack(packet.id));\n  }\n\n  if (this.connected) {\n    emit.apply(this, args);\n  } else {\n    this.receiveBuffer.push(args);\n  }\n};\n\n/**\n * Produces an ack callback to emit with an event.\n *\n * @api private\n */\n\nSocket.prototype.ack = function (id) {\n  var self = this;\n  var sent = false;\n  return function () {\n    // prevent double callbacks\n    if (sent) return;\n    sent = true;\n    var args = toArray(arguments);\n\n\n    self.packet({\n      type: hasBin(args) ? parser.BINARY_ACK : parser.ACK,\n      id: id,\n      data: args\n    });\n  };\n};\n\n/**\n * Called upon a server acknowlegement.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.onack = function (packet) {\n  var ack = this.acks[packet.id];\n  if ('function' === typeof ack) {\n\n    ack.apply(this, packet.data);\n    delete this.acks[packet.id];\n  } else {\n\n  }\n};\n\n/**\n * Called upon server connect.\n *\n * @api private\n */\n\nSocket.prototype.onconnect = function () {\n  this.connected = true;\n  this.disconnected = false;\n  this.emitBuffered();\n  this.emit('connect');\n};\n\n/**\n * Emit buffered events (received and emitted).\n *\n * @api private\n */\n\nSocket.prototype.emitBuffered = function () {\n  var i;\n  for (i = 0; i < this.receiveBuffer.length; i++) {\n    emit.apply(this, this.receiveBuffer[i]);\n  }\n  this.receiveBuffer = [];\n\n  for (i = 0; i < this.sendBuffer.length; i++) {\n    this.packet(this.sendBuffer[i]);\n  }\n  this.sendBuffer = [];\n};\n\n/**\n * Called upon server disconnect.\n *\n * @api private\n */\n\nSocket.prototype.ondisconnect = function () {\n\n  this.destroy();\n  this.onclose('io server disconnect');\n};\n\n/**\n * Called upon forced client/server side disconnections,\n * this method ensures the manager stops tracking us and\n * that reconnections don't get triggered for this.\n *\n * @api private.\n */\n\nSocket.prototype.destroy = function () {\n  if (this.subs) {\n    // clean subscriptions to avoid reconnections\n    for (var i = 0; i < this.subs.length; i++) {\n      this.subs[i].destroy();\n    }\n    this.subs = null;\n  }\n\n  this.io.destroy(this);\n};\n\n/**\n * Disconnects the socket manually.\n *\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.close =\nSocket.prototype.disconnect = function () {\n  if (this.connected) {\n\n    this.packet({ type: parser.DISCONNECT });\n  }\n\n  // remove socket from pool\n  this.destroy();\n\n  if (this.connected) {\n    // fire events\n    this.onclose('io client disconnect');\n  }\n  return this;\n};\n\n/**\n * Sets the compress flag.\n *\n * @param {Boolean} if `true`, compresses the sending data\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.compress = function (compress) {\n  this.flags.compress = compress;\n  return this;\n};\n\n/**\n * Sets the binary flag\n *\n * @param {Boolean} whether the emitted data contains binary\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.binary = function (binary) {\n  this.flags.binary = binary;\n  return this;\n};\n\n\n\n// WEBPACK FOOTER //\n// ./lib/socket.js", "module.exports = toArray\n\nfunction toArray(list, index) {\n    var array = []\n\n    index = index || 0\n\n    for (var i = index || 0; i < list.length; i++) {\n        array[i - index] = list[i]\n    }\n\n    return array\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/to-array/index.js\n// module id = 35\n// module chunks = 0", "\n/**\n * Module exports.\n */\n\nmodule.exports = on;\n\n/**\n * Helper for subscriptions.\n *\n * @param {Object|EventEmitter} obj with `Emitter` mixin or `EventEmitter`\n * @param {String} event name\n * @param {Function} callback\n * @api public\n */\n\nfunction on (obj, ev, fn) {\n  obj.on(ev, fn);\n  return {\n    destroy: function () {\n      obj.removeListener(ev, fn);\n    }\n  };\n}\n\n\n\n// WEBPACK FOOTER //\n// ./lib/on.js", "/**\n * Slice reference.\n */\n\nvar slice = [].slice;\n\n/**\n * Bind `obj` to `fn`.\n *\n * @param {Object} obj\n * @param {Function|String} fn or string\n * @return {Function}\n * @api public\n */\n\nmodule.exports = function(obj, fn){\n  if ('string' == typeof fn) fn = obj[fn];\n  if ('function' != typeof fn) throw new Error('bind() requires a function');\n  var args = slice.call(arguments, 2);\n  return function(){\n    return fn.apply(obj, args.concat(slice.call(arguments)));\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/component-bind/index.js\n// module id = 37\n// module chunks = 0", "\n/**\n * Expose `Backoff`.\n */\n\nmodule.exports = Backoff;\n\n/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\n\nfunction Backoff(opts) {\n  opts = opts || {};\n  this.ms = opts.min || 100;\n  this.max = opts.max || 10000;\n  this.factor = opts.factor || 2;\n  this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n  this.attempts = 0;\n}\n\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\n\nBackoff.prototype.duration = function(){\n  var ms = this.ms * Math.pow(this.factor, this.attempts++);\n  if (this.jitter) {\n    var rand =  Math.random();\n    var deviation = Math.floor(rand * this.jitter * ms);\n    ms = (Math.floor(rand * 10) & 1) == 0  ? ms - deviation : ms + deviation;\n  }\n  return Math.min(ms, this.max) | 0;\n};\n\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\n\nBackoff.prototype.reset = function(){\n  this.attempts = 0;\n};\n\n/**\n * Set the minimum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMin = function(min){\n  this.ms = min;\n};\n\n/**\n * Set the maximum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMax = function(max){\n  this.max = max;\n};\n\n/**\n * Set the jitter\n *\n * @api public\n */\n\nBackoff.prototype.setJitter = function(jitter){\n  this.jitter = jitter;\n};\n\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/backo2/index.js\n// module id = 38\n// module chunks = 0"], "sourceRoot": ""}
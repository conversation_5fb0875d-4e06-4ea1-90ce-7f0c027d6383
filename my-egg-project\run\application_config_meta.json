{"keys": "E:\\Kiro\\my-egg-project\\config\\config.default.js", "middleware": "E:\\Kiro\\my-egg-project\\config\\config.default.js", "io": {"init": {"cors": {"origin": "E:\\Kiro\\my-egg-project\\config\\config.default.js", "methods": "E:\\Kiro\\my-egg-project\\config\\config.default.js"}}, "namespace": {"/": {"connectionMiddleware": "E:\\Kiro\\my-egg-project\\config\\config.default.js", "packetMiddleware": "E:\\Kiro\\my-egg-project\\config\\config.default.js"}}}, "security": {"csrf": {"enable": "E:\\Kiro\\my-egg-project\\config\\config.default.js", "type": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "ignoreJSON": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "useSession": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "cookieName": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "sessionName": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "headerName": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "bodyName": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "queryName": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "rotateWhenInvalid": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "supportedRequests": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "refererWhiteList": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "cookieOptions": {"signed": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js"}}, "domainWhiteList": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "protocolWhiteList": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "defaultMiddleware": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "xframe": {"enable": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "value": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js"}, "hsts": {"enable": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.local.js", "maxAge": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "includeSubdomains": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js"}, "dta": {"enable": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js"}, "methodnoallow": {"enable": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js"}, "noopen": {"enable": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js"}, "nosniff": {"enable": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js"}, "referrerPolicy": {"enable": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "value": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js"}, "xssProtection": {"enable": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "value": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js"}, "csp": {"enable": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "policy": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js"}, "ssrf": {"ipBlackList": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "ipExceptionList": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "hostnameExceptionList": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js", "checkAddress": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js"}}, "cors": {"origin": "E:\\Kiro\\my-egg-project\\config\\config.default.js", "allowMethods": "E:\\Kiro\\my-egg-project\\config\\config.default.js"}, "session": {"maxAge": "E:\\Kiro\\my-egg-project\\node_modules\\egg-session\\config\\config.default.js", "key": "E:\\Kiro\\my-egg-project\\node_modules\\egg-session\\config\\config.default.js", "httpOnly": "E:\\Kiro\\my-egg-project\\node_modules\\egg-session\\config\\config.default.js", "encrypt": "E:\\Kiro\\my-egg-project\\node_modules\\egg-session\\config\\config.default.js", "logValue": "E:\\Kiro\\my-egg-project\\node_modules\\egg-session\\config\\config.default.js"}, "helper": {"shtml": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security\\config\\config.default.js"}, "jsonp": {"limit": "E:\\Kiro\\my-egg-project\\node_modules\\egg-jsonp\\config\\config.default.js", "callback": "E:\\Kiro\\my-egg-project\\node_modules\\egg-jsonp\\config\\config.default.js", "csrf": "E:\\Kiro\\my-egg-project\\node_modules\\egg-jsonp\\config\\config.default.js"}, "onerror": {"errorPageUrl": "E:\\Kiro\\my-egg-project\\node_modules\\egg-onerror\\config\\config.default.js", "appErrorFilter": "E:\\Kiro\\my-egg-project\\node_modules\\egg-onerror\\config\\config.default.js", "templatePath": "E:\\Kiro\\my-egg-project\\node_modules\\egg-onerror\\config\\config.default.js"}, "i18n": {"defaultLocale": "E:\\Kiro\\my-egg-project\\node_modules\\egg-i18n\\config\\config.default.js", "dirs": "E:\\Kiro\\my-egg-project\\node_modules\\egg-i18n\\config\\config.default.js", "queryField": "E:\\Kiro\\my-egg-project\\node_modules\\egg-i18n\\config\\config.default.js", "cookieField": "E:\\Kiro\\my-egg-project\\node_modules\\egg-i18n\\config\\config.default.js", "cookieDomain": "E:\\Kiro\\my-egg-project\\node_modules\\egg-i18n\\config\\config.default.js", "cookieMaxAge": "E:\\Kiro\\my-egg-project\\node_modules\\egg-i18n\\config\\config.default.js"}, "watcher": {"type": "E:\\Kiro\\my-egg-project\\node_modules\\egg-watcher\\config\\config.local.js", "eventSources": {"default": "E:\\Kiro\\my-egg-project\\node_modules\\egg-watcher\\config\\config.default.js", "development": "E:\\Kiro\\my-egg-project\\node_modules\\egg-watcher\\config\\config.default.js"}}, "customLogger": {"scheduleLogger": {"consoleLevel": "E:\\Kiro\\my-egg-project\\node_modules\\egg-schedule\\config\\config.default.js", "file": "E:\\Kiro\\my-egg-project\\node_modules\\egg-schedule\\config\\config.default.js"}}, "schedule": {"directory": "E:\\Kiro\\my-egg-project\\node_modules\\egg-schedule\\config\\config.default.js"}, "multipart": {"mode": "E:\\Kiro\\my-egg-project\\node_modules\\egg-multipart\\config\\config.default.js", "autoFields": "E:\\Kiro\\my-egg-project\\node_modules\\egg-multipart\\config\\config.default.js", "defaultCharset": "E:\\Kiro\\my-egg-project\\node_modules\\egg-multipart\\config\\config.default.js", "defaultParamCharset": "E:\\Kiro\\my-egg-project\\node_modules\\egg-multipart\\config\\config.default.js", "fieldNameSize": "E:\\Kiro\\my-egg-project\\node_modules\\egg-multipart\\config\\config.default.js", "fieldSize": "E:\\Kiro\\my-egg-project\\node_modules\\egg-multipart\\config\\config.default.js", "fields": "E:\\Kiro\\my-egg-project\\node_modules\\egg-multipart\\config\\config.default.js", "fileSize": "E:\\Kiro\\my-egg-project\\node_modules\\egg-multipart\\config\\config.default.js", "files": "E:\\Kiro\\my-egg-project\\node_modules\\egg-multipart\\config\\config.default.js", "fileExtensions": "E:\\Kiro\\my-egg-project\\node_modules\\egg-multipart\\config\\config.default.js", "whitelist": "E:\\Kiro\\my-egg-project\\node_modules\\egg-multipart\\config\\config.default.js", "allowArrayField": "E:\\Kiro\\my-egg-project\\node_modules\\egg-multipart\\config\\config.default.js", "tmpdir": "E:\\Kiro\\my-egg-project\\node_modules\\egg-multipart\\config\\config.default.js", "cleanSchedule": {"cron": "E:\\Kiro\\my-egg-project\\node_modules\\egg-multipart\\config\\config.default.js", "disable": "E:\\Kiro\\my-egg-project\\node_modules\\egg-multipart\\config\\config.default.js"}}, "development": {"watchDirs": "E:\\Kiro\\my-egg-project\\node_modules\\egg-development\\config\\config.default.js", "ignoreDirs": "E:\\Kiro\\my-egg-project\\node_modules\\egg-development\\config\\config.default.js", "fastReady": "E:\\Kiro\\my-egg-project\\node_modules\\egg-development\\config\\config.default.js", "reloadOnDebug": "E:\\Kiro\\my-egg-project\\node_modules\\egg-development\\config\\config.default.js", "overrideDefault": "E:\\Kiro\\my-egg-project\\node_modules\\egg-development\\config\\config.default.js", "overrideIgnore": "E:\\Kiro\\my-egg-project\\node_modules\\egg-development\\config\\config.default.js"}, "logrotator": {"filesRotateByHour": "E:\\Kiro\\my-egg-project\\node_modules\\egg-logrotator\\config\\config.default.js", "hourDelimiter": "E:\\Kiro\\my-egg-project\\node_modules\\egg-logrotator\\config\\config.default.js", "filesRotateBySize": "E:\\Kiro\\my-egg-project\\node_modules\\egg-logrotator\\config\\config.default.js", "maxFileSize": "E:\\Kiro\\my-egg-project\\node_modules\\egg-logrotator\\config\\config.default.js", "maxFiles": "E:\\Kiro\\my-egg-project\\node_modules\\egg-logrotator\\config\\config.default.js", "rotateDuration": "E:\\Kiro\\my-egg-project\\node_modules\\egg-logrotator\\config\\config.default.js", "maxDays": "E:\\Kiro\\my-egg-project\\node_modules\\egg-logrotator\\config\\config.default.js"}, "static": {"prefix": "E:\\Kiro\\my-egg-project\\node_modules\\egg-static\\config\\config.default.js", "dir": "E:\\Kiro\\my-egg-project\\node_modules\\egg-static\\config\\config.default.js", "dynamic": "E:\\Kiro\\my-egg-project\\node_modules\\egg-static\\config\\config.default.js", "preload": "E:\\Kiro\\my-egg-project\\node_modules\\egg-static\\config\\config.default.js", "buffer": "E:\\Kiro\\my-egg-project\\node_modules\\egg-static\\config\\config.default.js", "maxFiles": "E:\\Kiro\\my-egg-project\\node_modules\\egg-static\\config\\config.default.js"}, "view": {"root": "E:\\Kiro\\my-egg-project\\node_modules\\egg-view\\config\\config.default.js", "cache": "E:\\Kiro\\my-egg-project\\node_modules\\egg-view\\config\\config.local.js", "defaultExtension": "E:\\Kiro\\my-egg-project\\node_modules\\egg-view\\config\\config.default.js", "defaultViewEngine": "E:\\Kiro\\my-egg-project\\node_modules\\egg-view\\config\\config.default.js", "mapping": "E:\\Kiro\\my-egg-project\\node_modules\\egg-view\\config\\config.default.js"}, "env": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "name": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "cookies": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "proxy": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "maxIpsCount": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "maxProxyCount": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "protocolHeaders": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "ipHeaders": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "hostHeaders": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "pkg": {"name": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "version": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "description": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "private": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "egg": {"declarations": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}, "dependencies": {"egg": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "egg-scripts": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "egg-socket.io": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}, "devDependencies": {"egg-bin": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "egg-mock": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "eslint": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "eslint-config-egg": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}, "engines": {"node": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}, "scripts": {"start": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "stop": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "dev": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "test": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "test:local": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "cov": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "lint": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "ci": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}, "repository": {"type": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "url": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}, "author": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "license": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}, "baseDir": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "HOME": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "rundir": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "dump": {"ignore": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "timing": {"slowBootActionMinDuration": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}}, "confusedConfigurations": {"bodyparser": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "notFound": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "sitefile": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "middlewares": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "httpClient": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}, "notfound": {"pageUrl": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}, "siteFile": {"/favicon.ico": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "cacheControl": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}, "bodyParser": {"enable": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "encoding": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "formLimit": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "jsonLimit": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "textLimit": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "strict": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "queryString": {"arrayLimit": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "depth": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "parameterLimit": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}, "onerror": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}, "logger": {"dir": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "encoding": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "env": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "level": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "consoleLevel": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "disableConsoleAfterReady": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "outputJSON": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "buffer": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "appLogName": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "coreLogName": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "agentLogName": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "errorLogName": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "coreLogger": {"consoleLevel": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.local.js"}, "allowDebugAtProd": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "enablePerformanceTimer": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "enableFastContextLogger": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}, "httpclient": {"enableDNSCache": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "dnsCacheLookupInterval": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "dnsCacheMaxLength": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "request": {"timeout": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}, "httpAgent": {"keepAlive": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "freeSocketTimeout": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "maxSockets": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "maxFreeSockets": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}, "httpsAgent": {"keepAlive": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "freeSocketTimeout": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "maxSockets": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "maxFreeSockets": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}, "useHttpClientNext": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}, "meta": {"enable": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "logging": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}, "coreMiddleware": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "workerStartTimeout": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "serverTimeout": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "cluster": {"listen": {"path": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "port": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "hostname": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}}, "clusterClient": {"maxWaitTime": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js", "responseTimeout": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}, "onClientError": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\config.default.js"}
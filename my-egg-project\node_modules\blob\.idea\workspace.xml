<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="584fee10-991c-4143-af70-57730369b503" name="Default Changelist" comment="" />
    <ignored path="$PROJECT_DIR$/.tmp/" />
    <ignored path="$PROJECT_DIR$/temp/" />
    <ignored path="$PROJECT_DIR$/tmp/" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FUSProjectUsageTrigger">
    <session id="424275792">
      <usages-collector id="statistics.lifecycle.project">
        <counts>
          <entry key="project.open.time.3" value="1" />
          <entry key="project.opened" value="1" />
        </counts>
      </usages-collector>
      <usages-collector id="statistics.file.extensions.open">
        <counts>
          <entry key="Makefile" value="3" />
          <entry key="gitignore" value="3" />
          <entry key="js" value="6" />
          <entry key="json" value="4" />
          <entry key="md" value="3" />
        </counts>
      </usages-collector>
      <usages-collector id="statistics.file.types.open">
        <counts>
          <entry key="Git file" value="3" />
          <entry key="JSON" value="4" />
          <entry key="JavaScript" value="6" />
          <entry key="Markdown" value="3" />
          <entry key="PLAIN_TEXT" value="3" />
        </counts>
      </usages-collector>
      <usages-collector id="statistics.file.extensions.edit">
        <counts>
          <entry key="json" value="9" />
          <entry key="txt" value="34" />
        </counts>
      </usages-collector>
      <usages-collector id="statistics.file.types.edit">
        <counts>
          <entry key="JSON" value="9" />
          <entry key="PLAIN_TEXT" value="34" />
        </counts>
      </usages-collector>
      <usages-collector id="statistics.vcs.git.usages">
        <counts>
          <entry key="git.branch.checkout.local" value="2" />
          <entry key="git.branch.checkout.remote" value="2" />
          <entry key="git.branch.delete.local" value="2" />
          <entry key="git.branch.merge" value="2" />
          <entry key="git.branch.rebase" value="2" />
        </counts>
      </usages-collector>
    </session>
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="300">
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/index.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="578">
              <caret line="34" column="3" selection-start-line="34" selection-start-column="3" selection-end-line="34" selection-end-column="3" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/package.json">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="137">
              <caret line="11" column="24" selection-start-line="11" selection-start-column="24" selection-end-line="11" selection-end-column="24" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/.gitignore">
          <provider selected="true" editor-type-id="text-editor" />
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/Makefile">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="136">
              <caret line="8" column="46" selection-start-line="8" selection-start-column="25" selection-end-line="8" selection-end-column="46" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/test/index.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="136">
              <caret line="8" column="26" selection-start-line="8" selection-start-column="26" selection-end-line="8" selection-end-column="26" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/README.md">
          <provider selected="true" editor-type-id="split-provider[text-editor;MarkdownPreviewEditor]">
            <state split_layout="SPLIT">
              <first_editor relative-caret-position="204">
                <caret line="12" selection-start-line="12" selection-end-line="12" />
              </first_editor>
              <second_editor>
                <markdownNavigatorState />
              </second_editor>
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>esprima-six</find>
    </findStrings>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="patch-1" />
      </map>
    </option>
    <option name="RESET_MODE" value="KEEP" />
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/package.json" />
      </list>
    </option>
  </component>
  <component name="JsBuildToolGruntFileManager" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsBuildToolPackageJson" detection-done="true" sorting="DEFINITION_ORDER">
    <package-json value="$PROJECT_DIR$/package.json" />
  </component>
  <component name="JsGulpfileManager">
    <detection-done>true</detection-done>
    <sorting>DEFINITION_ORDER</sorting>
  </component>
  <component name="NodeModulesDirectoryManager">
    <handled-path value="$PROJECT_DIR$/node_modules" />
  </component>
  <component name="NodePackageJsonFileManager">
    <packageJsonPaths>
      <path value="$PROJECT_DIR$/package.json" />
    </packageJsonPaths>
  </component>
  <component name="ProjectFrameBounds" extendedState="6">
    <option name="x" value="-9" />
    <option name="width" value="1935" />
    <option name="height" value="1029" />
  </component>
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="blob" type="b2602c69:ProjectViewProjectNode" />
              <item name="blob" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="blob" type="b2602c69:ProjectViewProjectNode" />
              <item name="blob" type="462c0819:PsiDirectoryNode" />
              <item name="test" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
      <pane id="Scope" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="nodejs.mocha.mocha_node_package_dir" value="$PROJECT_DIR$/node_modules/mocha" />
    <property name="nodejs_interpreter_path.stuck_in_default_project" value="C:/Program Files/nodejs/node" />
    <property name="nodejs_npm_path_reset_for_default_project" value="true" />
    <property name="nodejs_package_manager_path" value="npm" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="RunManager" selected="Mocha.blob">
    <configuration name="test" type="js.build_tools.npm" factoryName="npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="test" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="blob" type="mocha-javascript-test-runner" factoryName="Mocha" temporary="true" nameIsGenerated="true">
      <node-interpreter>project</node-interpreter>
      <node-options />
      <working-directory>$PROJECT_DIR$</working-directory>
      <pass-parent-env>true</pass-parent-env>
      <ui>bdd</ui>
      <extra-mocha-options />
      <test-kind>SUITE</test-kind>
      <test-file>$PROJECT_DIR$/test/index.js</test-file>
      <test-names>
        <name value="blob" />
      </test-names>
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="npm.test" />
      <item itemvalue="Mocha.blob" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Mocha.blob" />
        <item itemvalue="npm.test" />
        <item itemvalue="Mocha.blob" />
        <item itemvalue="npm.test" />
        <item itemvalue="Mocha.blob" />
      </list>
    </recent_temporary>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="584fee10-991c-4143-af70-57730369b503" name="Default Changelist" comment="" />
      <created>1541010775450</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1541010775450</updated>
      <workItem from="1541010778407" duration="2946000" />
    </task>
    <task id="LOCAL-00001" summary="update browserify and zuul to avoid esprima-six issue">
      <created>1541013247794</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1541013247795</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TestHistory">
    <history-entry file="blob - 2018.10.31 at 21h 11m 12s.xml">
      <configuration name="blob" configurationId="mocha-javascript-test-runner" />
    </history-entry>
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="2946000" />
  </component>
  <component name="TodoView">
    <todo-panel id="selected-file">
      <is-autoscroll-to-source value="true" />
    </todo-panel>
    <todo-panel id="all">
      <are-packages-shown value="true" />
      <is-autoscroll-to-source value="true" />
    </todo-panel>
  </component>
  <component name="ToolWindowManager">
    <frame x="-7" y="-7" width="1550" height="838" extended-state="6" />
    <layout>
      <window_info id="npm" side_tool="true" />
      <window_info id="Favorites" side_tool="true" />
      <window_info active="true" content_ui="combo" id="Project" order="0" visible="true" weight="0.24983476" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info anchor="bottom" id="Docker" show_stripe_button="false" />
      <window_info anchor="bottom" id="Version Control" visible="true" weight="0.44948757" />
      <window_info anchor="bottom" id="Terminal" weight="0.55051243" />
      <window_info anchor="bottom" id="Event Log" side_tool="true" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Run" order="2" weight="0.47291362" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.4" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" weight="0.329429" />
      <window_info anchor="right" id="Commander" order="0" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="RECENTLY_FILTERED_USER_GROUPS">
                <collection />
              </option>
              <option name="RECENTLY_FILTERED_BRANCH_GROUPS">
                <collection />
              </option>
              <option name="COLUMN_ORDER">
                <list>
                  <option value="0" />
                  <option value="1" />
                  <option value="2" />
                  <option value="3" />
                </list>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsContentAnnotationSettings">
    <option name="myLimit" value="**********" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="update browserify and zuul to avoid esprima-six issue" />
    <option name="LAST_COMMIT_MESSAGE" value="update browserify and zuul to avoid esprima-six issue" />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/index.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="578">
          <caret line="34" column="3" selection-start-line="34" selection-start-column="3" selection-end-line="34" selection-end-column="3" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/.gitignore">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/Makefile">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="136">
          <caret line="8" column="46" selection-start-line="8" selection-start-column="25" selection-end-line="8" selection-end-column="46" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/index.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="136">
          <caret line="8" column="26" selection-start-line="8" selection-start-column="26" selection-end-line="8" selection-end-column="26" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/README.md">
      <provider selected="true" editor-type-id="split-provider[text-editor;MarkdownPreviewEditor]">
        <state split_layout="SPLIT">
          <first_editor relative-caret-position="204">
            <caret line="12" selection-start-line="12" selection-end-line="12" />
          </first_editor>
          <second_editor>
            <markdownNavigatorState />
          </second_editor>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/package.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="137">
          <caret line="11" column="24" selection-start-line="11" selection-start-column="24" selection-end-line="11" selection-end-column="24" />
        </state>
      </provider>
    </entry>
  </component>
</project>
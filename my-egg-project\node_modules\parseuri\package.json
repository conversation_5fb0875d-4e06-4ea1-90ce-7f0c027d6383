{"name": "parseuri", "version": "0.0.6", "description": "Method that parses a URI and returns an array of its components", "repository": {"type": "git", "url": "https://github.com/get/parseuri.git"}, "homepage": "https://github.com/get/parseuri", "scripts": {"test": "make test"}, "devDependencies": {"better-assert": "~1.0.0", "mocha": "1.17.1", "expect.js": "^0.3.1"}, "author": "<PERSON><PERSON>", "license": "MIT"}
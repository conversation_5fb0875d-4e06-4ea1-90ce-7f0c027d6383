
/**
 * Module dependencies.
 */

var Polling = require('./polling');
var qs = require('querystring');
var rDoubleSlashes = /\\\\n/g;
var rSlashes = /(\\)?\\n/g;
var util = require('util');

/**
 * Module exports.
 */

module.exports = JSONP;

/**
 * JSON-P polling transport.
 *
 * @api public
 */

function JSONP (req) {
  Polling.call(this, req);

  this.head = '___eio[' + (req._query.j || '').replace(/[^0-9]/g, '') + '](';
  this.foot = ');';
}

/**
 * Inherits from Polling.
 */

util.inherits(JSONP, Polling);

/**
 * Handles incoming data.
 * Due to a bug in \n handling by browsers, we expect a escaped string.
 *
 * @api private
 */

JSONP.prototype.onData = function (data) {
  // we leverage the qs module so that we get built-in DoS protection
  // and the fast alternative to decodeURIComponent
  data = qs.parse(data).d;
  if ('string' === typeof data) {
    // client will send already escaped newlines as \\\\n and newlines as \\n
    // \\n must be replaced with \n and \\\\n with \\n
    data = data.replace(rSlashes, function (match, slashes) {
      return slashes ? match : '\n';
    });
    Polling.prototype.onData.call(this, data.replace(rDoubleSlashes, '\\n'));
  }
};

/**
 * Performs the write.
 *
 * @api private
 */

JSONP.prototype.doWrite = function (data, options, callback) {
  // we must output valid javascript, not valid json
  // see: http://timelessrepo.com/json-isnt-a-javascript-subset
  var js = JSON.stringify(data)
    .replace(/\u2028/g, '\\u2028')
    .replace(/\u2029/g, '\\u2029');

  // prepare response
  data = this.head + js + this.foot;

  Polling.prototype.doWrite.call(this, data, options, callback);
};

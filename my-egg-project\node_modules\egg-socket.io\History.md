
4.1.6 / 2019-06-20
==================

  * bugfix: add error handle to redis adapter to avoid unexpected exit. (#55)

4.1.5 / 2018-10-30
==================

  * fix: fix the memory leak (#51)

4.1.4 / 2018-09-30
==================

  * fix: when use external cookie, ctx.session should work. (#48)

4.1.3 / 2018-09-20
==================

  * fix (uws): Update this to a fixed version and whole project to Node 8,10 (#47)

4.1.2 / 2018-09-12
==================

  * fix (index.d.ts): 'of' missing 'route' with unit tests (#46)

4.1.1 / 2018-08-29
==================

  * doc (README.md/README-zh_CN.md): Add clarification (#45)

4.1.0 / 2018-08-29
==================

  * feat: Allow users to re-define new generatorID for Socket Server (#44)
  * doc: add uws deprecated information (#43)
  * docs: fix ws engine name (#40)

4.0.8 / 2018-04-25
==================

  * chore: improve .d.ts declaration (#37)
  * docs: Update user.js (#38)
  * docs: add wechat little program solution doc (#36)

4.0.7 / 2018-03-02
==================

  * feat: Add declaration support (#35)

4.0.6 / 2018-01-16
==================

  * fix: when system event occured , should pass the right socket object (#34)

4.0.5 / 2018-01-15
==================

  * revert: https://github.com/eggjs/egg-socket.io/pull/32 (#33)

4.0.4 / 2018-01-12
==================

  * feat: update deps & License
  * fix: miss 'egg-core' dependency  may cause a error (#32)

4.0.3 / 2017-12-27
==================

  * fix: throw Error to native socket.io (#31)

4.0.2 / 2017-12-20
==================

  * fix: fix crash  when client close the socket. (#30)

4.0.1 / 2017-12-05
==================

  * fix: fix readme error (#29)

4.0.0 / 2017-12-04
==================

  * feat: support egg2 (#28)
  * fix: upgrade the example version (#27)

3.0.0 / 2017-10-24
==================

  * feat: [BREAKING_CHANGE] support class based async controllers.Use a single async function as module's default export won't be supported.(#26)
  * docs: fix typo (#25)
  * docs: redis config zh_CN (#24)
  * docs: redis config (#23)
  * docs: npm scripts --sticky (#22)
  * docs: README for async await usage (#21)

2.1.0 / 2017-08-11
==================

  * feat: add async support for controller. (#20)
  * fix: add the egg required keys config (#19)

2.0.2 / 2017-06-13
==================

  * deps: upgrade (#18)

2.0.1 / 2017-06-12
==================

  * fix: lock socket.io to v2.0.1 fix the error (#17)

2.0.0 / 2017-05-30
==================

  * deps: upgrade socket.io to 2 (#16)

1.2.2 / 2017-05-09
==================

  * fix:when a channel has error must throw the exception.#865 (#14)

1.2.1 / 2017-04-27
==================

  * fix:middleware sometimes not release(#803)
  * test:add ns test case (#12)

1.2.0 / 2017-04-13
==================

  * feat:add engine.io config support (#11)

1.1.0 / 2017-03-22
==================

  * feat:error log (#10)

1.0.2 / 2017-03-15
==================

  * doc:add cluster doc (#9)

1.0.1 / 2017-03-15
==================

  * doc:add nginx conf (#8)

1.0.0 / 2017-03-10
==================

  * doc: fix readme (#7)
  * test:fix windows test (#6)

1.0.0-beta.2 / 2017-03-07
==================

  * test:more test (#5)
  * chore:fix format
  * chore:fix example (#4)

1.0.0-beta.1 / 2017-03-03
==================

  * feat: add session support (#3)
  * doc:update readme (#2)

1.0.0-beta.0 / 2017-03-02
==================

  * feat:first-implement

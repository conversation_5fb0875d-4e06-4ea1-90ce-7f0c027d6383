{"name": "engine.io", "version": "3.6.2", "description": "The realtime engine behind Socket.IO. Provides the foundation of a bidirectional connection between client and server", "main": "lib/engine.io.js", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/socketio/engine.io", "contributors": [{"name": "<PERSON><PERSON>", "web": "https://github.com/EugenD<PERSON>ck"}, {"name": "<PERSON><PERSON><PERSON>", "web": "https://github.com/afshinm"}, {"name": "<PERSON>", "web": "https://github.com/cadorn"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"accepts": "~1.3.4", "base64id": "2.0.0", "cookie": "~0.4.1", "debug": "~4.1.0", "engine.io-parser": "~2.2.0", "ws": "~7.5.10"}, "devDependencies": {"babel-eslint": "^8.0.2", "babel-preset-es2015": "^6.24.0", "engine.io-client": "3.5.0", "eslint": "^4.5.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.1.1", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "expect.js": "^0.3.1", "mocha": "^4.0.1", "s": "0.1.1", "superagent": "^3.8.1", "uws": "~9.14.0"}, "scripts": {"lint": "eslint lib/ test/ *.js", "test": "npm run lint && mocha && EIO_WS_ENGINE=uws mocha"}, "repository": {"type": "git", "url": "**************:socketio/engine.io.git"}, "files": ["lib/"], "engines": {"node": ">=8.0.0"}}
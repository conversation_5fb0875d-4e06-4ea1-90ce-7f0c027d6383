{"config": {"session": {"maxAge": 86400000, "key": "EGG_SESS", "httpOnly": true, "encrypt": true, "logValue": true}, "security": {"domainWhiteList": [], "protocolWhiteList": [], "defaultMiddleware": "csrf,hsts,methodnoallow,noopen,nosniff,csp,xssProtection,xframe,dta", "csrf": {"enable": false, "type": "ctoken", "ignoreJSON": false, "useSession": false, "cookieName": "csrfToken", "sessionName": "csrfToken", "headerName": "x-csrf-token", "bodyName": "_csrf", "queryName": "_csrf", "rotateWhenInvalid": false, "supportedRequests": [{"path": {}, "methods": ["POST", "PATCH", "DELETE", "PUT", "CONNECT"]}], "refererWhiteList": [], "cookieOptions": {"signed": false}}, "xframe": {"enable": true, "value": "SAMEORIGIN"}, "hsts": {"enable": false, "maxAge": 31536000, "includeSubdomains": false}, "dta": {"enable": true}, "methodnoallow": {"enable": true}, "noopen": {"enable": true}, "nosniff": {"enable": true}, "referrerPolicy": {"enable": false, "value": "no-referrer-when-downgrade"}, "xssProtection": {"enable": true, "value": "1; mode=block"}, "csp": {"enable": false, "policy": {}}, "ssrf": {"ipBlackList": null, "ipExceptionList": null, "hostnameExceptionList": null, "checkAddress": null}, "_protocolWhiteListSet": "<Set>"}, "helper": {"shtml": {}}, "jsonp": {"limit": 50, "callback": ["_callback", "callback"], "csrf": false}, "onerror": {"errorPageUrl": "", "appErrorFilter": null, "templatePath": "E:\\Kiro\\my-egg-project\\node_modules\\egg-onerror\\lib\\onerror_page.mustache"}, "i18n": {"defaultLocale": "en_US", "dirs": [], "queryField": "locale", "cookieField": "locale", "cookieDomain": "", "cookieMaxAge": "1y"}, "watcher": {"type": "development", "eventSources": {"default": "E:\\Kiro\\my-egg-project\\node_modules\\egg-watcher\\lib\\event-sources\\default", "development": "E:\\Kiro\\my-egg-project\\node_modules\\egg-watcher\\lib\\event-sources\\development"}}, "customLogger": {"scheduleLogger": {"consoleLevel": "NONE", "file": "egg-schedule.log"}}, "schedule": {"directory": []}, "multipart": {"mode": "stream", "autoFields": false, "defaultCharset": "utf8", "defaultParamCharset": "utf8", "fieldNameSize": 100, "fieldSize": "100kb", "fields": 10, "fileSize": "10mb", "files": 10, "fileExtensions": [], "whitelist": null, "allowArrayField": false, "tmpdir": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\egg-multipart-tmp\\my-egg-project", "cleanSchedule": {"cron": "0 30 4 * * *", "disable": false}}, "development": {"watchDirs": [], "ignoreDirs": [], "fastReady": false, "reloadOnDebug": true, "overrideDefault": false, "overrideIgnore": false}, "logrotator": {"filesRotateByHour": null, "hourDelimiter": "-", "filesRotateBySize": null, "maxFileSize": 52428800, "maxFiles": 10, "rotateDuration": 60000, "maxDays": 31}, "static": {"prefix": "/public/", "dir": "E:\\Kiro\\my-egg-project\\app\\public", "dynamic": true, "preload": false, "buffer": false, "maxFiles": 1000}, "view": {"root": "E:\\Kiro\\my-egg-project\\app\\view", "cache": false, "defaultExtension": ".html", "defaultViewEngine": "", "mapping": {}}, "io": {"init": {"cors": {"origin": "*", "methods": ["GET", "POST"]}}, "namespace": {"/": {"connectionMiddleware": ["connection"], "packetMiddleware": []}}}, "env": "local", "name": "my-egg-project", "keys": "<String len: 32>", "cookies": {}, "proxy": false, "maxIpsCount": 0, "maxProxyCount": 0, "protocolHeaders": "x-forwarded-proto", "ipHeaders": "x-forwarded-for", "hostHeaders": "", "pkg": {"name": "my-egg-project", "version": "1.0.0", "description": "", "private": true, "egg": {"declarations": true}, "dependencies": {"egg": "^3.17.5", "egg-scripts": "2", "egg-socket.io": "^4.1.6"}, "devDependencies": {"egg-bin": "6", "egg-mock": "5", "eslint": "8", "eslint-config-egg": "13"}, "engines": {"node": ">=18.0.0"}, "scripts": {"start": "egg-scripts start --daemon --title=egg-server-my-egg-project", "stop": "egg-scripts stop --title=egg-server-my-egg-project", "dev": "egg-bin dev", "test": "npm run lint -- --fix && npm run test:local", "test:local": "egg-bin test", "cov": "egg-bin cov", "lint": "eslint .", "ci": "npm run lint && npm run cov"}, "repository": {"type": "git", "url": ""}, "author": "", "license": "MIT"}, "baseDir": "E:\\Kiro\\my-egg-project", "HOME": "C:\\Users\\<USER>", "rundir": "E:\\Kiro\\my-egg-project\\run", "dump": {"ignore": "<Set>", "timing": {"slowBootActionMinDuration": 5000}}, "confusedConfigurations": {"bodyparser": "<PERSON><PERSON><PERSON><PERSON>", "notFound": "notfound", "sitefile": "siteFile", "middlewares": "middleware", "httpClient": "httpclient"}, "notfound": {"pageUrl": ""}, "siteFile": {"/favicon.ico": "<Buffer len: 6463>", "cacheControl": "public, max-age=2592000"}, "bodyParser": {"enable": true, "encoding": "utf8", "formLimit": "1mb", "jsonLimit": "1mb", "textLimit": "1mb", "strict": true, "queryString": {"arrayLimit": 100, "depth": 5, "parameterLimit": 1000}, "onerror": "<Function onerror>"}, "logger": {"dir": "E:\\Kiro\\my-egg-project\\logs\\my-egg-project", "encoding": "utf8", "env": "local", "level": "INFO", "consoleLevel": "INFO", "disableConsoleAfterReady": false, "outputJSON": false, "buffer": true, "appLogName": "my-egg-project-web.log", "coreLogName": "egg-web.log", "agentLogName": "egg-agent.log", "errorLogName": "common-error.log", "coreLogger": {"consoleLevel": "WARN"}, "allowDebugAtProd": false, "enablePerformanceTimer": false, "enableFastContextLogger": false, "type": "agent", "localStorage": "<AsyncLocalStorage>"}, "httpclient": {"enableDNSCache": false, "dnsCacheLookupInterval": 10000, "dnsCacheMaxLength": 1000, "request": {"timeout": 5000}, "httpAgent": {"keepAlive": true, "freeSocketTimeout": 4000, "maxSockets": 9007199254740991, "maxFreeSockets": 256}, "httpsAgent": {"keepAlive": true, "freeSocketTimeout": 4000, "maxSockets": 9007199254740991, "maxFreeSockets": 256}, "useHttpClientNext": false}, "meta": {"enable": true, "logging": false}, "coreMiddleware": ["meta", "siteFile", "notfound", "<PERSON><PERSON><PERSON><PERSON>", "override<PERSON><PERSON><PERSON>"], "workerStartTimeout": 600000, "serverTimeout": null, "cluster": {"listen": {"path": "", "port": 7001, "hostname": ""}}, "clusterClient": {"maxWaitTime": 60000, "responseTimeout": 60000}, "onClientError": null, "middleware": [], "cors": {"origin": "*", "allowMethods": "GET,HEAD,PUT,POST,DELETE,PATCH"}, "coreMiddlewares": "~config~coreMiddleware", "appMiddlewares": "~config~middleware", "appMiddleware": "~config~middleware"}, "plugins": {"onerror": {"enable": true, "package": "egg-onerror", "name": "onerror", "dependencies": [], "optionalDependencies": ["jsonp"], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-onerror", "version": "2.4.0"}, "session": {"enable": true, "package": "egg-session", "name": "session", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-session", "version": "3.3.0", "dependents": ["io"]}, "i18n": {"enable": true, "package": "egg-i18n", "name": "i18n", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-i18n", "version": "2.1.1"}, "watcher": {"enable": true, "package": "egg-watcher", "name": "watcher", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-watcher", "version": "3.1.1", "dependents": ["development"]}, "multipart": {"enable": true, "package": "egg-multipart", "name": "multipart", "dependencies": [], "optionalDependencies": ["schedule"], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-multipart", "version": "3.5.0"}, "security": {"enable": true, "package": "egg-security", "name": "security", "dependencies": [], "optionalDependencies": ["session"], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-security", "version": "3.7.0"}, "development": {"enable": true, "package": "egg-development", "name": "development", "dependencies": ["watcher"], "optionalDependencies": [], "env": ["local"], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-development", "version": "3.0.2"}, "logrotator": {"enable": true, "package": "egg-logrotator", "name": "logrotator", "dependencies": ["schedule"], "optionalDependencies": [], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-logrotator", "version": "3.2.0"}, "schedule": {"enable": true, "package": "egg-schedule", "name": "schedule", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-schedule", "version": "4.0.1", "dependents": ["logrotator"]}, "static": {"enable": true, "package": "egg-static", "name": "static", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-static", "version": "2.3.1"}, "jsonp": {"enable": true, "package": "egg-jsonp", "name": "jsonp", "dependencies": [], "optionalDependencies": ["security"], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-jsonp", "version": "2.0.0"}, "view": {"enable": true, "package": "egg-view", "name": "view", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\Kiro\\my-egg-project\\node_modules\\egg\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-view", "version": "2.1.4"}, "io": {"enable": true, "package": "egg-socket.io", "name": "io", "dependencies": ["session"], "optionalDependencies": [], "env": [], "from": "E:\\Kiro\\my-egg-project\\config\\plugin.js", "path": "E:\\Kiro\\my-egg-project\\node_modules\\egg-socket.io", "version": "4.1.6"}}, "appInfo": {"name": "my-egg-project", "baseDir": "E:\\Kiro\\my-egg-project", "env": "local", "scope": "", "HOME": "C:\\Users\\<USER>", "pkg": {"name": "my-egg-project", "version": "1.0.0", "description": "", "private": true, "egg": {"declarations": true}, "dependencies": {"egg": "^3.17.5", "egg-scripts": "2", "egg-socket.io": "^4.1.6"}, "devDependencies": {"egg-bin": "6", "egg-mock": "5", "eslint": "8", "eslint-config-egg": "13"}, "engines": {"node": ">=18.0.0"}, "scripts": {"start": "egg-scripts start --daemon --title=egg-server-my-egg-project", "stop": "egg-scripts stop --title=egg-server-my-egg-project", "dev": "egg-bin dev", "test": "npm run lint -- --fix && npm run test:local", "test:local": "egg-bin test", "cov": "egg-bin cov", "lint": "eslint .", "ci": "npm run lint && npm run cov"}, "repository": {"type": "git", "url": ""}, "author": "", "license": "MIT"}, "root": "E:\\Kiro\\my-egg-project"}}
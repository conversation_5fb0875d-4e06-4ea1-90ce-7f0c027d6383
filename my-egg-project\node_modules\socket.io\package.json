{"name": "socket.io", "version": "2.5.1", "description": "node.js realtime framework server", "keywords": ["realtime", "framework", "websocket", "tcp", "events", "socket", "io"], "main": "./lib/index", "files": ["lib/"], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/socketio/socket.io"}, "scripts": {"test": "nyc mocha --reporter spec --slow 200 --bail --timeout 10000 test/socket.io.js"}, "dependencies": {"debug": "~4.1.0", "engine.io": "~3.6.0", "has-binary2": "~1.0.2", "socket.io-adapter": "~1.1.0", "socket.io-client": "2.5.0", "socket.io-parser": "~3.4.0"}, "devDependencies": {"expect.js": "0.3.1", "mocha": "^3.5.3", "nyc": "^11.2.1", "superagent": "^3.8.2", "supertest": "^3.0.0"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}]}
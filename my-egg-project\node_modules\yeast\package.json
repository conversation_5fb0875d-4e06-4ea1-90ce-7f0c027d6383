{"name": "yeast", "version": "0.1.2", "description": "Tiny but linear growing unique id generator", "main": "index.js", "scripts": {"100%": "istanbul check-coverage --statements 100 --functions 100 --lines 100 --branches 100", "test-node": "istanbul cover _mocha --report lcovonly -- test.js", "coverage": "istanbul cover _mocha -- test.js", "test-browser": "zuul -- test.js", "watch": "mocha --watch test.js", "test": "mocha test.js"}, "repository": {"type": "git", "url": "https://github.com/unshiftio/yeast.git"}, "keywords": ["yeast", "id", "generator", "unique"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/unshiftio/yeast/issues"}, "homepage": "https://github.com/unshiftio/yeast", "devDependencies": {"assume": "1.3.x", "istanbul": "0.3.x", "mocha": "2.3.x", "pre-commit": "1.1.x", "zuul": "3.4.x"}}